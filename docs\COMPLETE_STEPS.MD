Okay, drawing upon the details provided in the sources outlining the "Shell Script for Ani-Cli Anime Playback," let's break down its functionality and map out how you could replicate each step in Golang, including code examples and methods.

Replicating a complex Bash script like this in Golang involves translating shell commands, pipelines, and logic into Go's standard library functions and potentially some external Go packages (which we will note as outside the provided sources).

Here is a detailed breakdown of the script's steps and how to approach their implementation in Golang:

**1. Project Structure and Entry Point**

The Bash script starts with `#!/bin/sh` and execution flows from top-level variables and `while` loops down to function calls.

*   **Bash Script Approach**: Linear execution flow, functions defined and called as needed.
*   **Golang Replication**: A standard Go application structure using `package main` and a `func main()`. The `main` function will handle configuration loading, argument parsing, and orchestrating the main logic based on user input or options.
*   **Code Example**:

```go
package main

import (
	"fmt"
	"log"
	"os"
	// ... other imports
)

// Configuration struct (will be populated later)
type Config struct {
	Version string
	Player  string
	Quality string
	Mode    string
	// ... other config fields
}

var appConfig Config // Global config or pass around

func main() {
	log.SetFlags(0) // Basic logging setup

	// 1. Load configuration (env vars, defaults)
	loadConfig() // Implement this function

	// 2. Parse command-line arguments
	parseArgs() // Implement this function

	// 3. Check dependencies
	checkDependencies() // Implement this function

	// 4. Execute main logic based on parsed arguments (e.g., search, history, update)
	runMainLogic() // Implement this function
}

// Implement these functions later
func loadConfig() { /* ... */ }
func parseArgs() { /* ... */ }
func checkDependencies() { /* ... */ }
func runMainLogic() { /* ... */ }

func die(message string, args ...interface{}) {
	fmt.Fprintf(os.Stderr, "\033[2K\r\033[1;31m%s\033[0m\n", fmt.Sprintf(message, args...))
	os.Exit(1)
}

// Note: The `die` function directly replicates the Bash script's error output format.
```

**2. Configuration Management**

The script reads configuration from environment variables (e.g., `ANI_CLI_MODE`, `ANI_CLI_PLAYER`) and sets default values. Command-line arguments override these.

*   **Bash Script Approach**: Direct environment variable access (`${ANI_CLI_MODE:-sub}`) and manual parsing of `$@` in a `while case` loop.
*   **Golang Replication**: Use the `os` package to read environment variables. Use the `flag` package (standard library) or a third-party library like `cobra` (outside source) for robust command-line argument parsing. A struct can hold all configuration settings.
*   **Code Example**:

```go
// Inside Config struct:
type Config struct {
	Version string
	Player string
	Quality string
	Mode string // "sub" or "dub"
	DownloadDir string //
	HistDir string //
	LogEpisode bool //
	NoDetach bool //
	ExitAfterPlay bool //
	UseExternalMenu bool // (rofi or fzf toggle)
	ExternalMenuArgs string // (for rofi -normal-window)
	SkipIntro bool // (use ani-skip)
	SkipTitle string // (ani-skip query)
	SearchMode string // "scrape", "history", "nextep"
	Query string // Search query
	EpisodeNumber string // Specified episode/range
	SelectedQuality string // Specified quality
	SelectedIndex string // Specified search result index
	// ... other fields derived from Bash script variables
}

func loadConfig() {
	appConfig.Version = "4.10.1" // From Bash script
	appConfig.Mode = os.Getenv("ANI_CLI_MODE")
	if appConfig.Mode == "" {
		appConfig.Mode = "sub"
	}
	appConfig.DownloadDir = os.Getenv("ANI_CLI_DOWNLOAD_DIR")
	if appConfig.DownloadDir == "" {
		appConfig.DownloadDir = "." // Current directory
	}
	// ... load other env vars

	// Determine default player based on OS, similar to Bash case statement
	osName := getOSInfo() // Helper function to get OS info
	defaultPlayer := "mpv"
	// Add OS-specific logic based on script
	switch {
	case osName == "Darwin":
		defaultPlayer = whereIINA() // Implement this
	case osName == "Android": // Termux detection
		defaultPlayer = "android_mpv"
	case osName == "Windows": // MINGW/WSL2 detection
		defaultPlayer = "mpv.exe"
	case osName == "iOS": // iSH detection
		defaultPlayer = "iSH"
	default:
		defaultPlayer = whereMPV() // Implement this
	}
	appConfig.Player = os.Getenv("ANI_CLI_PLAYER")
	if appConfig.Player == "" {
		appConfig.Player = defaultPlayer
	}

	// History directory setup
	histDirEnv := os.Getenv("ANI_CLI_HIST_DIR")
	if histDirEnv != "" {
		appConfig.HistDir = histDirEnv
	} else {
		xdgStateHome := os.Getenv("XDG_STATE_HOME")
		if xdgStateHome != "" {
			appConfig.HistDir = filepath.Join(xdgStateHome, "ani-cli")
		} else {
			homeDir, _ := os.UserHomeDir() // os/user might be needed, or just $HOME
			appConfig.HistDir = filepath.Join(homeDir, ".local", "state", "ani-cli")
		}
	}
	// Ensure history directory exists
	if _, err := os.Stat(appConfig.HistDir); os.IsNotExist(err) {
		os.MkdirAll(appConfig.HistDir, 0755)
	}
	// Ensure history file exists
	histFile := filepath.Join(appConfig.HistDir, "ani-hsts")
	if _, err := os.Stat(histFile); os.IsNotExist(err) {
		os.Create(histFile)
	}
	// Store histFile path in config if needed later
	// appConfig.HistFilePath = histFile
}

func parseArgs() {
	// Use flag package or similar to define flags like -v, --vlc, -q, --quality, etc.
	// Example using standard flag package (simplified):
	vlcFlag := flag.Bool("vlc", false, "Use VLC player")
	qualityFlag := flag.String("quality", "", "Specify video quality")
	episodeFlag := flag.String("episode", "", "Specify episode number or range")
	rangeFlag := flag.String("r", "", "Alias for --episode") // Alias as in source
	downloadFlag := flag.Bool("download", false, "Download instead of play")
	// ... define other flags based on source

	flag.Parse()

	// Process flags and update appConfig, overriding env vars
	if *vlcFlag {
		// Set VLC player based on OS, similar to Bash script's case logic
		osName := getOSInfo()
		switch {
		case osName == "Android":
			appConfig.Player = "android_vlc"
		case osName == "Windows":
			appConfig.Player = "vlc.exe"
		case osName == "iOS":
			appConfig.Player = "iSH" // Note: iSH player usage is different
		default:
			appConfig.Player = "vlc"
		}
	}
	if *qualityFlag != "" {
		appConfig.SelectedQuality = *qualityFlag
	} else {
		appConfig.SelectedQuality = os.Getenv("ANI_CLI_QUALITY")
		if appConfig.SelectedQuality == "" {
			appConfig.SelectedQuality = "best" // Default quality
		}
	}
	if *episodeFlag != "" {
		appConfig.EpisodeNumber = *episodeFlag
	} else if *rangeFlag != "" {
		appConfig.EpisodeNumber = *rangeFlag // Alias handling
	}
	if *downloadFlag {
		appConfig.Player = "download"
		// Add iSH_DownFix logic if applicable
	}

	// Handle positional arguments (the query)
	// The Bash script concatenates all non-flag args into the query
	if len(flag.Args()) > 0 {
		appConfig.Query = strings.Join(flag.Args(), " ") // Bash script replaces spaces with + later
	}

	// Handle specific flag logic that changes search mode
	// Example: --continue, --logview, --nextep-countdown, --update, --delete
	// Check for these flags after parsing and set appConfig.SearchMode
	// If no such flag, default search mode is "scrape"
	appConfig.SearchMode = os.Getenv("ANI_CLI_DEFAULT_SOURCE")
	if appConfig.SearchMode == "" {
		appConfig.SearchMode = "scrape" // Default search mode
	}
	// Example: if --continue flag is present, set appConfig.SearchMode = "history"
}

// Helper function to get simplified OS name (approximate Bash `uname`)
func getOSInfo() string {
	// Use runtime.GOOS and runtime.GOARCH
	osName := runtime.GOOS
	// Add more specific checks if needed to match Bash script's detail
	// e.g., check for termux env vars on Android GOOS
	return osName
}

// Implement these helper functions using exec.LookPath
func whereIINA() string { /* ... */ return "iina" } // Check if IINA path exists first
func whereMPV() string { /* ... */ return "mpv" } // Check flatpak first
```
*Note: The `flag` package in Go parses flags differently from the Bash script. A more robust approach mirroring the Bash script's `while case` loop might involve iterating through `os.Args` manually or using a library like `cobra` (outside sources) which handles flags and positional arguments more flexibly.*

**3. Dependency Checking**

The script uses `command -v` to check if necessary executables exist.

*   **Bash Script Approach**: `command -v "${dep%% *}" >/dev/null || die "Program \"${dep%% *}\" not found..."`.
*   **Golang Replication**: Use `exec.LookPath` to search for executables in the system's PATH.
*   **Code Example**:

```go
func checkDependencies() {
	// List of required dependencies
	basicDeps := []string{"curl", "sed", "grep", "fzf"}
	// Bash script ignores errors for these basic checks initially
	// In Go, we might just check them and exit if crucial ones are missing.
	for _, dep := range basicDeps {
		_, err := exec.LookPath(dep)
		if err != nil {
			log.Printf("Warning: Dependency '%s' not found.", dep) // Or die for critical ones
		}
	}

	// Conditional dependencies
	if appConfig.SkipIntro {
		_, err := exec.LookPath("ani-skip")
		if err != nil {
			die("Program 'ani-skip' not found. Please install it.")
		}
	}

	// Player/Downloader dependencies
	switch appConfig.Player {
	case "debug":
		// No dependencies for debug
	case "download":
		// Check for ffmpeg and aria2c
		_, err := exec.LookPath("ffmpeg")
		if err != nil {
			die("Program 'ffmpeg' not found. Please install it.")
		}
		_, err = exec.LookPath("aria2c")
		if err != nil {
			die("Program 'aria2c' not found. Please install it.")
		}
	case "android_mpv", "android_vlc", "iSH":
		// Bash script disables checks for these platforms
		fmt.Println("\033[2K\rChecking of players on Android/iOS is disabled")
	case "flatpak_mpv":
		// Handled in whereMPV
	default:
		// Check for the configured player executable
		_, err := exec.LookPath(appConfig.Player)
		if err != nil {
			die("Program '%s' not found. Please install it.", appConfig.Player)
		}
	}
	fmt.Println("\033[2K\r\033[1;34mChecking dependencies... OK\033[0m") // Replicate success message format
}
```

**4. UI Interaction (Launcher and Nth)**

The script uses `fzf` or `rofi` for interactive selection from lists (search results, episodes, quality). The `nth` function handles single or multi-line input and formats it for the launcher, then processes the launcher's output.

*   **Bash Script Approach**: Execute `fzf` or `rofi` via shell, pipe input to their stdin, capture stdout. `nth` uses `cut`, `grep`, `head`, `tail`, `sed` to process input and output strings.
*   **Golang Replication**: Use `exec.Command` to run `fzf` or `rofi`. Pipe input data to the command's `Stdin` and capture output from `Stdout`. Replicate the `nth` logic using Go string manipulation and regex.
*   **Code Example**:

```go
// Replicate nth functionality in Go
func selectFromList(prompt string, list string, multi bool) (string, error) {
	if list == "" {
		return "", nil // Or return an error indicating no input
	}

	// Prepare launcher command arguments
	launcherArgs := []string{}
	if !appConfig.UseExternalMenu { // Using fzf
		launcherArgs = append(launcherArgs, "--reverse", "--cycle", "--prompt", prompt)
		if multi {
			launcherArgs = append(launcherArgs, appConfig.MultiSelectionFlag) // multi_selection_flag logic
		}
	} else { // Using rofi
		launcherArgs = append(launcherArgs, appConfig.ExternalMenuArgs, "-sort", "-dmenu", "-i", "-width", "1500", "-p", prompt)
		if multi {
			launcherArgs = append(launcherArgs, appConfig.MultiSelectionFlag) // multi_selection_flag logic
		}
	}

	var cmd *exec.Cmd
	if !appConfig.UseExternalMenu {
		cmd = exec.Command("fzf", launcherArgs...)
	} else {
		cmd = exec.Command("rofi", launcherArgs...)
	}

	// Pipe input list to stdin of the command
	cmd.Stdin = strings.NewReader(list)

	// Capture stdout
	outputBytes, err := cmd.Output()
	if err != nil {
		// fzf/rofi exiting non-zero often means user cancelled
		if exitErr, ok := err.(*exec.ExitError); ok {
			// Consider if specific exit codes mean cancellation vs actual error
			log.Printf("Launcher command exited with status %d", exitErr.ExitCode())
			return "", fmt.Errorf("selection cancelled or failed")
		}
		return "", fmt.Errorf("failed to run launcher: %w", err)
	}

	selectedLine := strings.TrimSpace(string(outputBytes))
	if selectedLine == "" {
		return "", fmt.Errorf("selection cancelled or empty") // Based on Bash exit 1 if line is empty
	}

	// Replicate the nth logic to extract necessary parts from the selected line
	// The Bash script's nth is complex, extracting fields based on column numbers or ranges.
	// This Go function would need to be generic or tailored based on the calling context
	// (e.g., search results vs episode list).
	// Let's illustrate processing a line like "ID Title (Episodes)" from search_anime output
	// Or "EP_NUM Title - episode EP_NUM" from history
	// Or "Quality > URL" from get_links

	// Example: Extracting ID from search result line "ID Title (Episodes)"
	// This requires parsing the selectedLine based on its expected format.
	// In Bash: `cut -f1`
	fields := strings.Fields(selectedLine)
	if len(fields) > 0 {
		return fields, nil // Return the first field (ID for search, EP_NUM for history/episodes)
	}


	// Note: A full replication of the Bash nth's filtering/range logic would require more complex parsing here,
	// potentially involving regex or custom state similar to the sed logic in the script.
	// The Bash script passes stdin to nth and nth does the filtering and selection.
	// A better Go translation might pass the structured *data* (e.g., list of Anime structs, list of Episode structs)
	// to a selection function that formats it for fzf/rofi input, then parses the output.

	return selectedLine, nil // Return the raw selected line for now
}

// Replicate the launcher function logic
// This function would format the input list and call selectFromList
func launcher(prompt string, list string, multi bool) (string, error) {
    // The Bash launcher directly calls fzf/rofi based on use_external_menu
    // and handles the single argument case `set -- "+m" "$2"`
    // A Go version might encapsulate this logic.
    // For simplicity, we can make `selectFromList` handle the fzf/rofi choice.

    // Replicate the `[ "$use_external_menu" = "0" ] && [ -z "$1" ] && set -- "+m" "$2"` logic
    // This seems to set a default multi flag if no flag is given and using fzf.
    // This can be handled when determining the `multi` argument for `selectFromList`.

    return selectFromList(prompt, list, multi)
}
```
*Note: The `nth` function in the Bash script is highly context-dependent based on the input format (tab-separated for history, space-separated for search results, `>` separated for links). A direct translation would require a flexible parsing mechanism in `selectFromList` or separate functions for different selection types.*

**5. API Interaction Core**

The script interacts with `api.allanime.day` using `curl` and GraphQL queries.

*   **Bash Script Approach**: `curl -e "$allanime_refr" -s -G "${allanime_api}/api" --data-urlencode "variables=..." --data-urlencode "query=..." -A "$agent"`. This uses a GET request with query parameters encoded in the URL, including the GraphQL query and variables. It sets a Referer and User-Agent header.
*   **Golang Replication**: Use the `net/http` package to make GET requests. Use `net/url` to build the query parameters correctly, including URL-encoding the GraphQL query and variables JSON. Set necessary headers. Use `encoding/json` to unmarshal the JSON response.
*   **Code Example**:

```go
// API Constants
const allanimeAPI = "https://api.allanime.day/api"
const allanimeReferer = "https://allmanga.to"
const userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"

// Generic function to make GraphQL API requests
func makeGraphQLRequest(query string, variables map[string]interface{}) ([]byte, error) {
	// Encode variables and query as JSON strings
	varsJSON, err := json.Marshal(variables)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal variables: %w", err)
	}

	// Build URL with data-urlencode parameters
	baseURL, err := url.Parse(allanimeAPI)
	if err != nil {
		return nil, fmt.Errorf("invalid API URL: %w", err)
	}
	params := url.Values{}
	params.Add("query", query)
	params.Add("variables", string(varsJSON))
	baseURL.RawQuery = params.Encode() // This does the data-urlencode equivalent for GET

	req, err := http.NewRequest("GET", baseURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Referer", allanimeReferer)
	req.Header.Set("User-Agent", userAgent)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("API request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read API response body: %w", err)
	}

	return bodyBytes, nil
}
```

**6. Searching Anime (`search_anime`)**

The script searches using a specific GraphQL query and parses the result to list anime.

*   **Bash Script Approach**: Calls `curl` with the search GraphQL query and variables. Uses `sed` to split lines and extract `_id`, `name`, and available episodes for the configured `mode` (sub/dub). Formats output for `nth`.
*   **Golang Replication**: Call `makeGraphQLRequest` with the search query and variables. Define Go structs to match the expected JSON response structure. Unmarshal the JSON. Format the results into a string list for the UI selection function.
*   **Code Example**:

```go
// Define structs matching the expected API response structure (partial)
type SearchResponse struct {
	Data struct {
		Shows struct {
			Edges []struct {
				ID string `json:"_id"`
				Name string `json:"name"`
				AvailableEpisodes struct {
					Sub int `json:"sub"` // Assume int, check API response
					Dub int `json:"dub"`
				} `json:"availableEpisodes"`
				Typename string `json:"__typename"` // Source has this field
			} `json:"edges"`
		} `json:"shows"`
	} `json:"data"`
}

func searchAnime(query string) ([]string, error) {
	// GraphQL search query from source
	searchGQL := `query( $search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType ) { shows( search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin ) { edges { _id name availableEpisodes __typename } }}`

	// Variables for the query
	variables := map[string]interface{}{
		"search": map[string]interface{}{
			"allowAdult": false, // Hardcoded in script
			"allowUnknown": false, // Hardcoded in script
			"query": query,
		},
		"limit": 40, // Hardcoded in script
		"page": 1, // Hardcoded in script
		"translationType": appConfig.Mode, // Use configured mode
		"countryOrigin": "ALL", // Hardcoded in script
	}

	bodyBytes, err := makeGraphQLRequest(searchGQL, variables)
	if err != nil {
		return nil, fmt.Errorf("search API request failed: %w", err)
	}

	// Unmarshal the JSON response
	var resp SearchResponse
	err = json.Unmarshal(bodyBytes, &resp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse search response JSON: %w", err)
	}

	// Format results for UI selection, similar to Bash script's sed output
	// Bash: `ID Name (Episodes)`
	var results []string
	for _, edge := range resp.Data.Shows.Edges {
		if edge.Typename == "Show" { // Filter out non-Show types if necessary
			episodeCount := 0
			if appConfig.Mode == "sub" {
				episodeCount = edge.AvailableEpisodes.Sub
			} else if appConfig.Mode == "dub" {
				episodeCount = edge.AvailableEpisodes.Dub
			}
			// Replicate the output format for nth/launcher
			results = append(results, fmt.Sprintf("%s\t%s (%d episodes)", edge.ID, edge.Name, episodeCount))
		}
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("no results found!") // Replicate Bash error message
	}

	return results, nil
}
```

**7. Listing Episodes (`episodes_list`)**

Fetches the list of episode numbers for a specific anime ID.

*   **Bash Script Approach**: Calls `curl` with the episode list GraphQL query. Uses `sed` to extract the comma-separated list of episode numbers for the configured `mode` and formats it into a newline-separated list.
*   **Golang Replication**: Call `makeGraphQLRequest`. Define a Go struct for the response. Unmarshal JSON. Extract the episode list based on the mode. Format into a newline-separated string for the UI selection function.
*   **Code Example**:

```go
// Struct for episode list response (partial)
type EpisodesListResponse struct {
	Data struct {
		Show struct {
			ID string `json:"_id"`
			AvailableEpisodesDetail struct {
				Sub []string `json:"sub"` // Assume episode numbers are strings, check API
				Dub []string `json:"dub"`
				// ... other modes
			} `json:"availableEpisodesDetail"`
		} `json:"show"`
	} `json:"data"`
}

func listEpisodes(showID string) (string, error) {
	// GraphQL episode list query from source
	episodesListGQL := `query ($showId: String!) { show( _id: $showId ) { _id availableEpisodesDetail }}`

	// Variables for the query
	variables := map[string]interface{}{
		"showId": showID,
	}

	bodyBytes, err := makeGraphQLRequest(episodesListGQL, variables)
	if err != nil {
		return "", fmt.Errorf("episode list API request failed: %w", err)
	}

	// Unmarshal the JSON response
	var resp EpisodesListResponse
	err = json.Unmarshal(bodyBytes, &resp)
	if err != nil {
		return "", fmt.Errorf("failed to parse episode list response JSON: %w", err)
	}

	// Extract episode list based on mode
	var episodes []string
	if appConfig.Mode == "sub" && resp.Data.Show.AvailableEpisodesDetail.Sub != nil {
		episodes = resp.Data.Show.AvailableEpisodesDetail.Sub
	} else if appConfig.Mode == "dub" && resp.Data.Show.AvailableEpisodesDetail.Dub != nil {
		episodes = resp.Data.Show.AvailableEpisodesDetail.Dub
	} else {
		return "", fmt.Errorf("no episodes found for mode '%s'", appConfig.Mode)
	}

	// Format into newline-separated string for nth/launcher
	// Bash sorts numerically, which is important for ranges and next/previous
	// We need to sort the episode strings numerically in Go
	sort.SliceStable(episodes, func(i, j int) bool {
		// Implement numeric comparison for episode strings like "1", "1.5", "10"
		// This can be tricky with floating point numbers.
		// Example simplistic float parse:
		epi, err := strconv.ParseFloat(episodes[i], 64)
		if err != nil { return episodes[i] < episodes[j] } // Fallback to string sort
		epj, err := strconv.ParseFloat(episodes[j], 64)
		if err != nil { return episodes[i] < episodes[j] }
		return epi < epj
	})

	return strings.Join(episodes, "\n"), nil // Return newline separated list
}
```

**8. Getting Episode URLs and Link Generation (`get_episode_url`, `generate_link`, `get_links`, `provider_init`)**

This is one of the most complex parts, involving multiple API calls, parsing different response structures, and intricate string manipulation/regex to extract video links.

*   **Bash Script Approach**: Fetches embed URLs using a GraphQL query. It then loops through potential providers (`1`, `2`, `3`, `4`), calls `generate_link` for each concurrently (`&`). `generate_link` calls `provider_init` to get provider details, then `get_links`. `get_links` makes `curl` requests to the provider URLs and uses extensive `sed` and `grep` pipelines to extract video/subtitle/referer links, handling different formats like M3U8. The `provider_init` includes a base conversion/decoding step. Results are saved to temporary files and then concatenated (`cat`).
*   **Golang Replication**: Call `makeGraphQLRequest` for embed URLs. Use Goroutines and `sync.WaitGroup` to process providers concurrently. Implement functions for `provider_init`, `generate_link`, and `get_links`. `get_links` will use `net/http` for requests and `regexp` and `strings` for complex parsing logic, translating the `sed`/`grep` pipelines. The base decoding in `provider_init` needs a dedicated Go function.
*   **Code Example (Illustrative - highly complex to fully translate `sed` pipelines)**:

```go
// Struct for episode embed response (partial)
type EpisodeEmbedResponse struct {
	Data struct {
		Episode struct {
			EpisodeString string `json:"episodeString"`
			SourceUrls []struct {
				SourceUrl string `json:"sourceUrl"` // Format: "--providerCode:..."
				SourceName string `json:"sourceName"`
				// ... other fields
			} `json:"sourceUrls"`
			// ... other fields
		} `json:"episode"`
	} `json:"data"`
}

// Struct to hold extracted link information
type VideoLink struct {
	Quality string
	URL     string
	Type    string // e.g., "mp4", "m3u8", "Yt"
	Referer string // for m3u8 or Yt
	Subtitle string // for m3u8
	Raw string // the original line from get_links output for select_quality
}

// Implement the complex hex decoding from provider_init
func decodeProviderID(encoded string) string {
	// This function needs to replicate the series of sed s/^XX$/Y/g commands
	// It looks like a custom base conversion or substitution cipher.
	// Example for a few mappings:
	replacements := map[string]string{
		"79": "A", "7a": "B", "08": "0", "09": "1", "17": "/", // ... add all mappings from source
	}
	decoded := ""
	// The Bash script splits every two characters, then applies replacements
	for i := 0; i < len(encoded); i += 2 {
		if i+2 <= len(encoded) {
			hexPair := encoded[i:i+2]
			if replacement, ok := replacements[hexPair]; ok {
				decoded += replacement
			} else {
				// Handle cases not in the map, or signal error
				decoded += hexPair // Keep original if not found? Or signal error?
			}
		}
	}
	// Final sed replacement from source: sed "s/\/clock/\/clock\.json/"
	decoded = strings.ReplaceAll(decoded, "/clock", "/clock.json")
	return decoded
}

// Replicate provider_init logic
func getProviderInfo(rawSourceUrl string) (providerName string, providerID string, ok bool) {
	// Bash script uses regex to match source name and extract encoded ID
	// Example patterns from source: "/Default :/p", "/Yt-mp4 :/p", "/S-mp4 :/p", "/Luf-Mp4 :/p"
	re := regexp.MustCompile(`--([^:]+):(.*)`) // Matches "--providerCode:encodedId" format
	matches := re.FindStringSubmatch(rawSourceUrl)
	if len(matches) < 3 {
		return "", "", false // Doesn't match expected format
	}
	providerCode := matches
	encodedID := matches

	switch providerCode { // Case based on provider code prefix
	case "Default": providerName = "wixmp"
	case "Yt-mp4": providerName = "youtube"
	case "S-mp4": providerName = "sharepoint"
	case "Luf-Mp4": providerName = "hianime"
	default:
		// Handle unknown providers or skip
		return "", "", false
	}

	providerID = decodeProviderID(encodedID) // Decode the extracted ID
	return providerName, providerID, true
}

// Replicate get_links logic (highly complex parsing)
// This function would take a providerID (decoded URL) and return a list of VideoLink structs
func getLinks(providerID string) ([]VideoLink, error) {
	// This function needs to replicate the curl calls and extensive sed/grep parsing
	// Example for M3U8 (case *master.m3u8*):
	if strings.Contains(providerID, "master.m3u8") {
		// Fetch m3u8 content
		m3u8Referer := "" // Need to extract this from initial response via sed - this is circular
		// The Bash script saves m3u8_refr to a cache file to be read later
		// In Go, we'd need to store this directly.

		// Simplified - assume m3u8Referer is passed or found elsewhere
		req, err := http.NewRequest("GET", providerID, nil)
		if err != nil { return nil, err }
		req.Header.Set("Referer", m3u8Referer) // Use extracted referer
		req.Header.Set("User-Agent", userAgent)

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil { return nil, err }
		defer resp.Body.Close()

		m3u8Bytes, err := ioutil.ReadAll(resp.Body)
		if err != nil { return nil, err }
		m3u8Content := string(m3u8Bytes)

		// Parse m3u8 content - needs to replicate sed logic
		// Bash sed: `sed 's|^#EXT-X-STREAM.*x||g; s|,.*|p|g; /^#/d; $!N; s|\n| >|;/EXT-X-I-FRAME/d'`
		// Then `sed "s|>|cc>${relative_link}|g"`
		var links []VideoLink
		// This parsing is complex and error-prone with regex. Might need a dedicated M3U8 parsing library (outside source)
		// or very careful regex construction.
		// Example (naive regex, likely incorrect):
		reStream := regexp.MustCompile(`#EXT-X-STREAM-INF:.*?RESOLUTION=\d+x(\d+).*?\n(.*?$)`) // Capture height and URL
		matches := reStream.FindAllStringSubmatch(m3u8Content, -1)
		relativeLink := providerID[:strings.LastIndex(providerID, "/")+1] // Replicate `sed 's|[^/]*$||'`
		for _, match := range matches {
			quality := match // Height
			url := match
			// Replicate `sed "s|>|cc>${relative_link}|g"` format
			rawLine := fmt.Sprintf("%s >cc>%s%s", quality, relativeLink, url)
			links = append(links, VideoLink{
				Quality: quality,
				URL:     relativeLink + url, // Construct full URL
				Type:    "m3u8",
				Raw: rawLine,
				// Referer and Subtitle would also need to be extracted here or elsewhere
				Referer: m3u8Referer, // Need to get this correctly
				// Subtitle URL extraction uses a different regex on the *initial* response body
			})
		}
		// Add subtitle link if found (extracted separately)
		// Add referer link if found (extracted separately)

		return links, nil // Return parsed links
	}

	// Handle other cases (wixmp, youtube, sharepoint)
	// Each case requires replicating its specific sed/grep logic.
	// Example for *repackager.wixmp.com*:
	if strings.Contains(providerID, "repackager.wixmp.com") {
		// This case uses `sed -nE 's|.*/,([^/]*),/mp4.*|\1|p' | sed 's|,|\<br>|g'` on the episode_link string
		// and then `printf "%s >%s\n" "$j" "$extract_link" | sed "s|,[^/]*|${j}|g"`
		// This suggests complex extraction of quality strings from the URL path.
		// Need to parse the providerID string (which is the full URL from the initial response after cut -d'>' -f2)
		// ... replicate this complex parsing using Go regex and string split/join.
	}

	// Default case - direct link?
	// `[ -n "$episode_link" ] && printf "%s\n" "$episode_link" ;;` implies if episode_link exists and didn't match prior cases, print it.
	// This might represent cases where the API directly provides an MP4 link.
	// Need to structure the output consistently, perhaps guessing quality or using a generic label.
	links = append(links, VideoLink{
		Quality: "unknown", // Or try to guess from URL
		URL: providerID,
		Type: "direct",
		Raw: fmt.Sprintf("unknown >%s", providerID),
		// No referer/subtitle flags usually needed for direct links
	})

	return links, nil // Return the link(s)
}


// Orchestrates getting embed URLs, generating links, and selecting quality
func getEpisodeURL(showID string, epNum string) error {
	// GraphQL query for episode embed URLs
	episodeEmbedGQL := `query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}`

	variables := map[string]interface{}{
		"showId": showID,
		"translationType": appConfig.Mode,
		"episodeString": epNum,
	}

	bodyBytes, err := makeGraphQLRequest(episodeEmbedGQL, variables)
	if err != nil {
		return fmt.Errorf("episode embed API request failed: %w", err)
	}

	// Unmarshal response
	var resp EpisodeEmbedResponse
	err = json.Unmarshal(bodyBytes, &resp)
	if err != nil {
		return fmt.Errorf("failed to parse episode embed response JSON: %w", err)
	}

	if resp.Data.Episode.SourceUrls == nil || len(resp.Data.Episode.SourceUrls) == 0 {
		// Check if episode number is valid based on episode list
		// Need to re-fetch or pass the full ep_list here to check.
		// For now, assume it's an issue with sources if embed URLs are empty.
		// Bash script checks against ep_list and gives different errors
		epListStr, _ := listEpisodes(showID) // Re-fetch or pass
		epList := strings.Split(strings.TrimSpace(epListStr), "\n")
		epReleased := false
		for _, e := range epList {
			if e == epNum {
				epReleased = true
				break
			}
		}

		if epReleased {
			return fmt.Errorf("episode is released, but no valid sources!")
		} else {
			return fmt.Errorf("episode not released!")
		}
	}

	// Process SourceUrls to generate provider links
	// The Bash script gets all sourceUrls first, then pipes them to provider_init/generate_link
	// In Go, we can iterate through SourceUrls and call generate_link.

	// Store all generated links from all providers
	allLinks := []VideoLink{}
	var wg sync.WaitGroup
	linksChan := make(chan []VideoLink)

	// Filter unique provider URLs (based on --providerCode:encodedId) from sourceUrls
	// Bash script seems to generate links based on hardcoded provider IDs "1 2 3 4" which map to regex in provider_init
	// This implies it's looking for specific sourceNames like "Default", "Yt-mp4", etc.
	// Let's iterate through the sourceUrls from the API and try to generate links for each recognized one.
	providerURLs := map[string]string{} // Map provider code to the raw sourceUrl string
	for _, source := range resp.Data.Episode.SourceUrls {
		// The sourceUrl format is "--providerCode:encodedId". We need the full original string for getProviderInfo
		// Let's assume getProviderInfo can parse "--providerCode:encodedId" to get the decoded ID needed for get_links
		// Or, re-reading sources, the regexes in `provider_init` match the *initial API response line* that contains the sourceUrl.
		// So, the Bash script's `resp` variable contains the raw JSON, and `provider_init` regexes match that raw string.
		// `provider_id=$(printf "%s" "$resp" | sed -n "$2" | head -1 | cut -d':' -f2 | sed 's/../&\<br>/g' | ...)`
		// This means `generate_link` is called with provider numbers (1-4), not the actual sourceUrl strings.
		// provider_init matches a regex against the full `$resp` to find the relevant sourceUrl encoded ID.

		// Let's refactor: getEpisodeURL fetches the raw response. It passes this raw response and provider number to generateLinkByNumber.
		// generateLinkByNumber calls provider_init with the response and regex.
		// provider_init extracts and decodes the ID.
		// generateLinkByNumber calls get_links with the decoded ID.
	}

	// Corrected orchestration:
	// This is simplified; error handling for goroutines and channel closing is needed.
	recognizedProviders := []int{1, 2, 3, 4} // As per source
	for _, providerNum := range recognizedProviders {
		wg.Add(1)
		go func(pNum int) {
			defer wg.Done()
			links, err := generateLinkByNumber(pNum, string(bodyBytes)) // Pass raw response body
			if err != nil {
				log.Printf("Error generating links for provider %d: %v", pNum, err)
				return
			}
			linksChan <- links
		}(providerNum)
	}

	go func() {
		wg.Wait()
		close(linksChan)
	}()

	// Collect links from all providers
	for links := range linksChan {
		allLinks = append(allLinks, links...)
	}

	if len(allLinks) == 0 {
		return fmt.Errorf("episode is released, but no valid sources!") // Assume released if API returned embed URLs but link generation failed
	}

	// Sort links based on quality numerically (descending)
	// Need a helper to parse quality strings (e.g., "720p", "1080") into numbers for sorting.
	sort.SliceStable(allLinks, func(i, j int) bool {
		qi, err := strconv.Atoi(strings.TrimSuffix(allLinks[i].Quality, "p"))
		if err != nil { return false } // Can't parse, don't reorder
		qj, err := strconv.Atoi(strings.TrimSuffix(allLinks[j].Quality, "p"))
		if err != nil { return false }
		return qi > qj // Descending sort
	})

	// Bash script stores sorted links in a variable `links`
	// Let's store them in config or pass them along.
	// appConfig.AvailableLinks = allLinks // Or similar

	// Select the link with the desired quality
	selectedLink, err := selectQuality(appConfig.SelectedQuality, allLinks) // Implement selectQuality
	if err != nil {
		return fmt.Errorf("failed to select quality: %w", err)
	}

	// Bash script stores the final selected episode URL in the `episode` variable
	// Let's store the URL and associated flags (referer, subtitle) in config.
	// appConfig.CurrentEpisodeURL = selectedLink.URL
	// appConfig.CurrentEpisodeReferer = selectedLink.Referer
	// appConfig.CurrentEpisodeSubtitle = selectedLink.Subtitle
	// appConfig.CurrentEpisodeRawLink = selectedLink.Raw // Store raw line for change_quality menu

	return nil // Success
}

// Replicates generate_link $1 >"$cache_dir"/"$provider"
func generateLinkByNumber(providerNum int, rawAPIResponse string) ([]VideoLink, error) {
	providerName := ""
	regexPattern := "" // Regex pattern to match in the raw response for this provider
	// Determine provider name and regex based on providerNum
	switch providerNum {
	case 1: providerName = "wixmp"; regexPattern = "/Default :/p"
	case 2: providerName = "youtube"; regexPattern = "/Yt-mp4 :/p"
	case 3: providerName = "sharepoint"; regexPattern = "/S-mp4 :/p"
	case 4: providerName = "hianime"; regexPattern = "/Luf-Mp4 :/p"
	default: return nil, fmt.Errorf("unknown provider number: %d", providerNum)
	}

	// Replicate the Bash provider_id extraction using Go regex
	// Bash: `printf "%s" "$resp" | sed -n "$2" | head -1 | cut -d':' -f2 | sed 's/../&\<br>/g' | ...`
	// This is highly specific. It selects lines matching the pattern, takes the first, cuts field 2 (':'),
	// then processes the hex string.

	// Example (simplified): Find the first line in rawAPIResponse matching the regex pattern, extract the part after ':', and decode.
	re := regexp.MustCompile(regexPattern)
	lines := strings.Split(rawAPIResponse, "\n") // Split response into lines
	providerID = ""
	for _, line := range lines {
		if re.MatchString(line) { // Find line matching pattern
			// Extract the part after ':' (cut -d':' -f2) - need to be careful about multiple ':'
			parts := strings.SplitN(line, ":", 2) // Split on the first colon
			if len(parts) > 1 {
				encodedID := parts // This is the string to decode
				// Need to trim surrounding quotes if present (sed 's|"||g' from search_anime is applied later but maybe needed here?)
				// The provider_init regexes already seem to handle quotes e.g. `:"([^"]*)"`
				providerID = decodeProviderID(strings.Trim(encodedID, `"`)) // Decode the part after the colon
				break // Take the first match (head -1)
			}
		}
	}

	if providerID == "" {
		// Provider not found in the response
		return nil, nil // No links generated for this provider
	}

	// Now call get_links with the extracted providerID
	links, err := getLinks(providerID) // Implement getLinks
	if err != nil {
		return nil, fmt.Errorf("failed to get links for provider %s: %w", providerName, err)
	}

	// Bash script adds provider name debug output here
	fmt.Fprintf(os.Stderr, "\033[1;32m%s\033[0m Links Fetched\n", providerName)

	return links, nil
}
```

**9. Quality Selection (`select_quality`)**

Filters and selects one video link based on the desired quality.

*   **Bash Script Approach**: Takes the list of links (sorted) and the desired quality string. Uses `grep -m 1` to find the first line matching the quality, `head -n1` for 'best', `tail -n1` on filtered results for 'worst'. Extracts URL, subtitle, and referer flags based on patterns (`cc>`, `subtitle >`, `Yt >`).
*   **Golang Replication**: Iterate through the sorted list of `VideoLink` structs. Find the first link matching the quality string. Implement logic for 'best' and 'worst'. Extract URL, subtitle, and referer from the selected `VideoLink` properties.
*   **Code Example**:

```go
func selectQuality(quality string, links []VideoLink) (*VideoLink, error) {
	if len(links) == 0 {
		return nil, fmt.Errorf("no links available to select quality")
	}

	var selectedLink *VideoLink

	// Filter out soft subs for specific players if needed
	// Bash: `sed '/cc>/d;/subtitle >/d;/m3u8_refr >/d'` for android, iSH, vlc
	// Bash: `sed '/Yt >/d'` for android, iSH
	filteredLinks := []VideoLink{}
	requiresHardSubs := strings.Contains(appConfig.Player, "android") || strings.Contains(appConfig.Player, "iSH") || strings.Contains(appConfig.Player, "vlc") // Players that don't get correct referrer or handle soft subs well
	requiresNoYt := strings.Contains(appConfig.Player, "android") || strings.Contains(appConfig.Player, "iSH") // Players that don't handle Yt well

	for _, link := range links {
		if requiresHardSubs && (strings.Contains(link.Raw, "cc>") || strings.Contains(link.Raw, "subtitle >") || strings.Contains(link.Raw, "m3u8_refr >")) {
			continue
		}
		if requiresNoYt && strings.Contains(link.Raw, "Yt >") {
			continue
		}
		filteredLinks = append(filteredLinks, link)
	}

	if len(filteredLinks) == 0 {
		log.Println("Warning: No links available after filtering for player compatibility. Trying original links.")
		filteredLinks = links // Fallback if filtering removed everything
	}


	switch quality {
	case "best":
		if len(filteredLinks) > 0 {
			selectedLink = &filteredLinks // Links are already sorted descending by quality
		}
	case "worst":
		// Find the lowest quality that is a number (e.g., "360", "480")
		var lowestQualityLink *VideoLink
		lowestQuality := 9999 // Start high
		for i := len(filteredLinks) - 1; i >= 0; i-- { // Iterate from end of sorted list
			link := filteredLinks[i]
			q, err := strconv.Atoi(strings.TrimSuffix(link.Quality, "p"))
			if err == nil { // Successfully parsed as number quality
				if q < lowestQuality {
					lowestQuality = q
					lowestQualityLink = &link
				}
			}
		}
		selectedLink = lowestQualityLink
	default:
		// Find the first link matching the specific quality string
		for _, link := range filteredLinks {
			if link.Quality == quality || strings.HasPrefix(link.Quality, quality) { // Bash uses grep -m 1 "$1" - allows partial match?
				selectedLink = &link
				break
			}
		}
	}

	if selectedLink == nil {
		// Specified quality not found, default to best
		fmt.Fprintln(os.Stderr, "Specified quality not found, defaulting to best")
		if len(filteredLinks) > 0 {
			selectedLink = &filteredLinks // Best of filtered or original list
		} else if len(links) > 0 { // If filtered list was empty, take from original
             selectedLink = &links
        } else {
             return nil, fmt.Errorf("no links available")
        }
	}

	// Extract subtitle and referer flags from the selected link's Raw representation
	// Bash uses sed/grep on the raw line string
	// Example: `subtitle="$(printf '%s' "$links" | sed -nE 's|subtitle >(.*)|\1|p')"`
	// In Go, we can use regex on the selectedLink.Raw string
	selectedLink.Subtitle = "" // Reset for selected link
	selectedLink.Referer = "" // Reset for selected link

	reSubtitle := regexp.MustCompile(`subtitle >(.*)`)
	if matches := reSubtitle.FindStringSubmatch(selectedLink.Raw); len(matches) > 1 {
		selectedLink.Subtitle = matches
	}

	reM3u8Referer := regexp.MustCompile(`m3u8_refr >(.*)`)
	if matches := reM3u8Referer.FindStringSubmatch(selectedLink.Raw); len(matches) > 1 {
		selectedLink.Referer = matches
	}

	reYt := regexp.MustCompile(`Yt >.*`)
	if reYt.MatchString(selectedLink.Raw) {
		// Bash sets refr_flag to $allanime_refr for Yt links
		selectedLink.Referer = allanimeReferer
	}

	// Unset referer/subtitle flags if not applicable to the link type
	// Bash: `! (printf '%s' "$result" | grep -qE "(cc>|tools.fast4speed.rsvp)") && unset refr_flag`
	// `! (printf '%s' "$result" | grep -q "cc>") && unset subs_flag`
	if !strings.Contains(selectedLink.Raw, "cc>") && !strings.Contains(selectedLink.Raw, "tools.fast4speed.rsvp") {
		selectedLink.Referer = "" // No cc> or Yt pattern, unset referer
	}
	if !strings.Contains(selectedLink.Raw, "cc>") {
		selectedLink.Subtitle = "" // No cc> pattern, unset subtitle
	}


	// Store the final selected link and its flags in config or state
	// appConfig.CurrentSelectedLink = selectedLink // Store the struct

	return selectedLink, nil
}
```

**10. Playback (`play_episode`, `play`)**

Launches the selected video URL using the configured player and manages episode ranges and the playback loop.

*   **Bash Script Approach**: Uses a `case` statement on the `player_function` variable to build and execute the correct command line using `exec` (implicitly via just typing the command name). Arguments include the URL, title, subtitle file, and referer. `nohup ... &` is used for backgrounding. `tput` is used for terminal control. The `play` function handles episode ranges and loops.
*   **Golang Replication**: Use `exec.Command` to prepare the player command. Build the argument list based on the selected link's details and configuration (title, subtitle, referer, skip intro). Use `Cmd.Start()` and potentially OS-specific `SysProcAttr` to achieve backgrounding if needed (true backgrounding cross-platform is tricky; running without `Wait()` is simpler but ties to the parent process). Use Go's `fmt` for terminal output (simulating `tput`). Implement the loop logic in the `play` function.
*   **Code Example (Illustrative)**:

```go
// play_episode function
func playEpisode(link *VideoLink, animeTitle string, episodeNum string) error {
	if link == nil {
		return fmt.Errorf("no link provided to play")
	}

	// Log episode playback if enabled - requires syslog library or similar (outside source)
	if appConfig.LogEpisode {
		// Implement logging to syslog/journalctl based on OS
		// Requires calling logger/journalctl command or using platform-specific logging APIs.
		logMessage := fmt.Sprintf("%sEpisode %s", animeTitle, episodeNum)
		// Example (Linux journalctl):
		// cmd := exec.Command("logger", "-t", "ani-cli", logMessage)
		// cmd.Run() // Run asynchronously
	}


	// Get skip intro flags if enabled - requires ani-skip command
	skipFlag := ""
	if appConfig.SkipIntro {
		// Need anime mal_id - script gets it using `ani-skip -q "${skip_title:-${title}}"` before playback loop
		// Let's assume mal_id is already obtained and stored in config/state.
		malID := appConfig.MalID // Assume MalID is fetched earlier

		cmd := exec.Command("ani-skip", "-q", malID, "-e", episodeNum) // ani-skip call
		out, err := cmd.Output()
		if err != nil {
			log.Printf("Warning: ani-skip failed: %v", err)
			skipFlag = "" // Clear skip flag on error
		} else {
			skipFlag = strings.TrimSpace(string(out)) // Capture ani-skip output
		}
	}

	// Build player arguments
	playerArgs := []string{}
	fullEpisodeTitle := fmt.Sprintf("%sEpisode %s", animeTitle, episodeNum)

	// Common arguments
	// Bash sets media title: `--force-media-title="..."` for mpv/iina/syncplay, `--meta-title="..."` for vlc
	// Bash passes subs: `--sub-file="..."` for mpv/syncplay/iina (with --mpv prefix)
	// Bash passes referer: `--referrer="..."` for mpv/syncplay/iina (with --mpv prefix), `--http-referrer="..."` for vlc

	// Handle specific player cases
	switch appConfig.Player {
	case "debug":
		fmt.Println("All links:\n", appConfig.AvailableLinks) // Assuming AvailableLinks is stored
		fmt.Println("Selected link:\n", link.Raw) // Assuming Raw is stored
		return nil // Debug doesn't play

	case "mpv", "mpv.exe":
		if skipFlag != "" { playerArgs = append(playerArgs, skipFlag) }
		if link.Subtitle != "" { playerArgs = append(playerArgs, "--sub-file="+link.Subtitle) }
		if link.Referer != "" { playerArgs = append(playerArgs, "--referrer="+link.Referer) }
		playerArgs = append(playerArgs, "--force-media-title="+fullEpisodeTitle, link.URL)

	case "android_mpv":
		// Bash uses `am start` intent - complex to replicate directly in Go cross-platform.
		// Needs `exec.Command("am", "start", ...)` and handling Android intents.
		return fmt.Errorf("android_mpv playback not fully implemented in Go example")

	case "android_vlc":
		// Bash uses `am start` intent - needs Android intent handling.
		return fmt.Errorf("android_vlc playback not fully implemented in Go example")

	case "iina", "/Applications/IINA.app/Contents/MacOS/iina-cli":
		// Bash prefixes mpv flags with --mpv- for iina
		playerArgs = append(playerArgs, "--no-stdin", "--keep-running")
		if link.Subtitle != "" { playerArgs = append(playerArgs, "--mpv-sub-file="+link.Subtitle) }
		if link.Referer != "" { playerArgs = append(playerArgs, "--mpv-referrer="+link.Referer) }
		playerArgs = append(playerArgs, "--mpv-force-media-title="+fullEpisodeTitle, link.URL)

	case "flatpak_mpv":
		// Bash uses `flatpak run io.mpv.Mpv ...`
		playerArgs = append([]string{"run", "io.mpv.Mpv"}, playerArgs...) // Prepend flatpak command and app ID
		if skipFlag != "" { playerArgs = append(playerArgs, skipFlag) }
		if link.Subtitle != "" { playerArgs = append(playerArgs, "--sub-file="+link.Subtitle) }
		if link.Referer != "" { playerArgs = append(playerArgs, "--referrer="+link.Referer) }
		playerArgs = append(playerArgs, "--force-media-title="+fullEpisodeTitle, link.URL)


	case "vlc", "vlc.exe":
		// Bash uses `--http-referrer="..." --play-and-exit --meta-title="..."`
		if link.Referer != "" { playerArgs = append(playerArgs, "--http-referrer="+link.Referer) }
		playerArgs = append(playerArgs, "--play-and-exit", "--meta-title="+fullEpisodeTitle, link.URL)

	case "syncplay", "/Applications/Syncplay.app/Contents/MacOS/syncplay":
		// Bash passes URL first, then `-- --force-media-title ...`
		playerArgs = append(playerArgs, link.URL, "--", "--force-media-title="+fullEpisodeTitle)
		if link.Subtitle != "" { playerArgs = append(playerArgs, "--sub-file="+link.Subtitle) }
		if link.Referer != "" { playerArgs = append(playerArgs, "--referrer="+link.Referer) }

	case "download":
		// This case calls the download function instead of a player
		return downloadEpisode(link, fullEpisodeTitle) // Implement downloadEpisode

	case "catt":
		// Bash uses `catt cast URL -s SUBTITLE`
		playerArgs = append(playerArgs, "cast", link.URL)
		if link.Subtitle != "" { playerArgs = append(playerArgs, "-s", link.Subtitle) }

	case "iSH":
		// Bash prints a special terminal escape sequence
		fmt.Printf("\e]8;;vlc://%s\a~~~~~~~~~~~~~~~~~~~~\n~ Tap to open VLC ~\n~~~~~~~~~~~~~~~~~~~~\e]8;;\a\n", link.URL)
		time.Sleep(5 * time.Second) // Wait for potential tap
		return nil // iSH doesn't launch process directly

	default:
		// Default player case
		playerArgs = append(playerArgs, link.URL)
	}

	// Execute the player command
	cmd := exec.Command(appConfig.Player, playerArgs...)

	// Handle detachment/backgrounding
	// Bash uses `nohup ... &`
	if appConfig.NoDetach == false && appConfig.Player != "iSH" && appConfig.Player != "debug" && appConfig.Player != "download" {
		// Try to detach - OS specific
		// For Linux/Unix-like, SysProcAttr can be used, but full detachment is tricky.
		// Simplest is to run `Start()` and not `Wait()`. This leaves the player running
		// but tied to the parent process's terminal unless other measures are taken (like setsid).
		// A robust solution might involve helper C code or more complex Go syscalls.
		// For this example, we'll just Start and not Wait if not detached.
		cmd.Stdout = nil // Discard player output
		cmd.Stderr = nil // Discard player errors
		if err := cmd.Start(); err != nil {
			return fmt.Errorf("failed to start player '%s': %w", appConfig.Player, err)
		}
		// Player is running in background (best effort)
		log.Printf("Player '%s' started in background.", appConfig.Player)

	} else {
		// Not detaching (e.g., for in-terminal mpv or when exit_after_play is true)
		// Attach stdout/stderr to parent process for terminal playback experience
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr

		if err := cmd.Run(); err != nil { // Run waits for command to complete
			// Handle exit code for exit_after_play
			if appConfig.ExitAfterPlay {
				if exitErr, ok := err.(*exec.ExitError); ok {
					os.Exit(exitErr.ExitCode()) // Exit with player's code
				} else {
					os.Exit(1) // Exit with error
				}
			}
			return fmt.Errorf("player '%s' failed: %w", appConfig.Player, err)
		}
		// Player finished successfully
		if appConfig.ExitAfterPlay && !appConfig.IsRangePlayback { // Check if not in a range
			os.Exit(0) // Exit successfully
		}
	}

	// Store replay URL
	// appConfig.ReplayURL = link.URL

	// Update history after successful playback
	// Need to pass current episode number and title to updateHistory
	// updateHistory(appConfig.CurrentShowID, appConfig.CurrentEpisodeNumber, appConfig.CurrentShowTitle) // Implement updateHistory

	// If external menu is used, wait for the background process to finish before showing menu
	if appConfig.UseExternalMenu && appConfig.NoDetach == false { // Only wait if detached and external menu is used
		// This requires keeping track of the background process created by cmd.Start()
		// and calling Wait() on it here.
		// Example: cmd.Wait()
		// This complicates the loop logic in `play`.
	}

	return nil // Playback initiated or completed (if not detached)
}

// play function orchestrates episode range and playback loop
func play(showID string, title string, episodeNum string) error {
	// Handle episode ranges (e.g., "5-7", "5 6")
	// Bash script uses sed/grep to parse the range string and select episodes from ep_list
	// Need to parse episodeNum string and get the list of episode numbers to play.
	// Use listEpisodes function to get the full list first.
	epListStr, err := listEpisodes(showID) // Get the sorted list of episodes
	if err != nil { return fmt.Errorf("failed to get episode list for playback: %w", err) }
	epList := strings.Split(strings.TrimSpace(epListStr), "\n")

	episodesToPlay, err := parseEpisodeRange(episodeNum, epList) // Implement parsing function
	if err != nil { return fmt.Errorf("invalid episode number or range: %w", err) }

	appConfig.IsRangePlayback = len(episodesToPlay) > 1 // Set flag for exit_after_play logic


	// Main playback loop
	currentEpisodeIndex := -1 // Keep track of current episode index in episodesToPlay list
	if len(episodesToPlay) > 0 {
		// Find starting episode if range was given, otherwise take the single epNum
		for i, ep := range epList {
			if ep == episodesToPlay {
				currentEpisodeIndex = i
				break
			}
		}
		if currentEpisodeIndex == -1 {
			return fmt.Errorf("starting episode not found in episode list")
		}
	} else {
		return fmt.Errorf("no episodes selected to play")
	}

	playedEpisodeCount := 0
	for {
		if playedEpisodeCount >= len(episodesToPlay) {
			break // Finished playing all episodes in the list/range
		}

		epNum := episodesToPlay[playedEpisodeCount]

		// Get episode URL and select quality for the current episode
		// This needs to be done *inside* the loop if quality can change or if
		// links are episode-specific (which they are).
		// However, the Bash script calls get_episode_url only once before the loop if `episode` variable is empty.
		// And `select_quality` is called when `change_quality` is chosen in the menu.
		// This implies links and quality are selected ONCE per `play` call, unless manually changed.
		// Let's replicate the Bash flow: get links for the first episode, then reuse them or re-select quality.
		// Need to rethink the flow slightly.

		// Replicated Bash flow:
		// getEpisodeURL is called outside the loop the first time, or if $episode is unset
		// For range playback, getEpisodeURL must be called for *each* episode in the range.
		// The Bash script calls `play_episode` inside the range loop, and `play_episode` calls `get_episode_url` if $episode is empty.
		// This suggests $episode is unset for each iteration of the range loop.

		// Inside the loop:
		fmt.Printf("\33[2K\r\033[1;34mPlaying episode %s...\033[0m\n", epNum)
		// Replicate tput clear, sc
		// Use ANSI escape codes: \033[2J\033[H (clear), \033[s (save cursor)
		fmt.Print("\033[2J\033[H") // Clear screen, move to home
		fmt.Print("\033[s")      // Save cursor position

		// Get links and select quality for the current episode
		err = getEpisodeURL(showID, epNum) // This populates config with current link/flags
		if err != nil {
			// Handle error (e.g., episode not released in a range) - Bash uses `die`
			// We should probably just print a warning and skip or ask user.
			log.Printf("Error fetching episode %s: %v. Skipping.", epNum, err)
			playedEpisodeCount++
			continue // Skip to next episode in range
		}

		// Play the episode using the selected link stored in config
		// Need to pass the selected link object to playEpisode
		err = playEpisode(appConfig.CurrentSelectedLink, title, epNum) // Assume CurrentSelectedLink is populated by getEpisodeURL/selectQuality
		if err != nil {
			log.Printf("Error playing episode %s: %v", epNum, err)
			// Decide how to handle playback errors (e.g., continue to next episode, stop)
		}

		// Update history *after* playback of this episode
		updateHistory(showID, epNum, title) // Need to implement updateHistory

		playedEpisodeCount++

		// If playing a range, and this is not the last episode, maybe pause or show prompt?
		// Bash script just loops immediately.

		// Main playback loop menu after playing a single episode or the last in a range
		if playedEpisodeCount >= len(episodesToPlay) && !appConfig.IsRangePlayback {
			// Only show menu if not playing a range and finished a single episode.
			// If playing a range, the loop finishes and the program might exit or return.
			// The Bash script's menu appears *after* the `play` function finishes its loop/single play.
			// This implies the menu logic is *outside* the `play` function in the main loop.
			break // Exit the range loop, fall through to main loop/menu
		}
	}

	// After play finishes (either single episode or range)
	// Replicate tput rc, ed
	// Use ANSI escape codes: \033[u (restore cursor), \033[J (clear from cursor down)
	// Bash script only does this if player is not debug or download
	if appConfig.Player != "debug" && appConfig.Player != "download" {
		fmt.Print("\033[u") // Restore cursor position
		fmt.Print("\033[J") // Clear from cursor down
	}

	return nil // Playback sequence finished
}

// Helper to parse episode range string like "5-7", "5 6", "-1"
func parseEpisodeRange(rangeStr string, epList []string) ([]string, error) {
    // This is complex string parsing replicating Bash logic
    // Need to find start/end episodes in epList based on the range string.
    // Handle "-1" (latest)
    // Handle space separated list "5 6"
    // Handle hyphenated range "5-7"
    // Handle single number "5"

    // Example (partial):
    episodes := []string{}
    if strings.Contains(rangeStr, "-") { // Hyphenated range
        parts := strings.Split(rangeStr, "-")
        if len(parts) == 2 {
            startEp := parts
            endEp := parts
            if startEp == "-1" { // Handle -1 at start of range - Bash doesn't seem to support this easily.
                // Bash seems to handle -1 as a single episode number (latest)
                // And ranges like `blue lock -e 5-6`. Let's stick to that.
                 return nil, fmt.Errorf("invalid range format with -1 start")
            }
             if endEp == "-1" { // Handle -1 at end of range - Bash sets end to latest
                 if len(epList) > 0 {
                      endEp = epList[len(epList)-1]
                 } else {
                     return nil, fmt.Errorf("cannot determine latest episode for range")
                 }
             }
            // Find start and end indices in epList (Bash uses sed -nE "/^${start}\$/,/^${end}\$/p")
            startIndex := -1
            endIndex := -1
            for i, ep := range epList {
                if ep == startEp { startIndex = i }
                if ep == endEp { endIndex = i }
            }

            if startIndex != -1 && endIndex != -1 && startIndex <= endIndex {
                episodes = epList[startIndex : endIndex+1] // Slice the relevant part
            } else {
                return nil, fmt.Errorf("invalid range specified or episodes not found in list")
            }
        } else {
            return nil, fmt.Errorf("invalid range format")
        }
    } else if strings.Contains(rangeStr, " ") { // Space separated list
         episodes = strings.Fields(rangeStr) // Simple split by space
         // Need to validate these exist in epList if strict checking is required.
    } else if rangeStr == "-1" { // Latest episode
        if len(epList) > 0 {
            episodes = []string{epList[len(epList)-1]}
        } else {
             return nil, fmt.Errorf("cannot determine latest episode")
        }
    } else if rangeStr != "" { // Single episode number
        episodes = []string{rangeStr}
        // Check if it exists in epList if strict checking needed.
    }

    if len(episodes) == 0 && rangeStr != "" {
         return nil, fmt.Errorf("specified episode/range not found")
    }


    return episodes, nil
}

// Implement the main playback loop logic that comes after initial search/selection in the Bash script
func runPlaybackLoop(showID string, title string, initialEpisodeNum string) error {
    // Need the full episode list for navigation (next/previous)
    epListStr, err := listEpisodes(showID)
    if err != nil { return fmt.Errorf("failed to get episode list: %w", err) }
    epList := strings.Split(strings.TrimSpace(epListStr), "\n")

    currentEpNum := initialEpisodeNum // Start with the selected episode

    for {
        // Bash script shows a menu prompt `Playing episode $ep_no of $title...`
        // Use nth/launcher equivalent for menu selection
        menuOptions := "next\nreplay\nprevious\nselect\nchange_quality\nquit"
        prompt := fmt.Sprintf("Playing episode %s of %s... ", currentEpNum, title)

        cmd, err := launcher(prompt, menuOptions, false) // Assuming single selection for menu
        if err != nil {
             // User cancelled menu - treat as quit?
             if strings.Contains(err.Error(), "cancelled") {
                 fmt.Println("Playback cancelled.")
                 return nil
             }
             return fmt.Errorf("menu selection failed: %w", err)
        }

        switch cmd {
        case "next":
            // Find current episode in epList and get the next one
            nextEp := ""
            for i, ep := range epList {
                if ep == currentEpNum && i+1 < len(epList) {
                    nextEp = epList[i+1]
                    break
                }
            }
            if nextEp == "" {
                die("Out of range") // Replicate Bash error
            }
            currentEpNum = nextEp // Update current episode number
            // Continue to play the next episode below

        case "replay":
            // Bash script uses the `replay` variable which stores the last played URL
            // We need to store the last played link object or URL.
            // Use the link from the previous playback.
            // This case skips fetching episode URL again and directly calls play_episode
            // Need to retrieve the last used link.
            // For simplicity in this structure, we might re-fetch or indicate replay state.
            // Let's assume we can re-fetch or stored the last played link.
            fmt.Println("Replaying current episode...")
             // Need to somehow signal playEpisode to use the previous link or refetch.
             // A simpler approach might be to just call play() with the current episode number again.
             // The `play` function itself handles calling getEpisodeURL if needed.

             // Replicate the Bash logic: set $episode variable to $replay
             // This bypasses the get_episode_url call in play_episode if $episode is set.
             // We can pass a flag or the direct URL to playEpisode or getEpisodeURL.
             // Let's adjust playEpisode to accept an optional direct URL for replay.
             //err = playEpisode(appConfig.ReplayURL, title, currentEpNum) // Need to store ReplayURL
             // For now, let's just re-call play for the same episode number.
             err = play(showID, title, currentEpNum) // Re-run play for the same episode
             if err != nil { log.Printf("Error during replay: %v", err); /* Decide error handling */ }
             continue // Menu loop continues after play finishes


        case "previous":
            // Find current episode in epList and get the previous one
             prevEp := ""
             for i, ep := range epList {
                 if ep == currentEpNum && i > 0 {
                 prevEp = epList[i-1]
                 break
                 }
            }
             if prevEp == "" {
                die("Out of range") // Replicate Bash error (if current is the first)
            }
             currentEpNum = prevEp // Update current episode number
            // Continue to play the previous episode

        case "select":
            // Prompt user to select a new episode from the list
            selectedEpStr, err := launcher("Select episode: ", epListStr, appConfig.MultiSelectionFlag != "") // Pass full list, check multi-select flag
            if err != nil {
                 if strings.Contains(err.Error(), "cancelled") {
                    continue // User cancelled episode selection, return to main menu
                 }
                 return fmt.Errorf("episode selection failed: %w", err)
            }
            // The output of launcher/nth here can be a single episode or multiple if multi-select is enabled
            // Bash script assigns this directly to `ep_no`. The `play` function then handles ranges.
            // Let's do the same: update currentEpNum or set up a new range.
            // If multiple selected, parse as a range.
            newEpisodesToPlay, err := parseEpisodeRange(selectedEpStr, epList)
             if err != nil || len(newEpisodesToPlay) == 0 {
                 log.Printf("Invalid selection: %v", err)
                 continue // Stay in menu
             }
            // Update the sequence of episodes to play. For simplicity, let's just play the first selected one for now
            // or set the sequence if range playback is re-initiated.
            // A full port needs to manage the `episodesToPlay` list within the main loop.
            currentEpNum = newEpisodesToPlay // Just play the first selected for now
            // To replicate fully, should update the loop's sequence and reset played count.
             // For this example, we'll just set the *next* episode to be played to the first of the selection.

        case "change_quality":
             // Need the list of available links again. This was stored after getEpisodeURL.
             // Or re-run getEpisodeURL? The script uses `links` variable, suggesting it's stored.
             // Prompt user to select from available links, extracting quality string
             // `printf "%s" "$links" | launcher | cut -d\> -f1`
             // This requires formatting the `allLinks` struct list back into the "Quality > URL" format for the launcher.
             var linkStrings []string
             for _, link := range appConfig.AvailableLinks { // Assume AvailableLinks is stored
                 linkStrings = append(linkStrings, link.Raw) // Use the raw line format
             }
             linkListString := strings.Join(linkStrings, "\n")

             selectedLinkRaw, err := launcher("", linkListString, false) // Select from link strings
             if err != nil {
                  if strings.Contains(err.Error(), "cancelled") {
                     continue // User cancelled
                  }
                  return fmt.Errorf("quality selection failed: %w", err)
             }

             // Extract quality string from the selected line
             // Bash: `cut -d\> -f1`
             selectedQualityString := strings.SplitN(selectedLinkRaw, ">", 2) // Get part before first '>'

             // Re-select quality based on the chosen string
             // This updates appConfig.CurrentSelectedLink and flags.
             selectedLink, err := selectQuality(selectedQualityString, appConfig.AvailableLinks) // Use stored links
             if err != nil {
                 log.Printf("Error selecting quality: %v", err)
                 continue // Stay in menu
             }
             // Update the link to be used for the *next* playback.
             appConfig.CurrentSelectedLink = selectedLink // Store the newly selected link

             fmt.Printf("Quality changed to %s\n", selectedQualityString)
             continue // Stay in menu after changing quality


        case "quit":
            fmt.Println("Quitting playback.")
            return nil // Exit loop and function

        default:
            log.Printf("Unknown command: %s", cmd)
             continue // Stay in menu
        }

        // If we reached here, it means a navigation command (next, previous, select) was chosen.
        // Now play the selected episode (currentEpNum)
        // This call will re-fetch/re-select link if needed (e.g., first time playing this ep, or after select)
        err = play(showID, title, currentEpNum) // Call play again with the updated episode number
         if err != nil { log.Printf("Error during playback: %v", err); /* Decide error handling */ }

        // The loop continues after `play` returns. If `play` completed a range, it exits.
        // If `play` completed a single episode, it returns, and the menu is shown again.
    }
}
```
*Note: The interactive playback loop logic in the Bash script is simplified. The Go example above attempts to replicate its observed behavior, but managing the state (current episode, list of episodes to play in a range, currently selected link) requires careful design in Go structs.*

**11. Download (`download`)**

Downloads the video and optionally subtitles.

*   **Bash Script Approach**: Uses `yt-dlp`, `ffmpeg`, or `aria2c` based on the link type and available commands. Constructs command-line arguments including referer, output directory, and file name. Optionally downloads subtitles using `curl` and embeds them using `ffmpeg`.
*   **Golang Replication**: Use `exec.Command` to run the downloader executables. Build arguments based on the link type and downloaded file paths. Use `net/http` or `os/exec` (`curl`) to download subtitles. Embed subtitles using `exec.Command` (`ffmpeg`).
*   **Code Example**:

```go
// downloadEpisode function (called from playEpisode)
func downloadEpisode(link *VideoLink, fileNameBase string) error {
	if link == nil || link.URL == "" {
		return fmt.Errorf("no link provided for download")
	}

	downloadFileName := filepath.Join(appConfig.DownloadDir, fileNameBase+".mp4")

	// Download subtitle if available
	if link.Subtitle != "" {
		subtitleFileName := filepath.Join(appConfig.DownloadDir, fileNameBase+".vtt")
		fmt.Printf("Downloading subtitle to %s...\n", subtitleFileName)
		// Use Go's net/http client to download the subtitle URL
		resp, err := http.Get(link.Subtitle) // Assuming subtitles don't need special headers
		if err != nil {
			log.Printf("Warning: Failed to download subtitle: %v", err)
			// Continue download of video even if subtitle fails
		} else {
			defer resp.Body.Close()
			outFile, err := os.Create(subtitleFileName)
			if err != nil {
				log.Printf("Warning: Failed to create subtitle file: %v", err)
			} else {
				defer outFile.Close()
				_, err = io.Copy(outFile, resp.Body)
				if err != nil {
					log.Printf("Warning: Failed to write subtitle file: %v", err)
				} else {
					fmt.Println("Subtitle download complete.")
				}
			}
		}
	}

	// Download the video based on link type and available tools
	fmt.Printf("Downloading video to %s...\n", downloadFileName)
	var cmd *exec.Cmd

	// Case handling based on link type (m3u8 vs others)
	if strings.Contains(link.URL, ".m3u8") || strings.Contains(link.Raw, "cc>") { // Check for m3u8 or cc> pattern
		// Use yt-dlp or ffmpeg for M3U8
		ytDlpPath, err := exec.LookPath("yt-dlp")
		if err == nil {
			// Use yt-dlp
			args := []string{"--no-skip-unavailable-fragments", "--fragment-retries", "infinite", "-N", "16", "-o", downloadFileName, link.URL}
			if link.Referer != "" { args = append([]string{"--referer", link.Referer}, args...) } // Prepend referer
             // Add iSH_DownFix if applicable - needs logic based on appConfig.Player and link type
            if appConfig.Player == "iSH" && strings.Contains(link.Raw, "cc>") { // iSH_DownFix logic
                 args = append([]string{"--async-dns=false"}, args...)
            }

			cmd = exec.Command(ytDlpPath, args...)
		} else {
			// Use ffmpeg
			args := []string{"-loglevel", "error", "-stats", "-i", link.URL, "-c", "copy", downloadFileName}
			if link.Referer != "" { args = append([]string{"-referer", link.Referer}, args...) } // Prepend referer
			cmd = exec.Command("ffmpeg", args...) // Assumes ffmpeg is checked/found
		}
	} else {
		// Use aria2c for other link types (direct mp4?)
		// Note: Bash script uses aria2c with --referer=$allanime_refr --enable-rpc=false etc.
		// It also uses $iSH_DownFix conditionally.

		aria2cPath, err := exec.LookPath("aria2c")
		if err != nil {
			return fmt.Errorf("'aria2c' not found, cannot download direct links.")
		}

		args := []string{"--enable-rpc=false", "--check-certificate=false", "--continue", "--summary-interval=0", "-x", "16", "-s", "16", "--dir=" + appConfig.DownloadDir, "-o", filepath.Base(downloadFileName), "--download-result=hide", link.URL}
		if link.Referer != "" { args = append([]string{"--referer=" + link.Referer}, args...) } // Prepend referer

        // Add iSH_DownFix if applicable
        if appConfig.Player == "iSH" { // Bash applies iSH_DownFix for download function when player is iSH
            args = append([]string{"--async-dns=false"}, args...)
        }

		cmd = exec.Command(aria2cPath, args...)
	}

	// Execute the download command
	cmd.Stdout = os.Stdout // Show download progress
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("download failed: %w", err)
	}

	fmt.Println("Video download complete.")

	// Embed subtitles using ffmpeg (optional, commented out in Bash source)
	// if subtitleFileName != "" && fileExists(subtitleFileName) {
	// 	fmt.Println("Embedding subtitle...")
	// 	tempDownloadFileName := downloadFileName + ".bak.mp4" // Backup original
	// 	// ffmpeg -i video.mp4 -i subs.vtt -c copy -c:s mov_text output.mp4
	// 	embedCmd := exec.Command("ffmpeg", "-i", downloadFileName, "-i", subtitleFileName, "-c", "copy", "-c:s", "mov_text", tempDownloadFileName)
	// 	embedCmd.Stdout = os.Stdout
	// 	embedCmd.Stderr = os.Stderr
	// 	if err := embedCmd.Run(); err != nil {
	// 		log.Printf("Warning: Failed to embed subtitles: %v", err)
	// 	} else {
	// 		os.Rename(tempDownloadFileName, downloadFileName) // Replace original with new file
	// 		fmt.Println("Subtitle embedding complete.")
	// 	}
	// }

	return nil // Download successful
}
```

**12. History Management**

Reads, updates, and deletes history from a file.

*   **Bash Script Approach**: History is stored in `$histfile` (`~/.local/state/ani-cli/ani-hsts` by default). Each line is `ep_no\tid\ttitle` (tab-separated). `grep` is used to check if an entry exists, `sed` is used to update or the entry is appended. Deletion clears the file (`: >"$histfile"`). Reading history involves processing the file line by line.
*   **Golang Replication**: Use `os` and `bufio` to read and write the history file. Use standard string functions (`strings.Split`, `strings.Join`) to handle the tab-separated format. Implement functions for reading, updating, deleting, and processing history entries.
*   **Code Example**:

```go
// Structure for a history entry
type HistoryEntry struct {
	EpisodeNumber string
	ShowID string
	Title string
}

// Path to history file
// histFile := filepath.Join(appConfig.HistDir, "ani-hsts") // Already determined in loadConfig

// Read all history entries from the file
func readHistory() ([]HistoryEntry, error) {
	histFile := filepath.Join(appConfig.HistDir, "ani-hsts")
	file, err := os.Open(histFile)
	if err != nil {
		// If file doesn't exist, return empty list, not an error
		if os.IsNotExist(err) {
			return []HistoryEntry{}, nil
		}
		return nil, fmt.Errorf("failed to open history file: %w", err)
	}
	defer file.Close()

	var entries []HistoryEntry
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		// Bash format is tab-separated: `ep_no\tid\ttitle`
		parts := strings.Split(line, "\t")
		if len(parts) == 3 {
			entries = append(entries, HistoryEntry{
				EpisodeNumber: parts,
				ShowID: parts,
				Title: parts,
			})
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading history file: %w", err)
	}

	return entries, nil
}

// Update or add a history entry
func updateHistory(showID string, episodeNum string, title string) error {
	histFile := filepath.Join(appConfig.HistDir, "ani-hsts")
	entries, err := readHistory() // Read existing entries
	if err != nil {
		// If reading fails, maybe just try to append? Or report error?
		// Bash creates the file if it doesn't exist. Let's assume readHistory handles non-existent file.
	}

	newEntry := HistoryEntry{EpisodeNumber: episodeNum, ShowID: showID, Title: title}
	updated := false
	// Check if entry for this showID already exists
	for i := range entries {
		if entries[i].ShowID == showID { // Bash checks `grep -q -- "$id" "$histfile"`
			// Update the existing entry
			// Bash uses `sed -E "s|^[^ ]+ ${id} [^ ]+$|${ep_no} ${id} ${title}|"`
			// This regex replaces the whole line if it contains the ID, which might be too aggressive.
			// It seems to replace `EPISODE_NUMBER ID TITLE` with `new_EPISODE_NUMBER ID new_TITLE`?
			// Let's assume it just updates the episode number and title for that show ID.
			entries[i] = newEntry // Replace the old entry with the new one
			updated = true
			break
		}
	}

	if !updated {
		// Add new entry if not found
		entries = append(entries, newEntry)
	}

	// Write all entries back to the file
	// Bash uses a temporary file and then moves it - good practice for atomic write
	tempFile := histFile + ".new"
	outFile, err := os.Create(tempFile)
	if err != nil {
		return fmt.Errorf("failed to create temporary history file: %w", err)
	}
	defer outFile.Close()

	writer := bufio.NewWriter(outFile)
	for _, entry := range entries {
		// Write in tab-separated format
		line := fmt.Sprintf("%s\t%s\t%s\n", entry.EpisodeNumber, entry.ShowID, entry.Title)
		_, err := writer.WriteString(line)
		if err != nil {
			return fmt.Errorf("failed to write history entry to temporary file: %w", err)
		}
	}
	writer.Flush()
	outFile.Sync() // Ensure data is written to disk

	// Rename the temporary file to the history file
	err = os.Rename(tempFile, histFile)
	if err != nil {
		// This could leave the history file in a bad state or temp file lying around
		return fmt.Errorf("failed to rename temporary history file: %w", err)
	}

	return nil
}

// Delete history
func deleteHistory() error {
	histFile := filepath.Join(appConfig.HistDir, "ani-hsts")
	// Bash: `: >"$histfile"` - truncates the file
	err := os.Truncate(histFile, 0)
	if err != nil && !os.IsNotExist(err) { // Ignore error if file didn't exist
		return fmt.Errorf("failed to delete history file: %w", err)
	}
	return nil
}

// Process history entries for --continue
func processHistoryEntries(entries []HistoryEntry) ([]string, error) {
    // Bash script processes each history entry to find the "next" unwatched episode
    // It calls `episodes_list` for each entry to get the full list, then finds the entry's episode number
    // in that list and gets the *next* one (`sed -n "/^${ep_no}$/{n;p;}"`)
    // If a next episode is found, it formats the output line for the launcher (`ID Title - episode NEXT_EP`)
    // If no next episode, that history entry isn't listed.
    // If the episode number in history is not found in the current episode list, it's updated to the latest episode.

    var runnableEntries []string // Formatted strings for the launcher menu
	var wg sync.WaitGroup
	resultChan := make(chan string) // Channel to collect formatted results

    // Use a Goroutine for each history entry to fetch episode list concurrently
	for _, entry := range entries {
		wg.Add(1)
		go func(e HistoryEntry) {
			defer wg.Done()
            // Get the full episode list for this show
			epListStr, err := listEpisodes(e.ShowID)
			if err != nil {
				log.Printf("Warning: Failed to get episode list for history entry '%s': %v", e.Title, err)
				return // Skip this entry on error
			}
			epList := strings.Split(strings.TrimSpace(epListStr), "\n")

            // Find the episode number from history in the episode list
            // Bash uses `ep_no=$(printf "%s" "$ep_list" | sed -n "/^${ep_no}$/{n;p;}") 2>/dev/null`
            // This gets the *next* episode number after the one in history.
            // If the history episode is the last, or not found, it returns empty, which the Bash script handles by not listing it.
            // Bash also has logic to update the history episode to the latest if the history episode number isn't in the list.

            nextEpNum := ""
            historyEpIndex := -1
            for i, ep := range epList {
                if ep == e.EpisodeNumber {
                    historyEpIndex = i
                    break
                }
            }

            if historyEpIndex != -1 && historyEpIndex+1 < len(epList) {
                 // Found the history episode, and there's a next one
                 nextEpNum = epList[historyEpIndex+1]
            } else {
                 // History episode not found or is the last one.
                 // Bash updates history episode to the latest if the original number isn't in the list.
                 // This implies if historyEpIndex is -1, it should check if the latest episode is different.
                 if historyEpIndex == -1 && len(epList) > 0 && e.EpisodeNumber != epList[len(epList)-1] {
                     // History episode number is outdated/wrong, update history to latest and use latest.
                      latestEpNum := epList[len(epList)-1]
                     // Update history *before* listing it? Bash updates history *after* processing entries into list.
                     // This suggests the update happens implicitly or later.
                     // Let's just find the latest for the list display if the history ep isn't in list.
                     nextEpNum = latestEpNum // List the latest as the episode to watch/continue
                 } else {
                    // History episode is the latest, or list is empty, or history ep index invalid and no latest.
                    // Don't list this entry for continuation.
                    return
                 }
            }

            if nextEpNum != "" {
                // Format the output line for the launcher menu
                // Bash: `ID Title - episode NEXT_EP`
                formattedLine := fmt.Sprintf("%s\t%s - episode %s", e.ShowID, e.Title, nextEpNum)
                 resultChan <- formattedLine
            }

		}(entry)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

    // Collect results from the channel
    for res := range resultChan {
        runnableEntries = append(runnableEntries, res)
    }

    // Bash sorts these results - need to determine sorting criteria.
    // Looks like it sorts numerically or alphabetically by ID/Title?
    // Let's sort alphabetically by title for simplicity.
    sort.Strings(runnableEntries)

    if len(runnableEntries) == 0 {
        return nil, fmt.Errorf("no unwatched series in history!") // Replicate Bash error
    }

	return runnableEntries, nil
}
```

**13. Next Episode Countdown (`time_until_next_ep`)**

Fetches and displays release time information from `animeschedule.net`.

*   **Bash Script Approach**: Uses `curl` to query `animeschedule.net`'s API and then fetches the anime's page. Uses `sed` multiple times to parse the response (which appears to be HTML or a mix) and extract English title, Japanese title, raw release time, sub release time, and status. Formats and prints the output.
*   **Golang Replication**: Use `net/http` to fetch data from `animeschedule.net`. Use string manipulation or `regexp` to parse the relevant information from the HTML/text response, translating the `sed` commands. Print formatted output.
*   **Code Example (Illustrative)**:

```go
func timeUntilNextEp(query string) error {
	// Bash script replaces spaces with + for the query
	encodedQuery := strings.ReplaceAll(query, " ", "+")
	animescheduleAPI := "https://animeschedule.net/api/v3/anime"
	animescheduleSite := "https://animeschedule.net"

	// 1. Query the API to get anime route/ID
	apiURL := fmt.Sprintf("%s?q=%s", animescheduleAPI, encodedQuery)
	resp, err := http.Get(apiURL)
	if err != nil {
		return fmt.Errorf("failed to query animeschedule API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return fmt.Errorf("animeschedule API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read animeschedule API response: %w", err)
	}
	apiResponse := string(bodyBytes)

	// Parse API response to get the anime 'route'
	// Bash sed: `sed 's|"id"|\n|g' | sed -nE 's|.*,"route":"([^"]*)","premier.*|\1|p'`
	var routes []string
	// A simpler way might be JSON parsing if the API returns JSON, but the Bash script implies text processing.
	// Let's use regex mirroring the sed logic.
	reRoute := regexp.MustCompile(`,"route":"([^"]*)","premier`) // Look for route field
	matches := reRoute.FindAllStringSubmatch(apiResponse, -1)
	for _, match := range matches {
		if len(match) > 1 {
			routes = append(routes, match) // Capture group 1 is the route
		}
	}

	if len(routes) == 0 {
		fmt.Printf("No schedule results found for '%s'\n", query)
		return nil // Not an error if nothing is found
	}

	// 2. For each route, fetch the anime page and parse details
	for _, route := range routes {
		animePageURL := fmt.Sprintf("%s/anime/%s", animescheduleSite, route)
		pageResp, err := http.Get(animePageURL)
		if err != nil {
			log.Printf("Warning: Failed to fetch anime page '%s': %v", animePageURL, err)
			continue // Skip this anime on error
		}
		defer pageResp.Body.Close()

		pageBytes, err := ioutil.ReadAll(pageResp.Body)
		if err != nil {
			log.Printf("Warning: Failed to read anime page response: %v", err)
			continue
		}
		pageContent := string(pageBytes)

		// Parse page content using regex replicating sed logic
		// Example sed patterns: `countdown-time-raw" datetime="([^"]*)">.*`, `english-title">([^<]*)<.*`
		dataLines := []string{}

		reRawRelease := regexp.MustCompile(`countdown-time-raw" datetime="([^"]*)">`)
		if match := reRawRelease.FindStringSubmatch(pageContent); len(match) > 1 {
			dataLines = append(dataLines, "Next Raw Release: "+match)
		}

        reSubRelease := regexp.MustCompile(`countdown-time" datetime="([^"]*)">`)
        if match := reSubRelease.FindStringSubmatch(pageContent); len(match) > 1 {
            dataLines = append(dataLines, "Next Sub Release: "+match)
        }

		reEnglishTitle := regexp.MustCompile(`english-title">([^<]*)<`)
		if match := reEnglishTitle.FindStringSubmatch(pageContent); len(match) > 1 {
			dataLines = append(dataLines, "English Title: "+match)
		}

		reJapaneseTitle := regexp.MustCompile(`main-title".*>([^<]*)<`)
		if match := reJapaneseTitle.FindStringSubmatch(pageContent); len(match) > 1 {
			dataLines = append(dataLines, "Japanese Title: "+match)
		}

		// Print extracted data
		for _, line := range dataLines {
			fmt.Println(line)
		}

		// Determine and print status (Ongoing/Finished)
		status := "Ongoing"
		colorCode := "33" // Blue/Cyan color in Bash
		// Bash checks if "Next Raw Release:" was NOT found
		rawReleaseFound := false
		for _, line := range dataLines {
			if strings.HasPrefix(line, "Next Raw Release:") {
				rawReleaseFound = true
				break
			}
		}
		if !rawReleaseFound {
			status = "Finished"
			colorCode = "32" // Green color in Bash
		}
		fmt.Printf("Status: \033[1;%sm%s\033[0m\n", colorCode, status)
		fmt.Println("---") // Separator between results

	}

	return nil // Function exits after printing results
}
```

**14. Script Update (`update_script`)**

Downloads the latest version and applies it using `diff` and `patch`.

*   **Bash Script Approach**: Uses `curl` to fetch the raw script from GitHub. Uses `diff -u` to compare the running script (`$0`) with the downloaded version. If `diff` output is not empty, it pipes it to `patch "$0" -` to apply the changes directly to the running script file.
*   **Golang Replication**: Use `net/http` to download the latest script source. Comparing the running executable's source with the downloaded source and applying a patch is **extremely difficult and not recommended** for a Go binary. A Go binary is compiled; patching its source file won't change the running code.
    *   **Alternative Golang Approach**: The safest way to implement an update in Go is to download the *new binary*, replace the old one, and potentially re-execute the new binary. This is OS-dependent and requires careful handling of file permissions and ensuring the old process is stopped before replacement. Or, if the Go program is distributed as a script (less common), you could potentially download and replace the script file, similar to Bash, but without the `diff`/`patch` complexity, just a direct replacement. Given the nature of Go (compiled), direct source patching is not applicable. Replicating the `diff`/`patch` *logic* (finding differences and applying them) would be a significant project (implementing `diff` and `patch` in Go) and still wouldn't apply to a compiled binary.
*   **Code Example (Illustrative - showing download/compare, noting patching difficulty)**:

```go
func updateScript() error {
	scriptURL := "https://raw.githubusercontent.com/pystardust/ani-cli/master/ani-cli"
	fmt.Println("Checking for updates...")

	// 1. Download the latest script source
	req, err := http.NewRequest("GET", scriptURL, nil)
	if err != nil { return fmt.Errorf("failed to create update request: %w", err) }
	req.Header.Set("User-Agent", userAgent) // Use user agent
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil { return fmt.Errorf("connection error: %w", err) }
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download update, status: %d", resp.StatusCode)
	}

	latestScriptBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil { return fmt.Errorf("failed to read downloaded script: %w", err) }
	latestScript := string(latestScriptBytes)

	// 2. Read the currently running script source
	// This assumes the Go program is being run *as a script* or we have access to its source file.
	// For a compiled Go binary, this step is meaningless for applying source patches.
	// If this *were* a Go script or you had the source file path:
	currentScriptPath := os.Args // Path to the running executable/script
	currentScriptBytes, err := ioutil.ReadFile(currentScriptPath)
	if err != nil {
		// For a compiled binary, just compare version numbers or skip source comparison
		// If we were serious about updating a script, this would be readFile.
		// For a binary, we might embed the version string and just compare that.
        log.Printf("Warning: Cannot read running script source at %s: %v. Cannot perform source diff.", currentScriptPath, err)
		// Fallback: just compare version string? Need to extract version from downloaded source.
		// This adds complexity. Let's focus on the Bash script's source-patching approach first.
		return fmt.Errorf("failed to read running script source: %w", err)
	}
	currentScript := string(currentScriptBytes)


	// 3. Compare current and latest script sources using diff logic
	// Replicating `diff -u` output and logic in Go is complex.
	// Instead of generating diff output, we can simply check if the content is different.
	if currentScript == latestScript {
		fmt.Println("Script is up to date :)")
		return nil // Exit successfully if up to date
	}

	fmt.Println("Update available. Attempting to apply update...")

	// 4. Apply the update using patch logic
	// This step is the *most difficult* to replicate natively for a compiled Go binary.
	// The Bash script pipes `diff` output to `patch`.
	// A compiled Go program cannot patch its own running code or source file easily.
	// Options (outside sources):
	// A) If distributing as a script: Write `latestScript` to `currentScriptPath`. Requires permission.
	// B) If distributing as a binary: Download new binary, replace old binary, potentially re-exec. Complex and OS-specific.
	// C) Skip this feature in the Go port.

	// **Illustrative (Simplified - not actual patching)**
	// For a simple script replacement (if applicable):
	// err = ioutil.WriteFile(currentScriptPath, latestScriptBytes, 0755) // Write new content
	// if err != nil { return fmt.Errorf("failed to write updated script: %w", err) }
	// fmt.Println("Script has been updated")

	// For a compiled binary, you might do something like:
	// downloadNewBinary("my-go-app", "latest-version") // Custom function to download binary
	// replaceRunningBinary("my-go-app", "new-binary-path") // Custom OS-specific function
	// exec.Execute("my-go-app", os.Args) // Re-execute the new binary

	// Since we cannot replicate the `patch` logic directly on a Go binary's source,
	// we must explicitly state this limitation based on the provided sources.

	return fmt.Errorf("update logic (diff/patch) cannot be directly replicated for a compiled Go binary based on source script methodology")
	// Bash script dies if patch fails
}
```
*Note: The update mechanism of the Bash script is highly specific to how shell scripts are often updated in place. Replicating this for a compiled Go binary fundamentally changes the update process.*

**15. Main Logic Flow**

The script's `main` section handles command-line arguments, sets up configuration, checks dependencies, then enters the core logic which is either searching/playing or performing a specific action like showing history/logs/version/update. The main interactive playback loop runs after initial selection.

*   **Bash Script Approach**: A large `case "$search"` block determines the initial action (history, nextep, scrape). After selecting an anime and episode, it calls `play`. After `play` returns (for a single episode), it enters a `while` loop showing a menu for navigation/actions.
*   **Golang Replication**: The `main` function will orchestrate these steps. After loading config, parsing args, and checking deps, use a `switch` statement based on the `appConfig.SearchMode`. Implement functions for each mode (`runHistory`, `runNextep`, `runSearchPlay`). The playback menu loop will be a separate function called after the initial `play`.

```go
// Inside func main() after checkDependencies():

func runMainLogic() {
	switch appConfig.SearchMode {
	case "history":
		err := runHistory() // Implement runHistory
		if err != nil { die("History failed: %v", err) }

	case "nextep":
		if appConfig.Query == "" { // Bash reads query if not provided
            // In Go, we could prompt the user for the query here if empty,
            // or require it as an argument for nextep mode.
            // Let's require it for now to simplify.
             if len(flag.Args()) == 0 { // If query wasn't passed as arg
                 die("missing query for --nextep-countdown")
             }
             appConfig.Query = strings.Join(flag.Args(), " ")
		}
		err := timeUntilNextEp(appConfig.Query)
		if err != nil { die("Next episode countdown failed: %v", err) }
         os.Exit(0) // Bash exits after nextep

	case "scrape":
		err := runSearchPlay() // Implement runSearchPlay
		if err != nil { die("Search and Play failed: %v", err) }

	// Handle other modes/flags that cause early exit
    // e.g., --version, --help, --delete, --logview
    case "version_info": // Custom internal mode based on flag
         versionInfo() // Implement versionInfo
         os.Exit(0)
    case "help_info": // Custom internal mode based on flag
         helpInfo() // Implement helpInfo
         os.Exit(0)
    case "delete_history": // Custom internal mode based on flag
         err := deleteHistory()
         if err != nil { die("Failed to delete history: %v", err) }
         fmt.Println("History deleted.")
         os.Exit(0)
    case "log_view": // Custom internal mode based on flag
         viewLogs() // Implement viewLogs (OS-specific)
         os.Exit(0)
    case "update_script": // Custom internal mode based on flag
         err := updateScript()
         if err != nil { die("Update failed: %v", err) }
         // Note: updateScript implementation in Go might not match Bash.
         os.Exit(0)


	default:
		die("Unknown search mode or action: %s", appConfig.SearchMode)
	}
}

// Implement functions for main search/play flows

func runSearchPlay() error {
	// Search for anime
	// If query is empty, prompt user (unless external menu is used)
	if appConfig.Query == "" && !appConfig.UseExternalMenu {
		fmt.Print("\33[2K\r\033[1;36mSearch anime: \033[0m")
		reader := bufio.NewReader(os.Stdin)
		q, _ := reader.ReadString('\n')
		appConfig.Query = strings.TrimSpace(q)
		if appConfig.Query == "" { os.Exit(1); } // Exit if no query entered
	} else if appConfig.Query == "" && appConfig.UseExternalMenu {
        // Bash uses external menu with empty input to prompt for search query
        // Need to pass empty string to launcher/selectFromList
        q, err := launcher("Search anime: ", "", appConfig.MultiSelectionFlag != "")
        if err != nil || q == "" { os.Exit(1); } // Exit if cancelled or empty
        appConfig.Query = q
    }

	// Bash replaces spaces with + for search query
	searchQuery := strings.ReplaceAll(appConfig.Query, " ", "+")

	animeListStrings, err := searchAnime(searchQuery)
	if err != nil { return fmt.Errorf("anime search failed: %w", err) }

	// Select anime from results
	// Bash uses nth/launcher for selection
	// Needs to handle --select-nth flag ($index)
	selectedAnimeString := ""
	if appConfig.SelectedIndex != "" { // Handle index flag
        // Select the Nth line from animeListStrings
        index, atoiErr := strconv.Atoi(appConfig.SelectedIndex)
        if atoiErr != nil || index <= 0 || index > len(animeListStrings) {
             return fmt.Errorf("invalid index '%s'", appConfig.SelectedIndex)
        }
        selectedAnimeString = animeListStrings[index-1] // 1-based index

	} else { // Use interactive selection
        // Bash adds line numbers before passing to nth
        // Need to format animeListStrings with line numbers
        var numberedList []string
        for i, line := range animeListStrings {
            numberedList = append(numberedList, fmt.Sprintf("%2d %s", i+1, line)) // Replicate numbering format
        }
        listWithNumbers := strings.Join(numberedList, "\n")

		selectedLine, err := launcher("Select anime: ", listWithNumbers, appConfig.MultiSelectionFlag != "") // Pass formatted list
		if err != nil { return fmt.Errorf("anime selection cancelled or failed: %w", err) }
        // Bash nth extracts ID from the selected line with numbering
        // Example: `cut -f1` on " 1 ID Title (Episodes)"
        // Need to parse the number, then find the original string in animeListStrings, then extract ID.
        parts := strings.Fields(selectedLine)
        if len(parts) > 0 {
            selectedIndex, atoiErr := strconv.Atoi(parts)
            if atoiErr == nil && selectedIndex > 0 && selectedIndex <= len(animeListStrings) {
                 selectedAnimeString = animeListStrings[selectedIndex-1] // Get original string using 1-based index
            } else {
                 return fmt.Errorf("failed to parse selected anime index from launcher output")
            }
        }
	}


	// Extract ID, Title from selected anime string
	// Bash: `cut -f1` for ID, `cut -f2` for Title
	animeParts := strings.Split(selectedAnimeString, "\t") // Assume tab-separated format from searchAnime
	if len(animeParts) < 2 { return fmt.Errorf("invalid format for selected anime string") }
	animeID := animeParts
	animeTitle := animeParts // Includes episode count in parentheses

	// Get episode list for the selected anime
	epListStr, err := listEpisodes(animeID)
	if err != nil { return fmt.Errorf("failed to get episode list: %w", err) }
    // Store episode list for playback loop
    // appConfig.CurrentEpisodeList = strings.Split(strings.TrimSpace(epListStr), "\n")


	// Select episode(s) to play
	// If ep_no is not set by args, prompt user
	if appConfig.EpisodeNumber == "" {
		selectedEpisodeStr, err := launcher("Select episode: ", epListStr, appConfig.MultiSelectionFlag != "") // Pass full list
		if err != nil { return fmt.Errorf("episode selection cancelled or failed: %w", err) }
		appConfig.EpisodeNumber = selectedEpisodeStr // Can be single number or range/list
	}

    // Determine ani-skip query title
    // Bash: `ani-skip -q "${skip_title:-${title}}"`
    // This needs to happen before playback loop if skip_intro is enabled.
    appConfig.MalID = "" // Reset MalID
    if appConfig.SkipIntro {
        skipQuery := animeTitle // Default skip query is anime title
        if appConfig.SkipTitle != "" {
            skipQuery = appConfig.SkipTitle // Override with --skip-title arg
        }
        // Call ani-skip to get MAL ID
        // Need to handle exec.Command for ani-skip here or store the skipQuery for playEpisode to use.
        // Let's fetch MAL ID here as Bash does it before the main loop
        cmd := exec.Command("ani-skip", "-q", skipQuery)
        out, aniSkipErr := cmd.Output()
        if aniSkipErr != nil {
            log.Printf("Warning: Failed to get MAL ID from ani-skip for '%s': %v", skipQuery, aniSkipErr)
            // Continue without skip_intro or with manual skip? Bash just clears $skip_flag
            appConfig.SkipIntro = false // Disable skip if we can't get MAL ID
        } else {
             appConfig.MalID = strings.TrimSpace(string(out)) // Store MAL ID
             if appConfig.MalID == "" {
                log.Printf("Warning: ani-skip found no MAL ID for '%s'. Disabling skip.", skipQuery)
                 appConfig.SkipIntro = false // Disable if ani-skip returned empty
             } else {
                 log.Printf("Got MAL ID %s for skipping intro.", appConfig.MalID)
             }
        }
    }

    // Store info needed for playback and menu loop
    appConfig.CurrentShowID = animeID
    appConfig.CurrentShowTitle = animeTitle

	// Play the selected episode(s)
    // The play function handles ranges and calls play_episode
	err = play(animeID, animeTitle, appConfig.EpisodeNumber) // Pass the episode number string/range
	if err != nil { return fmt.Errorf("playback failed: %w", err) }

    // If playback mode is download or debug, exit after play
	if appConfig.Player == "download" || appConfig.Player == "debug" {
		os.Exit(0)
	}

    // Enter the main playback loop menu
	// Pass necessary info to the loop function
	err = runPlaybackLoop(animeID, animeTitle, appConfig.EpisodeNumber) // Start menu loop with the episode just played (or first in range)
    if err != nil { return fmt.Errorf("playback loop failed: %w", err) }

	return nil // Main search/play sequence completed
}

func runHistory() error {
    // Read and process history entries to find what to continue watching
    histEntries, err := readHistory() // Use readHistory implemented earlier
    if err != nil { return fmt.Errorf("failed to read history: %w", err) }

    runnableListStrings, err := processHistoryEntries(histEntries) // Use processHistoryEntries implemented earlier
    if err != nil { return fmt.Errorf("failed to process history: %w", err) } // This includes "No unwatched series" error

    // Select anime from the runnable history list
    // Needs to handle --select-nth flag ($index)
    selectedRunnableString := ""
	if appConfig.SelectedIndex != "" { // Handle index flag
        // Select the Nth line from runnableListStrings
        index, atoiErr := strconv.Atoi(appConfig.SelectedIndex)
        if atoiErr != nil || index <= 0 || index > len(runnableListStrings) {
             return fmt.Errorf("invalid index '%s'", appConfig.SelectedIndex)
        }
        selectedRunnableString = runnableListStrings[index-1] // 1-based index

	} else { // Use interactive selection
        // Bash adds line numbers before passing to nth
        var numberedList []string
        for i, line := range runnableListStrings {
            numberedList = append(numberedList, fmt.Sprintf("%2d %s", i+1, line)) // Replicate numbering format
        }
        listWithNumbers := strings.Join(numberedList, "\n")

		selectedLine, err := launcher("Select anime: ", listWithNumbers, appConfig.MultiSelectionFlag != "") // Pass formatted list
		if err != nil { return fmt.Errorf("history selection cancelled or failed: %w", err) }

        // Bash nth extracts ID from the selected line with numbering
        // Example: `cut -f1` on " 1 ID Title - episode EP_NUM"
        // Need to parse the number, then find the original string, then extract ID.
        parts := strings.Fields(selectedLine)
        if len(parts) > 0 {
            selectedIndex, atoiErr := strconv.Atoi(parts)
            if atoiErr == nil && selectedIndex > 0 && selectedIndex <= len(runnableListStrings) {
                 selectedRunnableString = runnableListStrings[selectedIndex-1] // Get original string
            } else {
                 return fmt.Errorf("failed to parse selected history index from launcher output")
            }
        }
	}


    // Extract ID, Title, and Episode Number from selected history string
    // Bash: `id=$(...| cut -f1)`, `title=$(...| cut -f2 | sed 's/ - episode.*//')`, `ep_no=$(...| sed -nE 's/.*- episode (.+)$/\1/p')`
    historyParts := strings.Split(selectedRunnableString, "\t") // Assume tab-separated from processHistoryEntries
     if len(historyParts) < 2 { return fmt.Errorf("invalid format for selected history string") }
    animeID := historyParts
    titleAndEp := historyParts // Format: "Title - episode EP_NUM"

    // Extract title and episode number from the second part
    reTitleEp := regexp.MustCompile(`^(.*) - episode (.+)$`)
    match := reTitleEp.FindStringSubmatch(titleAndEp)
    if len(match) < 3 { return fmt.Errorf("invalid format for history title/episode string") }
    animeTitle := match
    episodeNum := match

    // Set config values for playback
    appConfig.CurrentShowID = animeID
    appConfig.CurrentShowTitle = animeTitle
    // Start playback loop with the determined episode number
    err = runPlaybackLoop(animeID, animeTitle, episodeNum)
    if err != nil { return fmt.Errorf("history playback failed: %w", err) }

    return nil
}

// Implement simple functions for --version, --help, --logview
func versionInfo() {
    fmt.Println(appConfig.Version)
}

func helpInfo() {
    // Print the help message string from sources
    helpText := `
Usage:
%s [options] [query]
%s [query] [options]
%s [options] [query] [options]

Options:
-c, --continue             Continue watching from history
-d, --download             Download the video instead of playing it
-D, --delete               Delete history
-l, --logview              Show logs
-s, --syncplay             Use Syncplay to watch with friends
-S, --select-nth <num>     Select nth entry
-q, --quality <quality>    Specify the video quality
-v, --vlc                  Use VLC to play the video
-V, --version              Show the version of the script
-h, --help                 Show this help message and exit
-e, --episode, -r, --range <num/range> Specify the number of episodes to watch
--dub                      Play dubbed version
--rofi                     Use rofi instead of fzf for the interactive menu
--skip                     Use ani-skip to skip the intro of the episode (mpv only)
--no-detach                Don't detach the player (useful for in-terminal playback, mpv only)
--exit-after-play          Exit the player, and return the player exit code (useful for non interactive scenarios, mpv only)
--skip-title <title>       Use given title as ani-skip query
-N, --nextep-countdown     Display a countdown to the next episode
-U, --update               Update the script

Some example usages:
%s -q 720p banana fish
%s --skip --skip-title "one piece" -S 2 one piece
%s -d -e 2 cyberpunk edgerunners
%s --vlc cyberpunk edgerunners -q 1080p -e 4
%s blue lock -e 5-6
%s -e "5 6" blue lock
`
    // Bash uses ${0##*/} to get the script name
    scriptName := filepath.Base(os.Args)
    fmt.Printf(helpText, scriptName, scriptName, scriptName, scriptName, scriptName, scriptName, scriptName, scriptName, scriptName)
}

func viewLogs() {
    // This is OS specific in the Bash script
    // Bash: Darwin uses `log show --predicate 'process == "logger"'`
    // Bash: Linux uses `journalctl -t ani-cli`
    // Need to detect OS and run the appropriate command using exec.Command
    osName := getOSInfo()
    var cmd *exec.Cmd
    switch osName {
    case "Darwin":
        cmd = exec.Command("log", "show", "--predicate", `process == "logger"`)
    case "Linux":
        cmd = exec.Command("journalctl", "-t", "ani-cli")
    default:
        die("Logger not implemented for your platform")
    }
    cmd.Stdout = os.Stdout
    cmd.Stderr = os.Stderr
    if err := cmd.Run(); err != nil {
        log.Printf("Failed to show logs: %v", err)
    }
}
```

**Summary and Considerations:**

*   The most challenging aspects to port are the extensive text processing pipelines using `sed` and `grep` in the Bash script, particularly in `get_links` and `provider_init`. Replicating this precisely in Go using `regexp` and `strings` requires careful translation of the complex regexes and sequence of operations.
*   The `update_script` functionality using `diff` and `patch` is not directly portable to a compiled Go binary in the same way. An alternative binary replacement method would be needed, which is outside the scope of the provided Bash source's method.
*   The interactive UI using `fzf` or `rofi` can be replicated by shelling out to these commands using `os/exec`, piping input, and capturing output. A native Go TUI would be an alternative (outside source) but more complex than directly mimicking the Bash script's approach. The `nth` function's input/output parsing needs careful implementation in Go based on the expected format in different contexts.
*   OS-specific commands like `am start` for Android, `log show` for Darwin, and `journalctl` for Linux need conditional execution based on the detected OS (`runtime.GOOS`).
*   Error handling and cancellation logic (like user cancelling `fzf`/`rofi`) need to be robustly implemented using Go's error handling. The Bash script often exits (`exit 1`) on error or cancellation; the Go version should handle this appropriately.
*   Concurrency (`&` and `wait` in Bash) maps well to Goroutines and `sync.WaitGroup` in Go, particularly for fetching links from multiple providers concurrently.
*   History management requires careful file I/O and parsing/formatting of the tab-separated data.

This detailed outline with code examples provides a solid foundation for translating the Bash script's functionality into a Golang application, highlighting the key methods and challenges for each step based directly on the provided source material. Remember that for the most complex parsing and the update function, you will need to make design choices about how closely you want to replicate the Bash script's specific techniques versus using more idiomatic (and potentially more robust) Go approaches (like a dedicated M3U8 parser, or a different update strategy).