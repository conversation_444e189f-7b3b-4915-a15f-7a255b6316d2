[{"name": "cyberpunk", "primary": "#00FFFF", "background": "#001122", "prompt": "🌌 NeoAnimeOS > ", "header": ["🌠══════════════════════════════════════════════════════════════🌠", "║                ✨ STARLINK-NEKOSAKI NETWORK ✨                ║", "║              🌃 SHINJUKU-MECHA CORE TERMINAL 🌃               ║", "║                                                               ║", "║  🌟 SYSTEM STATUS: FULL SYNC - KAIJU LEVEL 🌟                 ║", "║  👾 SENPAI STATUS: AWAKENED & HYPERLINKED 👾                  ║", "║  🎌 MISSION: ULTIMATE ANIME DATACAST PROTOCOL 🎌              ║", "🌠══════════════════════════════════════════════════════════════🌠", "", "Enter 'help' to unlock command matrix... 💿", ""]}, {"name": "matrix", "primary": "#00FF00", "background": "#000000", "prompt": "neo@matrix:~$ ", "header": ["╔══════════════════════════════════════════════════════════════╗", "║                    THE MATRIX - ZION MAINFRAME               ║", "║                      ANIME PROTOCOL v3.0                     ║", "║                                                              ║", "║  STATUS: CONNECTED TO THE SOURCE                             ║", "║  REALITY: SIMULATED                                          ║", "║  MISSION: FOLLOW THE WHITE RABBIT                            ║", "╚══════════════════════════════════════════════════════════════╝", "", "Wake up, <PERSON>... Type 'help' to see the truth.", ""]}, {"name": "aliens", "primary": "#FFAA00", "background": "#110800", "prompt": "USCSS NOSTROMO > ", "header": ["╔══════════════════════════════════════════════════════════════╗", "║                    WEYLAND-YUTANI CORP                       ║", "║                  USCSS NOSTROMO TERMINAL                     ║", "║                                                              ║", "║  SYSTEM STATUS: OPERATIONAL                                  ║", "║  CREW STATUS: ACTIVE                                         ║", "║  MISSION: ANIME STREAMING PROTOCOL                           ║", "╚══════════════════════════════════════════════════════════════╝", "", "Type 'help' for available commands...", ""]}, {"name": "vaporwave", "primary": "#FF00FF", "background": "#220033", "prompt": "🌴 AESTHETIC > ", "header": ["▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "█                    ＡＮＩＭＥｗａｖｅ ８０                   █", "█                  ～ DIGITAL DREAMS ～                        █", "█                                                              █", "█  STATUS: ａｅｓｔｈｅｔｉｃ                                  █", "█  VIBE: ｍａｘｉｍｕｍ                                        █", "█  MISSION: ｒｅｔｒｏ　ａｎｉｍｅ　ｓｔｒｅａｍｉｎｇ         █", "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "", "Type 'help' for commands... 🌸", ""]}, {"name": "tron", "primary": "#00AAFF", "background": "#000011", "prompt": "USER@GRID > ", "header": ["┌────────────────────────────────────────────────────────────┐", "│                    TRON LEGACY SYSTEM                      │", "│                   ANIME GRID PROTOCOL                      │", "│                                                            │", "│  GRID STATUS: ACTIVE                                       │", "│  PROGRAMS: RUNNING                                         │", "│  MISSION: FIGHT FOR THE USERS                              │", "└────────────────────────────────────────────────────────────┘", "", "End of line. Type 'help' for commands.", ""]}, {"name": "blade", "primary": "#FF4444", "background": "#110000", "prompt": "TYRELL CORP > ", "header": ["╔══════════════════════════════════════════════════════════════╗", "║                    TYRELL CORPORATION                        ║", "║                  BLADE RUNNER TERMINAL                       ║", "║                                                              ║", "║  SYSTEM: NEXUS-6 COMPATIBLE                                  ║", "║  STATUS: MORE HUMAN THAN HUMAN                               ║", "║  MISSION: ANIME REPLICANT PROTOCOL                           ║", "╚══════════════════════════════════════════════════════════════╝", "", "I've seen things you people wouldn't believe... Type 'help'.", ""]}, {"name": "ghost", "primary": "#9966FF", "background": "#110022", "prompt": "SECTION-9 > ", "header": ["╔══════════════════════════════════════════════════════════════╗", "║                    PUBLIC SECURITY SECTION 9                 ║", "║                  GHOST IN THE SHELL NETWORK                  ║", "║                                                              ║", "║  CYBERBRAIN: CONNECTED                                       ║", "║  GHOST: AUTHENTICATED                                        ║", "║  MISSION: ANIME SURVEILLANCE PROTOCOL                        ║", "╚══════════════════════════════════════════════════════════════╝", "", "What is identity? Type 'help' to explore the net.", ""]}, {"name": "a<PERSON>ra", "primary": "#FF0066", "background": "#220011", "prompt": "NEO-TOKYO > ", "header": ["████████████████████████████████████████████████████████████████", "██                    NEO-TOKYO 2019                          ██", "██                  AKIRA PROJECT TERMINAL                    ██", "██                                                            ██", "██  PSYCHIC LEVEL: MAXIMUM                                    ██", "██  CAPSULE STATUS: ACTIVE                                    ██", "██  MISSION: KANEDA! WHAT DO YOU SEE?                         ██", "████████████████████████████████████████████████████████████████", "", "TETSUOOOOO! Type 'help' for commands.", ""]}]