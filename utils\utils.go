package utils

import (
	"os"
	"strings"
)

// getEnvWithDefault returns environment variable value or default if not set
func getEnvWithDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// getBoolEnvWithDefault returns bool environment variable value or default if not set
func getBoolEnvWithDefault(key string, defaultValue bool) bool {
	if value, exists := os.LookupEnv(key); exists {
		return value == "1" || strings.ToLower(value) == "true"
	}
	return defaultValue
}
