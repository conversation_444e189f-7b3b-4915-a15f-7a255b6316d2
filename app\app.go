package app

import (
	"flag"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"sync"

	"github.com/stl3/go-ani/api"
	"github.com/stl3/go-ani/history"
	"github.com/stl3/go-ani/player"
	"github.com/stl3/go-ani/url"
)

// Run runs the application
func Run() error {
	// Parse flags
	var (
		query       = flag.String("query", "", "Search query")
		translation = flag.String("t", "SUB", "Translation type (SUB/DUB)")
		quality     = flag.String("quality", "best", "Video quality (best/worst/number)")
		episode     = flag.String("e", "", "Episode number or range")
		historyFile = flag.String("f", "history.json", "History file path")
		watchlist   = flag.Bool("wl", false, "Show watchlist")
		showHistory = flag.Bool("h", false, "Show history")
		noDetach    = flag.Bool("nd", false, "Do not detach player")
		skipIntro   = flag.Bool("si", false, "Skip intro")
		skipTitle   = flag.String("st", "", "Skip title")
	)
	flag.Parse()

	// Initialize history manager
	historyManager := history.NewManager(*historyFile)

	// Process watchlist command
	if *watchlist {
		entries, err := historyManager.ReadEntries()
		if err != nil {
			fmt.Printf("Error reading watchlist: %v\n", err)
			return err
		}
		fmt.Println("\n=== Watchlist ===")
		for _, entry := range entries {
			fmt.Printf("- %s\n", entry.Title)
		}
		return nil
	}

	// Process history command
	if *showHistory {
		entries, err := historyManager.ReadEntries()
		if err != nil {
			fmt.Printf("Error reading history: %v\n", err)
			return err
		}
		fmt.Println("\n=== History ===")
		for _, entry := range entries {
			fmt.Printf("- %s (Episode %s)\n", entry.Title, entry.EpisodeNo)
		}
		return nil
	}

	// Initialize API client
	apiClient := api.NewClient()

	// Initialize URL processor
	urlProcessor := url.NewProcessor(*quality)

	// Initialize player
	playerConfig := player.Config{
		NoDetach:  *noDetach,
		SkipIntro: *skipIntro,
		SkipTitle: *skipTitle,
		Quality:   *quality,
	}

	player, err := player.NewPlayer("mpv", playerConfig)
	if err != nil {
		fmt.Printf("Error initializing player: %v\n", err)
		return err
	}

	// Search for anime
	if *query == "" {
		fmt.Print("\nEnter anime name: ")
		fmt.Scanln(query)
	} else {
		// Use the provided query
		fmt.Printf("Searching for: %s\n", *query)
	}

	shows, err := apiClient.SearchAnime(*query, *translation)
	if err != nil {
		fmt.Printf("Error searching for anime: %v\n", err)
		return err
	}

	if len(shows) == 0 {
		fmt.Println("No results found")
		return nil
	}

	// Display search results
	fmt.Println("\nSearch Results:")
	for i, show := range shows {
		fmt.Printf("%d. %s (Episodes: %d)\n", i+1, show.Title, show.Episodes)
	}

	// Get user selection
	var selection int
	fmt.Print("\nSelect an anime (number): ")
	fmt.Scanln(&selection)

	if selection < 1 || selection > len(shows) {
		fmt.Println("Invalid selection")
		return nil
	}

	selectedShow := shows[selection-1]

	// Get episode list
	episodes, err := apiClient.GetEpisodesList(selectedShow.ID)
	if err != nil {
		fmt.Printf("Error getting episode list: %v\n", err)
		return err
	}

	if len(episodes) == 0 {
		fmt.Println("No episodes found")
		return nil
	}

	// Get MAL ID for skip intro if enabled
	var malID string
	if *skipIntro {
		skipQuery := selectedShow.Name
		if *skipTitle != "" {
			skipQuery = *skipTitle
		}

		if skipCmd, err := exec.LookPath("go-aniskip.exe"); err == nil {
			cmd := exec.Command(skipCmd, "-q", skipQuery)
			out, err := cmd.Output()
			if err != nil {
				fmt.Printf("Warning: Failed to get MAL ID from go-aniskip: %v\n", err)
			} else {
				malID = strings.TrimSpace(string(out))
				if malID == "" {
					fmt.Printf("Warning: go-aniskip found no MAL ID for '%s'\n", skipQuery)
				}
			}
		}
	}

	// If specific episode provided, use it
	var episodeNums []int
	if *episode != "" {
		// Parse episode range directly
		epNum, err := strconv.Atoi(*episode)
		if err != nil {
			fmt.Printf("Invalid episode format: %v\n", err)
			return err
		}
		episodeNums = []int{epNum}
	} else {
		// Get user episode selection
		var epSel string
		fmt.Print("\nEnter episode number or range (e.g., 1-3, 1,2,3): ")
		fmt.Scanln(&epSel)

		// Parse episode number directly
		epNum, err := strconv.Atoi(epSel)
		if err != nil {
			fmt.Printf("Invalid episode format: %v\n", err)
			return err
		}
		episodeNums = []int{epNum}
	}

	// Process episodes
	var wg sync.WaitGroup
	for _, epNum := range episodeNums {
		wg.Add(1)
		go func(epNum int) {
			defer wg.Done()

			episodeString := fmt.Sprintf("Episode %d", epNum)
			episodeSources, err := apiClient.GetEpisodeSources(selectedShow.ID, episodeString, *translation)
			if err != nil {
				fmt.Printf("Error getting episode %d sources: %v\n", epNum, err)
				return
			}

			if len(episodeSources) == 0 {
				fmt.Printf("No sources found for episode %d\n", epNum)
				return
			}

			// Get the first source URL (we'll process it later)
			source := episodeSources[0]

			// Process URL
			processedURL, err := urlProcessor.ProcessURL(source.URL)
			if err != nil {
				fmt.Printf("Error processing episode %d URL: %v\n", epNum, err)
				return
			}

			// Play episode
			if err := player.Play(processedURL, selectedShow.Title, epNum, historyManager); err != nil {
				fmt.Printf("Error playing episode %d: %v\n", epNum, err)
				return
			}

			// Mark episode as watched
			if err := historyManager.MarkEpisodeWatched(selectedShow.ID, selectedShow.Title, epNum); err != nil {
				fmt.Printf("Error marking episode as watched: %v\n", err)
			}
		}(epNum)
	}

	wg.Wait()

	return nil
}
