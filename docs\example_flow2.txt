$ bash -x ./ani-cli -q 1080p "mushoku tensei"
+ version_number=4.10.1
+ agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ allanime_refr=https://allmanga.to
+ allanime_base=allanime.day
+ allanime_api=https://api.allanime.day
+ mode=sub
+ download_dir=.
+ log_episode=1
+ quality=best
+ case "$(uname -a | cut -d " " -f 1,3-)" in
++ uname -a
++ cut -d ' ' -f 1,3-
+ player_function=mpv.exe
+ no_detach=0
+ exit_after_play=0
+ use_external_menu=0
+ external_menu_normal_window=0
+ skip_intro=0
+ skip_title=
+ '[' -t 0 ']'
+ hist_dir=/c/Users/<USER>/.local/state/ani-cli
+ '[' '!' -d /c/Users/<USER>/.local/state/ani-cli ']'
+ histfile=/c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' '!' -f /c/Users/<USER>/.local/state/ani-cli/ani-hsts ']'
+ search=scrape
+ '[' 3 -gt 0 ']'
+ case "$1" in
+ '[' 3 -lt 2 ']'
+ quality=1080p
+ shift
+ shift
+ '[' 1 -gt 0 ']'
+ case "$1" in
++ printf %s ' mushoku tensei'
++ sed 's|^ ||;s| |+|g'
+ query=mushoku+tensei
+ shift
+ '[' 0 -gt 0 ']'
+ '[' 0 = 0 ']'
+ multi_selection_flag=-m
+ '[' 0 = 1 ']'
+ '[' 0 = 1 ']'
+ printf '\33[2K\r\033[1;34mChecking dependencies...\033[0m\n'
Checking dependencies...
+ dep_ch curl sed grep
+ for dep in "$@"
+ command -v curl
+ for dep in "$@"
+ command -v sed
+ for dep in "$@"
+ command -v grep
+ '[' 0 = 1 ']'
+ dep_ch fzf
+ for dep in "$@"
+ command -v fzf
+ case "$player_function" in
+ dep_ch mpv.exe
+ for dep in "$@"
+ command -v mpv.exe
+ case "$search" in
+ '[' 0 = 0 ']'
+ '[' -z mushoku+tensei ']'
+ '[' scrape = nextep ']'
++ printf %s mushoku+tensei
++ sed 's| |+|g'
+ query=mushoku+tensei
++ search_anime mushoku+tensei
++ search_gql='query( $search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType ) { shows( search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin ) { edges { _id name availableEpisodes __typename } }}'
++ curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"search":{"allowAdult":false,"allowUnknown":false,"query":"mushoku+tensei"},"limit":40,"page":1,"translationType":"sub","countryOrigin":"ALL"}' --data-urlencode 'query=query( $search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType ) { shows( search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin ) { edges { _id name availableEpisodes __typename } }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|Show|\
| g'
++ sed -nE 's|.*_id":"([^"]*)","name":"(.+)",.*sub":([1-9][^,]*).*|\1   \2 (\3 episodes)|p'
++ sed 's/\\"//g'
+ anime_list='QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+ '[' -z 'QusmPJR29sg3G35CB     Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
+ '[' '' -eq '' ']'
+ '[' -z '' ']'
++ printf %s 'QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ nl -w 2
++ sed 's/^[[:space:]]//'
++ nth 'Select anime: '
+++ cat -
++ stdin='1     QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ '[' -z '1    QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
+++ printf '%s\n' '1    QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=6
++ '[' 6 -eq 1 ']'
++ prompt='Select anime: '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s '1        QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Select anime: '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Select anime: '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Select anime: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=6
+++ printf %s 6
+++ head -n1
++ line_start=6
+++ printf %s 6
+++ tail -n1
++ line_end=6
++ '[' -n 6 ']'
++ '[' 6 = 6 ']'
++ printf %s '1 QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ grep -E '^6($|[[:space:]])'
++ cut -f2,3
+ result='ed6654HMukxd8TKJ7     Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+ '[' -z 'ed6654HMukxd8TKJ7     Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
++ printf %s 'ed6654HMukxd8TKJ7 Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ cut -f2
+ title='Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ printf %s 'Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ cut '-d(' -f1
++ tr -d '[:punct:]'
+ allanime_title='Mushoku Tensei Isekai Ittara Honki Dasu '
++ printf %s 'ed6654HMukxd8TKJ7 Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ cut -f1
+ id=ed6654HMukxd8TKJ7
++ episodes_list ed6654HMukxd8TKJ7
++ episodes_list_gql='query ($showId: String!) { show( _id: $showId ) { _id availableEpisodesDetail }}'
++ curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"ed6654HMukxd8TKJ7"}' --data-urlencode 'query=query ($showId: String!) { show( _id: $showId ) { _id availableEpisodesDetail }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed -nE 's|.*sub":\[([0-9.",]*)\].*|\1|p'
++ sed 's|,|\
|g; s|"||g'
++ sort -n -k 1
+ ep_list='1
2
3
4
5
6
7
8
9
10
11'
+ '[' -z '' ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
++ nth 'Select episode: ' -m
+++ cat -
++ stdin='1
2
3
4
5
6
7
8
9
10
11'
++ '[' -z '1
2
3
4
5
6
7
8
9
10
11' ']'
+++ printf '%s\n' '1
2
3
4
5
6
7
8
9
10
11'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=11
++ '[' 11 -eq 1 ']'
++ prompt='Select episode: '
++ multi_flag=
++ '[' 2 -ne 1 ']'
++ shift
++ multi_flag=-m
+++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher -m 'Select episode: '
+++ '[' 0 = 0 ']'
+++ '[' -z -m ']'
+++ '[' 0 = 0 ']'
+++ fzf -m --reverse --cycle --prompt 'Select episode: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=1
+++ printf %s 1
+++ head -n1
++ line_start=1
+++ printf %s 1
+++ tail -n1
++ line_end=1
++ '[' -n 1 ']'
++ '[' 1 = 1 ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
++ grep -E '^1($|[[:space:]])'
++ cut -f2,3
+ ep_no=1
+ '[' -z 1 ']'
+ '[' 0 = 1 ']'
+ tput elu1
+ tput sc
+ play
++ printf %s 1
++ grep -Eo '^(-1|[0-9]+(\.[0-9]+)?)'
+ start=1
++ printf %s 1
++ grep -Eo '(-1|[0-9]+(\.[0-9]+)?)$'
+ end=1
+ '[' 1 = -1 ']'
+ '[' -z 1 ']'
+ '[' 1 = 1 ']'
+ unset start end
+ '[' '' = -1 ']'
++ printf '%s\n' 1
++ wc -l
++ tr -d '[:space:]'
+ line_count=1
+ '[' 1 '!=' 1 ']'
+ '[' -n '' ']'
+ play_episode
+ '[' 1 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ command -v logger
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ get_episode_url
+ episode_embed_gql='query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}'
++ curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"ed6654HMukxd8TKJ7","translationType":"sub","episodeString":"1"}' --data-urlencode 'query=query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ tr '{}' '\n'
++ sed 's|\\u002F|\/|g;s|\\||g'
++ sed -nE 's|.*sourceUrl":"--([^"]*)".*sourceName":"([^"]*)".*|\2 :\1|p'
+ resp='S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ mktemp -d
+ cache_dir=/tmp/tmp.pZnjpASWde
+ providers='1 2 3 4'
+ for provider in $providers
+ for provider in $providers
+ generate_link 1
+ case $1 in
+ provider_init wixmp '/Default :/p'
+ provider_name=wixmp
+ for provider in $providers
+ generate_link 2
+ case $1 in
+ provider_init youtube '/Yt-mp4 :/p'
+ provider_name=youtube
+ for provider in $providers
+ generate_link 3
+ case $1 in
+ provider_init sharepoint '/S-mp4 :/p'
+ provider_name=sharepoint
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
+ wait
+ generate_link 4
+ case $1 in
+ provider_init hianime '/Luf-Mp4 :/p'
+ provider_name=hianime
++ sed -n '/Default :/p'
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ sed -n '/Yt-mp4 :/p'
++ head -1
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0b0b5d0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ sed -n '/S-mp4 :/p'
++ head -1
++ cut -d: -f2
++ sed -n '/Luf-Mp4 :/p'
++ head -1
++ cut -d: -f2
++ sed 's/../&\
/g'
++ sed 's/../&\
++ cut -d: -f2
++ head -1
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
/g'
++ sed 's/../&\
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
++ cut -d: -f2
++ tr -d '\n'
/g'
++ sed 's/../&\
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
/g'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
++ sed 's/\/clock/\/clock\.json/'
++ tr -d '\n'
+ provider_id=
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e296b73756e696d732b72636875636f2b6c69646a6375752b74636f686567746867726f69682b37333e34363963763b333734343f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c247b'
+ '[' -n '' ']'
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e296b73756e696d732b72636875636f2b6c69646a6375752b74636f686567746867726f69682b37333e34363963763b333734343f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c247b' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e296b73756e696d732b72636875636f2b6c69646a6375752b74636f686567746867726f69682b37333e34363963763b333734343f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c247b'
+ provider_id='https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22'
+ '[' -n 'https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22' ']'
+ get_links 'https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22'
++ sed 's/\/clock/\/clock\.json/'
++ curl -e https://allmanga.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e296b73756e696d732b72636875636f2b6c69646a6375752b74636f686567746867726f69682b37333e34363963763b333734343f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64;
x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ curl -e https://allmanga.to -s 'https://allanime.dayhttps://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b'
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b'
++ curl -e https://allmanga.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ response=
++ printf %s ''
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link=
+ case "$episode_link" in
+ '[' -n '' ']'
+ printf %s 'https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22'
+ grep -q tools.fast4speed.rsvp
+ printf '%s\n' 'Yt >https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22'
+ printf '\033[1;32m%s\033[0m Links Fetched\n' youtube
youtube Links Fetched
+ response='{"links":[{"link":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","hls":true,"resolutionStr":"auto","subtitles":[{"lang":"en","label":"English","default":"default","src":"https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt"}],"src":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","fromCache":"2025-06-02T13:58:20.751Z","headers":{"Referer":"https://megacloud.club/","Origin":"https://megacloud.club","user-agent":"Mozilla/5.0 (Linux; Android 14; samsung SM-S921U) Chrome/121.0.6167 Mobile"}}]}'
++ printf %s '{"links":[{"link":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","hls":true,"resolutionStr":"auto","subtitles":[{"lang":"en","label":"English","default":"default","src":"https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt"}],"src":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","fromCache":"2025-06-02T13:58:20.751Z","headers":{"Referer":"https://megacloud.club/","Origin":"https://megacloud.club","user-agent":"Mozilla/5.0 (Linux; Android 14; samsung SM-S921U) Chrome/121.0.6167 Mobile"}}]}'
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link='auto >https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8'
+ case "$episode_link" in
++ printf %s '{"links":[{"link":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","hls":true,"resolutionStr":"auto","subtitles":[{"lang":"en","label":"English","default":"default","src":"https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt"}],"src":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","fromCache":"2025-06-02T13:58:20.751Z","headers":{"Referer":"https://megacloud.club/","Origin":"https://megacloud.club","user-agent":"Mozilla/5.0 (Linux; Android 14; samsung SM-S921U) Chrome/121.0.6167 Mobile"}}]}'
++ sed -nE 's|.*Referer":"([^"]*)".*|\1|p'
+ m3u8_refr=https://megacloud.club/
+ printf '%s\n' 'm3u8_refr >https://megacloud.club/'
++ printf %s 'auto >https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8'
++ head -1
++ cut '-d>' -f2
+ extract_link=https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8
++ printf %s https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8
++ sed 's|[^/]*$||'
+ relative_link=https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/
++ curl -e https://megacloud.club/ -s https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8 -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ response='{"links":[{"link":"https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw","mp4":true,"resolutionStr":"Mp4","src":"https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw"}]}'
++ printf %s '{"links":[{"link":"https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw","mp4":true,"resolutionStr":"Mp4","src":"https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw"}]}'
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ case "$episode_link" in
+ '[' -n 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw' ']'
+ printf '%s\n' 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ printf %s '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b'
+ grep -q tools.fast4speed.rsvp
+ printf '\033[1;32m%s\033[0m Links Fetched\n' sharepoint
sharepoint Links Fetched
+ m3u8_streams='#EXTM3U
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=1451662,RESOLUTION=1920x1080,FRAME-RATE=23.974,CODECS="avc1.640032,mp4a.40.2"
index-f1-v1-a1.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=821276,RESOLUTION=1280x720,FRAME-RATE=23.974,CODECS="avc1.64001f,mp4a.40.2"
index-f2-v1-a1.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=452443,RESOLUTION=640x360,FRAME-RATE=23.974,CODECS="avc1.64001e,mp4a.40.2"
index-f3-v1-a1.m3u8

#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=183432,RESOLUTION=1920x1080,CODECS="avc1.640032",URI="iframes-f1-v1-a1.m3u8"
#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=99968,RESOLUTION=1280x720,CODECS="avc1.64001f",URI="iframes-f2-v1-a1.m3u8"
#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=39567,RESOLUTION=640x360,CODECS="avc1.64001e",URI="iframes-f3-v1-a1.m3u8"'
+ printf %s '#EXTM3U
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=1451662,RESOLUTION=1920x1080,FRAME-RATE=23.974,CODECS="avc1.640032,mp4a.40.2"
index-f1-v1-a1.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=821276,RESOLUTION=1280x720,FRAME-RATE=23.974,CODECS="avc1.64001f,mp4a.40.2"
index-f2-v1-a1.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=452443,RESOLUTION=640x360,FRAME-RATE=23.974,CODECS="avc1.64001e,mp4a.40.2"
index-f3-v1-a1.m3u8

#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=183432,RESOLUTION=1920x1080,CODECS="avc1.640032",URI="iframes-f1-v1-a1.m3u8"
#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=99968,RESOLUTION=1280x720,CODECS="avc1.64001f",URI="iframes-f2-v1-a1.m3u8"
#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=39567,RESOLUTION=640x360,CODECS="avc1.64001e",URI="iframes-f3-v1-a1.m3u8"'
+ grep -q EXTM3U
+ printf %s '#EXTM3U
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=1451662,RESOLUTION=1920x1080,FRAME-RATE=23.974,CODECS="avc1.640032,mp4a.40.2"
index-f1-v1-a1.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=821276,RESOLUTION=1280x720,FRAME-RATE=23.974,CODECS="avc1.64001f,mp4a.40.2"
index-f2-v1-a1.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=452443,RESOLUTION=640x360,FRAME-RATE=23.974,CODECS="avc1.64001e,mp4a.40.2"
index-f3-v1-a1.m3u8

#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=183432,RESOLUTION=1920x1080,CODECS="avc1.640032",URI="iframes-f1-v1-a1.m3u8"
#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=99968,RESOLUTION=1280x720,CODECS="avc1.64001f",URI="iframes-f2-v1-a1.m3u8"
#EXT-X-I-FRAME-STREAM-INF:BANDWIDTH=39567,RESOLUTION=640x360,CODECS="avc1.64001e",URI="iframes-f3-v1-a1.m3u8"'
+ sed 's|^#EXT-X-STREAM.*x||g; s|,.*|p|g; /^#/d; $!N; s|\n| >|;/EXT-X-I-FRAME/d'
+ sed 's|>|cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/|g'
+ sort -nr
+ printf %s '{"links":[{"link":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","hls":true,"resolutionStr":"auto","subtitles":[{"lang":"en","label":"English","default":"default","src":"https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt"}],"src":"https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/master.m3u8","fromCache":"2025-06-02T13:58:20.751Z","headers":{"Referer":"https://megacloud.club/","Origin":"https://megacloud.club","user-agent":"Mozilla/5.0 (Linux; Android 14; samsung SM-S921U) Chrome/121.0.6167 Mobile"}}]}'
+ sed -nE 's|.*"subtitles":\[\{"lang":"en","label":"English","default":"default","src":"([^"]*)".*|subtitle >\1|p'
+ printf %s '/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e296b73756e696d732b72636875636f2b6c69646a6375752b74636f686567746867726f69682b37333e34363963763b333734343f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36302b36345237353c333e3c3636283636365c247b'
+ grep -q tools.fast4speed.rsvp
+ printf '\033[1;32m%s\033[0m Links Fetched\n' hianime
hianime Links Fetched
++ cat /tmp/tmp.pZnjpASWde/1 /tmp/tmp.pZnjpASWde/2 /tmp/tmp.pZnjpASWde/3 /tmp/tmp.pZnjpASWde/4 /tmp/tmp.pZnjpASWde/m3u8_refr /tmp/tmp.pZnjpASWde/suburl
++ sort -g -r -s
+ links='1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8
720p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f2-v1-a1.m3u8
360p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f3-v1-a1.m3u8
Yt >https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22
Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw
m3u8_refr >https://megacloud.club/
subtitle >https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt'
+ rm -r /tmp/tmp.pZnjpASWde
+ select_quality 1080p
+ printf %s mpv.exe
+ cut -f1 '-d '
+ grep -qE '(android|iSH|vlc)'
+ printf %s mpv.exe
+ cut -f1 '-d '
+ grep -qE '(android|iSH)'
+ case "$1" in
++ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8
720p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f2-v1-a1.m3u8
360p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f3-v1-a1.m3u8
Yt >https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22
Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw
m3u8_refr >https://megacloud.club/
subtitle >https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt'
++ grep -m 1 1080p
+ result='1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
+ '[' -z '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8' ']'
+ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
+ grep -q 'cc>'
++ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8
720p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f2-v1-a1.m3u8
360p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f3-v1-a1.m3u8
Yt >https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22
Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw
m3u8_refr >https://megacloud.club/
subtitle >https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt'
++ sed -nE 's|subtitle >(.*)|\1|p'
+ subtitle=https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt
+ '[' -n https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt ']'
+ subs_flag=--sub-file=https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt
+ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
+ grep -q 'cc>'
++ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8
720p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f2-v1-a1.m3u8
360p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f3-v1-a1.m3u8
Yt >https://tools.fast4speed.rsvp//media9/videos/ed6654HMukxd8TKJ7/sub/1?v=v22
Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw
m3u8_refr >https://megacloud.club/
subtitle >https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt'
++ sed -nE 's|m3u8_refr >(.*)|\1|p'
+ m3u8_refr=https://megacloud.club/
+ refr_flag=--referrer=https://megacloud.club/
+ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
+ grep -q tools.fast4speed.rsvp
+ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
+ grep -qE '(cc>|tools.fast4speed.rsvp)'
+ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
+ grep -q 'cc>'
++ printf %s '1080p cc>https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8'
++ cut '-d>' -f2
+ episode=https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8
+ printf %s '1
2
3
4
5
6
7
8
9
10
11'
+ grep -q '^1$'
+ '[' -z https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8 ']'
+ case "$player_function" in
+ '[' 0 = 0 ']'
+ replay=https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8
+ nohup mpv.exe '--force-media-title=Mushoku Tensei Isekai Ittara Honki Dasu Episode 1' https://sunshinerays93.live/_v7/b58f713420412a61793af42eae2a5acfcee33b3bee84fd4ba353857260705543fe2d277e4a73d384dd7524b17623f757859914f592e44e015e682b211952421e7dc746982bbffc0bdf81a0b0f8811fdb251d4b4d2b9630ae066f7f2a2f7a49337c2351dde443aec4c85d9521090c338ac81e8677734624d9d2852ea6375d6dfc/index-f1-v1-a1.m3u8 --sub-file=https://s.megastatics.com/subtitle/cb430f9c6c68841138d0c94170f22be8/eng-2.vtt --referrer=https://megacloud.club/
+ unset episode
+ update_history
+ grep -q -- ed6654HMukxd8TKJ7 /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ sed -E 's|^[^ ]+      ed6654HMukxd8TKJ7       [^      ]+$|1   ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)|' /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ mv /c/Users/<USER>/.local/state/ani-cli/ani-hsts.new /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' 0 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ tput rc
+ tput ed
+ '[' mpv.exe = download ']'
+ '[' mpv.exe = debug ']'
++ printf 'next\nreplay\nprevious\nselect\nchange_quality\nquit'
++ nth 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ cat -
++ stdin='next
replay
previous
select
change_quality
quit'
++ '[' -z 'next
replay
previous
select
change_quality
quit' ']'
+++ printf '%s\n' 'next
replay
previous
select
change_quality
quit'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=6
++ '[' 6 -eq 1 ']'
++ prompt='Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s 'next
replay
previous
select
change_quality
quit'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=quit
+++ printf %s quit
+++ head -n1
++ line_start=quit
+++ printf %s quit
+++ tail -n1
++ line_end=quit
++ '[' -n quit ']'
++ '[' quit = quit ']'
++ printf %s 'next
replay
previous
select
change_quality
quit'
++ grep -E '^quit($|[[:space:]])'
++ cut -f2,3
+ cmd=quit
+ case "$cmd" in
+ exit 0