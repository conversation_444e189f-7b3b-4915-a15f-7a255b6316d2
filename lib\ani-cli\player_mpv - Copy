#!/bin/sh

player_commands () {
	if ! uname -a | grep -qE '[Aa]ndroid';then
		printf '%s' "mpv.com"
	fi
}

play_episode () {
	video_url="$1"; refr="$2" trackma_title="$3"
	inf "Currently playing $trackma_title"
	if uname -a | grep -qE '[Aa]ndroid';then
		am start --user 0 -a android.intent.action.VIEW -d "$video_url" -n is.xyz.mpv/.MPVActivity > /dev/null 2>&1 &
	else
		mpv.com "$video_url" --config-dir=F:\PortableApps\mpv --profile=anime --referrer="$refr" --force-media-title="$trackma_title" > /dev/null 2>&1 &
	fi
}
