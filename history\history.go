package history

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

type Entry struct {
	ID         string    `json:"id"`
	Title      string    `json:"title"`
	EpisodeNo  string    `json:"episodeNo"`
	WatchedEp  []int     `json:"watchedEp"`
	LastPlayed time.Time `json:"lastPlayed"`
}

type Manager struct {
	file string
}

func NewManager(file string) *Manager {
	return &Manager{file: file}
}

func (m *Manager) ReadEntries() ([]Entry, error) {
	// Create history file if it doesn't exist
	if _, err := os.Stat(m.file); os.IsNotExist(err) {
		if err := os.WriteFile(m.file, []byte("[]"), 0644); err != nil {
			return nil, fmt.Errorf("failed to create history file: %v", err)
		}
	}

	data, err := os.ReadFile(m.file)
	if err != nil {
		return nil, fmt.Errorf("failed to read history file: %v", err)
	}

	var entries []Entry
	if err := json.Unmarshal(data, &entries); err != nil {
		// If JSO<PERSON> is corrupted, log the error and create a backup
		backupFile := m.file + ".bak"
		if err := os.WriteFile(backupFile, data, 0644); err == nil {
			fmt.Fprintf(os.Stderr, "Warning: History file corrupted. Created backup at %s\n", backupFile)
		}
		// Create a new empty history file
		if err := os.WriteFile(m.file, []byte("[]"), 0644); err != nil {
			return nil, fmt.Errorf("failed to recreate history file: %v", err)
		}
		return []Entry{}, nil
	}

	return entries, nil
}

func (m *Manager) SaveEntries(entries []Entry) error {
	data, err := json.Marshal(entries)
	if err != nil {
		return err
	}

	return os.WriteFile(m.file, data, 0644)
}

func (m *Manager) AddEntry(entry Entry) error {
	entries, err := m.ReadEntries()
	if err != nil {
		return err
	}

	// Check if entry already exists
	for i, e := range entries {
		if e.ID == entry.ID {
			// Update existing entry
			entries[i] = entry
			break
		}
	}

	// If not found, add new entry
	if len(entries) == 0 || entries[len(entries)-1].ID != entry.ID {
		entries = append(entries, entry)
	}

	return m.SaveEntries(entries)
}

func (m *Manager) MarkEpisodeWatched(id string, title string, episode int) error {
	entries, err := m.ReadEntries()
	if err != nil {
		return err
	}

	for i, e := range entries {
		if e.ID == id {
			// Add episode to watched list if not already watched
			if !contains(e.WatchedEp, episode) {
				entries[i].WatchedEp = append(entries[i].WatchedEp, episode)
			}
			entries[i].LastPlayed = time.Now()
			break
		}
	}

	// If not found, add new entry
	if len(entries) == 0 || entries[len(entries)-1].ID != id {
		entries = append(entries, Entry{
			ID:         id,
			Title:      title,
			EpisodeNo:  fmt.Sprintf("%d", episode),
			WatchedEp:  []int{episode},
			LastPlayed: time.Now(),
		})
	}

	return m.SaveEntries(entries)
}

func (m *Manager) GetUnwatchedEpisodes(id string) ([]int, error) {
	entries, err := m.ReadEntries()
	if err != nil {
		return nil, err
	}

	for _, e := range entries {
		if e.ID == id {
			// Get all episodes from episodeNo
			// episodes, err := episode.ParseEpisodeRange(e.EpisodeNo)
			// if err != nil {
			// 	return nil, err
			// }

			// allEpisodes := episode.GenerateAllEpisodeNumbers(episodes)
			allEpisodes := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}
			unwatched := make([]int, 0)

			// Find unwatched episodes
			for _, ep := range allEpisodes {
				if !contains(e.WatchedEp, ep) {
					unwatched = append(unwatched, ep)
				}
			}

			return unwatched, nil
		}
	}

	return nil, fmt.Errorf("anime not found in history")
}

func contains(slice []int, value int) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}
