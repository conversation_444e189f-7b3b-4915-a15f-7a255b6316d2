DemonSeed@ /f/programming/GitHub/ani-cli (master $%=) [21:29]
$ bash -x ./ani-cli-mymod-current.sh -q 1080p "mushoku tensei" > flow.txt
+ version_number=4.8.2
+ agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ allanime_refr=https://allanime.to
+ allanime_base=allanime.day
+ allanime_api=https://api.allanime.day
+ mode=sub
+ download_dir=.
+ quality=best
+ case "$(uname -a)" in
++ uname -a
+ player_function=mpv.exe
+ no_detach=0
+ use_external_menu=0
+ skip_intro=0
+ skip_title=
+ '[' -t 0 ']'
+ hist_dir=/c/Users/<USER>/.local/state/ani-cli
+ '[' '!' -d /c/Users/<USER>/.local/state/ani-cli ']'
+ histfile=/c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' '!' -f /c/Users/<USER>/.local/state/ani-cli/ani-hsts ']'
+ search=scrape
+ '[' 3 -gt 0 ']'
+ case "$1" in
+ '[' 3 -lt 2 ']'
+ quality=1080p
+ shift
+ shift
+ '[' 1 -gt 0 ']'
+ case "$1" in
++ printf %s ' mushoku tensei'
++ sed 's|^ ||;s| |+|g'
+ query=mushoku+tensei
+ shift
+ '[' 0 -gt 0 ']'
+ '[' 0 = 0 ']'
+ multi_selection_flag=-m
+ '[' 0 = 1 ']'
+ printf '\33[2K\r\033[1;34mChecking dependencies...\033[0m\n'
+ dep_ch curl sed grep
+ for dep in "$@"
+ command -v curl
+ for dep in "$@"
+ command -v sed
+ for dep in "$@"
+ command -v grep
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ dep_ch fzf
+ for dep in "$@"
+ command -v fzf
+ case "$player_function" in
+ dep_ch mpv.exe
+ for dep in "$@"
+ command -v mpv.exe
+ case "$search" in
+ '[' 0 = 0 ']'
+ '[' -z mushoku+tensei ']'
++ printf %s mushoku+tensei
++ sed 's| |+|g'
+ query=mushoku+tensei
++ search_anime mushoku+tensei
++ search_gql='query(        $search: SearchInput        $limit: Int        $page: Int        $translationType: VaildTranslationTypeEnumType        $countryOrigin: VaildCountryOriginEnumType    ) {    shows(        search: $search        limit: $limit
     page: $page        translationType: $translationType        countryOrigin: $countryOrigin    ) {        edges {
 _id name availableEpisodes __typename       }    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"search":{"allowAdult":false,"allowUnknown":false,"query":"mushoku+tensei"},"limit":40,"page":1,"translationType":"sub","countryOrigin":"ALL"}' --data-urlencode 'query=query(        $search: SearchInput        $limit: Int        $page: Int        $translationType: VaildTranslationTypeEnumType        $countryOrigin: VaildCountryOriginEnumType    ) {    shows(        search: $search        limit: $limit        page: $page        translationType: $translationType        countryOrigin: $countryOrigin    ) {        edges {            _id name availableEpisodes __typename       }    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|Show|\
|g'
++ sed -nE 's|.*_id":"([^"]*)","name":"([^"]*)".*sub":([1-9][^,]*).*|\1 \2 (\3 episodes)|p'
+ anime_list='QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+ '[' -z 'QusmPJR29sg3G35CB     Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
+ '[' '' -eq '' ']'
+ '[' -z '' ']'
++ printf %s 'QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ nl -w 2
++ sed 's/^[[:space:]]//'
++ nth 'Select anime: '
+++ cat -
++ stdin='1     QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ '[' -z '1    QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
+++ printf '%s\n' '1    QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=6
++ '[' 6 -eq 1 ']'
++ prompt='Select anime: '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s '1        QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Select anime: '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Select anime: '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Select anime: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=1
++ '[' -n 1 ']'
++ printf %s '1 QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ grep -E '^1($|[[:space:]])'
++ cut -f2,3
+ result='QusmPJR29sg3G35CB     Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)'
+ '[' -z 'QusmPJR29sg3G35CB     Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)' ']'
++ printf %s 'QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)'
++ cut -f2
+ title='Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)'
++ printf %s 'Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)'
++ cut '-d(' -f1
++ tr -d '[:punct:]'
+ allanime_title='Mushoku Tensei II Isekai Ittara Honki Dasu Part 2 '
++ printf %s 'QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)'
++ cut -f1
+ id=QusmPJR29sg3G35CB
++ episodes_list QusmPJR29sg3G35CB
++ episodes_list_gql='query ($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"QusmPJR29sg3G35CB"}' --data-urlencode 'query=query ($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed -nE 's|.*sub":\[([0-9.",]*)\].*|\1|p'
++ sed 's|,|\
|g; s|"||g'
++ sort -n -k 1
+ ep_list='1
2
3
4
5
6
7
8
9
10
11
12'
+ '[' -z '' ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11
12'
++ nth 'Select episode: ' -m
+++ cat -
++ stdin='1
2
3
4
5
6
7
8
9
10
11
12'
++ '[' -z '1
2
3
4
5
6
7
8
9
10
11
12' ']'
+++ printf '%s\n' '1
2
3
4
5
6
7
8
9
10
11
12'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=12
++ '[' 12 -eq 1 ']'
++ prompt='Select episode: '
++ multi_flag=
++ '[' 2 -ne 1 ']'
++ shift
++ multi_flag=-m
+++ printf %s '1
2
3
4
5
6
7
8
9
10
11
12'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher -m 'Select episode: '
+++ '[' 0 = 0 ']'
+++ '[' -z -m ']'
+++ '[' 0 = 0 ']'
+++ fzf -m --reverse --cycle --prompt 'Select episode: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=1
++ '[' -n 1 ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11
12'
++ grep -E '^1($|[[:space:]])'
++ cut -f2,3
+ ep_no=1
+ '[' -z 1 ']'
+ tput cuu1
+ tput el
+ tput sc
+ play
++ printf %s 1
++ grep -Eo '^(-1|[0-9]+(\.[0-9]+)?)'
+ start=1
++ printf %s 1
++ grep -Eo '(-1|[0-9]+(\.[0-9]+)?)$'
+ end=1
+ '[' 1 = -1 ']'
+ '[' -z 1 ']'
+ '[' 1 = 1 ']'
+ unset start end
+ '[' '' = -1 ']'
++ printf '%s\n' 1
++ wc -l
++ tr -d '[:space:]'
+ line_count=1
+ '[' 1 '!=' 1 ']'
+ '[' -n '' ']'
+ play_episode
+ aniskip_title='Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)'
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ get_episode_url
+ episode_embed_gql='query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"QusmPJR29sg3G35CB","translationType":"sub","episodeString":"1"}' --data-urlencode 'query=query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ tr '{}' '\n'
++ sed 's|\\u002F|\/|g;s|\\||g'
++ sed -nE 's|.*sourceUrl":"--([^"]*)".*sourceName":"([^"]*)".*|\2 :\1|p'
+ resp='Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515900174e515c5d574b17694d4b5568726a0a014b5f0b7f0b0d7b7a174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0f0d0e0b0e0f0f0d0e010e000a5a0b0c0a5a0f0e0e0f0f0c0f0a0a5a0b0c0a5a0b0f0b5e0b0f0b0a0b090b010e0b0f0e0b5a0b0f0b0c0b0d0b0c0b0a0b0d0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ mktemp -d
+ cache_dir=/tmp/tmp.4iuk1PjqK0
+ providers='1 2 3 4 5'
+ for provider in $providers
+ for provider in $providers
+ generate_link 1
+ case $1 in
+ provider_init wixmp '/Default :/p'
+ provider_name=wixmp
+ for provider in $providers
+ generate_link 2
+ case $1 in
+ provider_init dropbox '/Sak :/p'
+ provider_name=dropbox
+ for provider in $providers
+ generate_link 3
+ case $1 in
+ provider_init wetransfer '/Kir :/p'
+ provider_name=wetransfer
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515900174e515c5d574b17694d4b5568726a0a014b5f0b7f0b0d7b7a174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0f0d0e0b0e0f0f0d0e010e000a5a0b0c0a5a0f0e0e0f0f0c0f0a0a5a0b0c0a5a0b0f0b5e0b0f0b0a0b090b010e0b0f0e0b5a0b0f0b0c0b0d0b0c0b0a0b0d0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
+ for provider in $providers
+ generate_link 4
+ case $1 in
+ provider_init sharepoint '/S-mp4 :/p'
+ provider_name=sharepoint
++ sed -n '/Default :/p'
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515900174e515c5d574b17694d4b5568726a0a014b5f0b7f0b0d7b7a174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0f0d0e0b0e0f0f0d0e010e000a5a0b0c0a5a0f0e0e0f0f0c0f0a0a5a0b0c0a5a0b0f0b5e0b0f0b0a0b090b010e0b0f0e0b5a0b0f0b0c0b0d0b0c0b0a0b0d0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
+ wait
+ generate_link 5
+ case $1 in
+ provider_init gogoanime '/Luf-mp4 :/p'
+ provider_name=gogoanime
++ head -1
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515900174e515c5d574b17694d4b5568726a0a014b5f0b7f0b0d7b7a174b4d5a1709
++ sed -n '/Sak :/p'
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0f0d0e0b0e0f0f0d0e010e000a5a0b0c0a5a0f0e0e0f0f0c0f0a0a5a0b0c0a5a0b0f0b5e0b0f0b0a0b090b010e0b0f0e0b5a0b0f0b0c0b0d0b0c0b0a0b0d0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515900174e515c5d574b17694d4b5568726a0a014b5f0b7f0b0d7b7a174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0f0d0e0b0e0f0f0d0e010e000a5a0b0c0a5a0f0e0e0f0f0c0f0a0a5a0b0c0a5a0b0f0b5e0b0f0b0a0b090b010e0b0f0e0b5a0b0f0b0c0b0d0b0c0b0a0b0d0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ sed -n '/Kir :/p'
++ cut -d: -f2
++ head -1
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515900174e515c5d574b17694d4b5568726a0a014b5f0b7f0b0d7b7a174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0d0f0f0b0f0d0e5a0d0e0c5b0d0c0b0c0b5e0f0d0e090b0d0c090b0d0b0b0c0d0c0c0d010b0f0d010f0d0f0b0e0c0a0c0f5a
++ sed -n '/S-mp4 :/p'
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0f0d0e0b0e0f0f0d0e010e000a5a0b0c0a5a0f0e0e0f0f0c0f0a0a5a0b0c0a5a0b0f0b5e0b0f0b0a0b090b010e0b0f0e0b5a0b0f0b0c0b0d0b0c0b0a0b0d0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ sed -n '/Luf-mp4 :/p'
++ head -1
++ head -1
++ head -1
++ cut -d: -f2
++ sed 's/../&\
/g'
++ cut -d: -f2
++ sed 's/../&\
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ cut -d: -f2
++ cut -d: -f2
/g'
++ sed 's/../&\
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ tr -d '\n'
/g'
++ sed 's/../&\
++ sed 's/../&\
/g'
/g'
++ sed 's/\/clock/\/clock\.json/'
++ tr -d '\n'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
+ provider_id=
+ '[' -n '' ']'
++ tr -d '\n'
++ tr -d '\n'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
+ provider_id=
+ '[' -n '' ']'
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334295773756b564c54343f7561354135334544593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b5773756b564c54343f7561354135334544593759757364247b'
+ provider_id=
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334295773756b564c54343f7561354135334544593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b5773756b564c54343f7561354135334544593759757364247b' ']'
+ provider_id=
+ '[' -n '' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334295773756b564c54343f7561354135334544593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b5773756b564c54343f7561354135334544593759757364247b'
+ '[' -n '' ']'
++ curl -e https://allanime.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334295773756b564c54343f7561354135334544593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b5773756b564c54343f7561354135334544593759757364247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+ case "$episode_link" in
+ '[' -n 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw' ']'
+ printf '%s\n' 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+ '[' -z '' ']'
+ printf '\033[1;32m%s\033[0m Links Fetched\n' sharepoint
sharepoint Links Fetched
++ cat /tmp/tmp.4iuk1PjqK0/1 /tmp/tmp.4iuk1PjqK0/2 /tmp/tmp.4iuk1PjqK0/3 /tmp/tmp.4iuk1PjqK0/4 /tmp/tmp.4iuk1PjqK0/5
++ sed 's|^Mp4-||g;/http/!d'
++ sort -g -r -s
+ links='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+ rm -r /tmp/tmp.4iuk1PjqK0
++ select_quality 1080p
++ case "$1" in
+++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+++ grep -m 1 1080p
++ result=
++ '[' -z '' ']'
++ printf 'Specified quality not found, defaulting to best\n'
Specified quality not found, defaulting to best
+++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+++ head -n1
++ result='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
++ cut '-d>' -f2
+ episode='https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+ '[' -z 'https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw' ']'
+ case "$player_function" in
+ '[' 0 = 0 ']'
+ replay='https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+ nohup mpv.exe --profile=anime '--force-media-title=Mushoku Tensei II Isekai Ittara Honki Dasu Part 2 Episode 1' 'https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=ER9BZnCzublAsWYvkSdkPpsBDBfiL1Xy5cUWXl6vtVGJiw'
+ unset episode
+ update_history
+ grep -q -- QusmPJR29sg3G35CB /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ sed -E 's/^[^\t]+\tQusmPJR29sg3G35CB\t/1\tQusmPJR29sg3G35CB\t/' /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ mv /c/Users/<USER>/.local/state/ani-cli/ani-hsts.new /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' 0 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ tput rc
+ tput ed
+ '[' mpv.exe = download ']'
+ '[' mpv.exe = debug ']'
++ printf 'next\nreplay\nprevious\nselect\nhistory\nchange_quality\nsearch_new\nquit'
++ nth 'Playing episode 1 of Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)... '
+++ cat -
++ stdin='next
replay
previous
select
history
change_quality
search_new
quit'
++ '[' -z 'next
replay
previous
select
history
change_quality
search_new
quit' ']'
+++ printf '%s\n' 'next
replay
previous
select
history
change_quality
search_new
quit'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=8
++ '[' 8 -eq 1 ']'
++ prompt='Playing episode 1 of Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)... '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s 'next
replay
previous
select
history
change_quality
search_new
quit'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Playing episode 1 of Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)... '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Playing episode 1 of Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)... '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Playing episode 1 of Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)... '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=quit
++ '[' -n quit ']'
++ printf %s 'next
replay
previous
select
history
change_quality
search_new
quit'
++ grep -E '^quit($|[[:space:]])'
++ cut -f2,3
+ cmd=quit
+ case "$cmd" in
+ exit 0

DemonSeed@ /f/programming/GitHub/ani-cli (master $%=) [21:30]
$ bash -x ./ani-cli-mymod-current.sh -q 1080p "mushoku tensei" > flow1.txt
+ version_number=4.8.2
+ agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ allanime_refr=https://allanime.to
+ allanime_base=allanime.day
+ allanime_api=https://api.allanime.day
+ mode=sub
+ download_dir=.
+ quality=best
+ case "$(uname -a)" in
++ uname -a
+ player_function=mpv.exe
+ no_detach=0
+ use_external_menu=0
+ skip_intro=0
+ skip_title=
+ '[' -t 0 ']'
+ hist_dir=/c/Users/<USER>/.local/state/ani-cli
+ '[' '!' -d /c/Users/<USER>/.local/state/ani-cli ']'
+ histfile=/c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' '!' -f /c/Users/<USER>/.local/state/ani-cli/ani-hsts ']'
+ search=scrape
+ '[' 3 -gt 0 ']'
+ case "$1" in
+ '[' 3 -lt 2 ']'
+ quality=1080p
+ shift
+ shift
+ '[' 1 -gt 0 ']'
+ case "$1" in
++ printf %s ' mushoku tensei'
++ sed 's|^ ||;s| |+|g'
+ query=mushoku+tensei
+ shift
+ '[' 0 -gt 0 ']'
+ '[' 0 = 0 ']'
+ multi_selection_flag=-m
+ '[' 0 = 1 ']'
+ printf '\33[2K\r\033[1;34mChecking dependencies...\033[0m\n'
+ dep_ch curl sed grep
+ for dep in "$@"
+ command -v curl
+ for dep in "$@"
+ command -v sed
+ for dep in "$@"
+ command -v grep
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ dep_ch fzf
+ for dep in "$@"
+ command -v fzf
+ case "$player_function" in
+ dep_ch mpv.exe
+ for dep in "$@"
+ command -v mpv.exe
+ case "$search" in
+ '[' 0 = 0 ']'
+ '[' -z mushoku+tensei ']'
++ printf %s mushoku+tensei
++ sed 's| |+|g'
+ query=mushoku+tensei
++ search_anime mushoku+tensei
++ search_gql='query(        $search: SearchInput        $limit: Int        $page: Int        $translationType: VaildTranslationTypeEnumType        $countryOrigin: VaildCountryOriginEnumType    ) {    shows(        search: $search        limit: $limit
     page: $page        translationType: $translationType        countryOrigin: $countryOrigin    ) {        edges {
 _id name availableEpisodes __typename       }    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"search":{"allowAdult":false,"allowUnknown":false,"query":"mushoku+tensei"},"limit":40,"page":1,"translationType":"sub","countryOrigin":"ALL"}' --data-urlencode 'query=query(        $search: SearchInput        $limit: Int        $page: Int        $translationType: VaildTranslationTypeEnumType        $countryOrigin: VaildCountryOriginEnumType    ) {    shows(        search: $search        limit: $limit        page: $page        translationType: $translationType        countryOrigin: $countryOrigin    ) {        edges {            _id name availableEpisodes __typename       }    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|Show|\
|g'
++ sed -nE 's|.*_id":"([^"]*)","name":"([^"]*)".*sub":([1-9][^,]*).*|\1 \2 (\3 episodes)|p'
+ anime_list='QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+ '[' -z 'QusmPJR29sg3G35CB     Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
+ '[' '' -eq '' ']'
+ '[' -z '' ']'
++ printf %s 'QusmPJR29sg3G35CB Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ nl -w 2
++ sed 's/^[[:space:]]//'
++ nth 'Select anime: '
+++ cat -
++ stdin='1     QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ '[' -z '1    QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
+++ printf '%s\n' '1    QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=6
++ '[' 6 -eq 1 ']'
++ prompt='Select anime: '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s '1        QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Select anime: '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Select anime: '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Select anime: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=6
++ '[' -n 6 ']'
++ printf %s '1 QusmPJR29sg3G35CB       Mushoku Tensei II: Isekai Ittara Honki Dasu Part 2 (12 episodes)
2       uvaA8ufcJbrFmF8Sb       Mushoku Tensei II: Isekai Ittara Honki Dasu (13 episodes)
3       JfHCxBoSLi24TQMAq       Mushoku Tensei II: Isekai Ittara Honki Dasu: Shugo Jutsushi Fitz (1 episodes)
4       r5qPDS2dM7YsRxxcD       Mushoku Tensei: Isekai Ittara Honki Dasu - Eris no Goblin Toubatsu (1 episodes)
5       ijrAuozJRbs7aY96J       Mushoku Tensei: Isekai Ittara Honki Dasu Part 2 (12 episodes)
6       ed6654HMukxd8TKJ7       Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ grep -E '^6($|[[:space:]])'
++ cut -f2,3
+ result='ed6654HMukxd8TKJ7     Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+ '[' -z 'ed6654HMukxd8TKJ7     Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)' ']'
++ printf %s 'ed6654HMukxd8TKJ7 Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ cut -f2
+ title='Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ printf %s 'Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ cut '-d(' -f1
++ tr -d '[:punct:]'
+ allanime_title='Mushoku Tensei Isekai Ittara Honki Dasu '
++ printf %s 'ed6654HMukxd8TKJ7 Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
++ cut -f1
+ id=ed6654HMukxd8TKJ7
++ episodes_list ed6654HMukxd8TKJ7
++ episodes_list_gql='query ($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"ed6654HMukxd8TKJ7"}' --data-urlencode 'query=query ($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed -nE 's|.*sub":\[([0-9.",]*)\].*|\1|p'
++ sed 's|,|\
|g; s|"||g'
++ sort -n -k 1
+ ep_list='1
2
3
4
5
6
7
8
9
10
11'
+ '[' -z '' ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
++ nth 'Select episode: ' -m
+++ cat -
++ stdin='1
2
3
4
5
6
7
8
9
10
11'
++ '[' -z '1
2
3
4
5
6
7
8
9
10
11' ']'
+++ printf '%s\n' '1
2
3
4
5
6
7
8
9
10
11'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=11
++ '[' 11 -eq 1 ']'
++ prompt='Select episode: '
++ multi_flag=
++ '[' 2 -ne 1 ']'
++ shift
++ multi_flag=-m
+++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher -m 'Select episode: '
+++ '[' 0 = 0 ']'
+++ '[' -z -m ']'
+++ '[' 0 = 0 ']'
+++ fzf -m --reverse --cycle --prompt 'Select episode: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=1
++ '[' -n 1 ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
++ grep -E '^1($|[[:space:]])'
++ cut -f2,3
+ ep_no=1
+ '[' -z 1 ']'
+ tput cuu1
+ tput el
+ tput sc
+ play
++ printf %s 1
++ grep -Eo '^(-1|[0-9]+(\.[0-9]+)?)'
+ start=1
++ printf %s 1
++ grep -Eo '(-1|[0-9]+(\.[0-9]+)?)$'
+ end=1
+ '[' 1 = -1 ']'
+ '[' -z 1 ']'
+ '[' 1 = 1 ']'
+ unset start end
+ '[' '' = -1 ']'
++ printf '%s\n' 1
++ wc -l
++ tr -d '[:space:]'
+ line_count=1
+ '[' 1 '!=' 1 ']'
+ '[' -n '' ']'
+ play_episode
+ aniskip_title='Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)'
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ get_episode_url
+ episode_embed_gql='query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"ed6654HMukxd8TKJ7","translationType":"sub","episodeString":"1"}' --data-urlencode 'query=query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ tr '{}' '\n'
++ sed 's|\\u002F|\/|g;s|\\||g'
++ sed -nE 's|.*sourceUrl":"--([^"]*)".*sourceName":"([^"]*)".*|\2 :\1|p'
+ resp='S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ mktemp -d
+ cache_dir=/tmp/tmp.TfRifZ8y3s
+ providers='1 2 3 4 5'
+ for provider in $providers
+ for provider in $providers
+ generate_link 1
+ case $1 in
+ provider_init wixmp '/Default :/p'
+ provider_name=wixmp
+ for provider in $providers
+ generate_link 2
+ case $1 in
+ provider_init dropbox '/Sak :/p'
+ provider_name=dropbox
+ for provider in $providers
+ generate_link 3
+ case $1 in
+ provider_init wetransfer '/Kir :/p'
+ provider_name=wetransfer
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
+ for provider in $providers
+ generate_link 4
+ case $1 in
+ provider_init sharepoint '/S-mp4 :/p'
+ provider_name=sharepoint
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
++ sed -n '/Default :/p'
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
+ wait
+ generate_link 5
+ case $1 in
+ provider_init gogoanime '/Luf-mp4 :/p'
+ provider_name=gogoanime
++ sed -n '/Sak :/p'
++ head -1
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ sed -n '/Kir :/p'
++ head -1
++ printf %s 'S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0b0c0a010e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
++ sed -n '/S-mp4 :/p'
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0d0b090b5d0b0c0b0c0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0b0e0a0b080b080b0b0b0a0c5d0c5a0f0b0e5c0f5d0e0a0b5d0d0a0c5c0c5b0b090d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e5a0f0b0f0d0e5d0e010e5c0f0b0a5a0f0a0e0b0e000f0d0e0b0e5e0a5a0e5b0e010e0c0e590e0b0f0d0f0d0a5a0f0c0e0b0e5e0e000e0d0e0f0f0c0e000e0f0f0a0e5e0e010e000a5a0b0f0b0b0b5d0b0c0b0e0b010e0b0f0e0b5a0b0b0b0f0b0c0b0c0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a
Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c515901174e515c5d574b175d5c0e0e0d0c70754d53405c006c73720f174b4d5a1709074e054e0a0a'
++ cut -d: -f2
++ head -1
++ cut -d: -f2
++ sed -n '/Luf-mp4 :/p'
++ sed 's/../&\
/g'
++ head -1
++ cut -d: -f2
++ sed 's/../&\
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ sed 's/../&\
++ cut -d: -f2
++ head -1
/g'
/g'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ cut -d: -f2
++ tr -d '\n'
++ sed 's/../&\
/g'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/../&\
/g'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ tr -d '\n'
+ provider_id=
+ '[' -n '' ']'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ tr -d '\n'
+ provider_id=
+ '[' -n '' ']'
+ provider_id=
+ '[' -n '' ']'
++ sed 's/\/clock/\/clock\.json/'
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b'
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b'
+ provider_id=
+ '[' -n '' ']'
++ curl -e https://allanime.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6334296362303033324e4b736d7e623e524d4c31593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6362303033324e4b736d7e623e524d4c31593759757364247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ case "$episode_link" in
+ '[' -n 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw' ']'
+ printf '%s\n' 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ '[' -z '' ']'
+ printf '\033[1;32m%s\033[0m Links Fetched\n' sharepoint
sharepoint Links Fetched
++ cat /tmp/tmp.TfRifZ8y3s/1 /tmp/tmp.TfRifZ8y3s/2 /tmp/tmp.TfRifZ8y3s/3 /tmp/tmp.TfRifZ8y3s/4 /tmp/tmp.TfRifZ8y3s/5
++ sed 's|^Mp4-||g;/http/!d'
++ sort -g -r -s
+ links='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ rm -r /tmp/tmp.TfRifZ8y3s
++ select_quality 1080p
++ case "$1" in
+++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+++ grep -m 1 1080p
++ result=
++ '[' -z '' ']'
++ printf 'Specified quality not found, defaulting to best\n'
Specified quality not found, defaulting to best
+++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+++ head -n1
++ result='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
++ cut '-d>' -f2
+ episode='https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ '[' -z 'https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw' ']'
+ case "$player_function" in
+ '[' 0 = 0 ']'
+ replay='https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ nohup mpv.exe --profile=anime '--force-media-title=Mushoku Tensei Isekai Ittara Honki Dasu Episode 1' 'https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EZFlvFAUxWlHrfTzeXMtJNkBm9uYc1AJv-V40u8prepUYw'
+ unset episode
+ update_history
+ grep -q -- ed6654HMukxd8TKJ7 /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ sed -E 's/^[^\t]+\ted6654HMukxd8TKJ7\t/1\ted6654HMukxd8TKJ7\t/' /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ mv /c/Users/<USER>/.local/state/ani-cli/ani-hsts.new /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' 0 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ tput rc
+ tput ed
+ '[' mpv.exe = download ']'
+ '[' mpv.exe = debug ']'
++ printf 'next\nreplay\nprevious\nselect\nhistory\nchange_quality\nsearch_new\nquit'
++ nth 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ cat -
++ stdin='next
replay
previous
select
history
change_quality
search_new
quit'
++ '[' -z 'next
replay
previous
select
history
change_quality
search_new
quit' ']'
+++ printf '%s\n' 'next
replay
previous
select
history
change_quality
search_new
quit'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=8
++ '[' 8 -eq 1 ']'
++ prompt='Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s 'next
replay
previous
select
history
change_quality
search_new
quit'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Playing episode 1 of Mushoku Tensei: Isekai Ittara Honki Dasu (11 episodes)... '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=quit
++ '[' -n quit ']'
++ printf %s 'next
replay
previous
select
history
change_quality
search_new
quit'
++ grep -E '^quit($|[[:space:]])'
++ cut -f2,3
+ cmd=quit
+ case "$cmd" in
+ exit 0

DemonSeed@ /f/programming/GitHub/ani-cli (master $%=) [21:30]
$ bash -x ./ani-cli-mymod-current.sh -q 1080p "akira" > flow2.txt
+ version_number=4.8.2
+ agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ allanime_refr=https://allanime.to
+ allanime_base=allanime.day
+ allanime_api=https://api.allanime.day
+ mode=sub
+ download_dir=.
+ quality=best
+ case "$(uname -a)" in
++ uname -a
+ player_function=mpv.exe
+ no_detach=0
+ use_external_menu=0
+ skip_intro=0
+ skip_title=
+ '[' -t 0 ']'
+ hist_dir=/c/Users/<USER>/.local/state/ani-cli
+ '[' '!' -d /c/Users/<USER>/.local/state/ani-cli ']'
+ histfile=/c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' '!' -f /c/Users/<USER>/.local/state/ani-cli/ani-hsts ']'
+ search=scrape
+ '[' 3 -gt 0 ']'
+ case "$1" in
+ '[' 3 -lt 2 ']'
+ quality=1080p
+ shift
+ shift
+ '[' 1 -gt 0 ']'
+ case "$1" in
++ printf %s ' akira'
++ sed 's|^ ||;s| |+|g'
+ query=akira
+ shift
+ '[' 0 -gt 0 ']'
+ '[' 0 = 0 ']'
+ multi_selection_flag=-m
+ '[' 0 = 1 ']'
+ printf '\33[2K\r\033[1;34mChecking dependencies...\033[0m\n'
+ dep_ch curl sed grep
+ for dep in "$@"
+ command -v curl
+ for dep in "$@"
+ command -v sed
+ for dep in "$@"
+ command -v grep
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ dep_ch fzf
+ for dep in "$@"
+ command -v fzf
+ case "$player_function" in
+ dep_ch mpv.exe
+ for dep in "$@"
+ command -v mpv.exe
+ case "$search" in
+ '[' 0 = 0 ']'
+ '[' -z akira ']'
++ printf %s akira
++ sed 's| |+|g'
+ query=akira
++ search_anime akira
++ search_gql='query(        $search: SearchInput        $limit: Int        $page: Int        $translationType: VaildTranslationTypeEnumType        $countryOrigin: VaildCountryOriginEnumType    ) {    shows(        search: $search        limit: $limit
     page: $page        translationType: $translationType        countryOrigin: $countryOrigin    ) {        edges {
 _id name availableEpisodes __typename       }    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"search":{"allowAdult":false,"allowUnknown":false,"query":"akira"},"limit":40,"page":1,"translationType":"sub","countryOrigin":"ALL"}' --data-urlencode 'query=query(        $search: SearchInput        $limit: Int        $page: Int        $translationType: VaildTranslationTypeEnumType
      $countryOrigin: VaildCountryOriginEnumType    ) {    shows(        search: $search        limit: $limit        page: $page        translationType: $translationType        countryOrigin: $countryOrigin    ) {        edges {            _id name availableEpisodes __typename       }    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|Show|\
|g'
++ sed -nE 's|.*_id":"([^"]*)","name":"([^"]*)".*sub":([1-9][^,]*).*|\1 \2 (\3 episodes)|p'
+ anime_list='2wkWm2dJdptKw92Sy Pink Mizu Dorobou Ame Dorobou (1 episodes)
zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
njKtHnTzXYtK748KM       Akira (1 episodes)
pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)'
+ '[' -z '2wkWm2dJdptKw92Sy     Pink Mizu Dorobou Ame Dorobou (1 episodes)
zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
njKtHnTzXYtK748KM       Akira (1 episodes)
pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)' ']'
+ '[' '' -eq '' ']'
+ '[' -z '' ']'
++ printf %s '2wkWm2dJdptKw92Sy Pink Mizu Dorobou Ame Dorobou (1 episodes)
zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
njKtHnTzXYtK748KM       Akira (1 episodes)
pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)'
++ nl -w 2
++ sed 's/^[[:space:]]//'
++ nth 'Select anime: '
+++ cat -
++ stdin='1     2wkWm2dJdptKw92Sy       Pink Mizu Dorobou Ame Dorobou (1 episodes)
2       zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
3       njKtHnTzXYtK748KM       Akira (1 episodes)
4       pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)'
++ '[' -z '1    2wkWm2dJdptKw92Sy       Pink Mizu Dorobou Ame Dorobou (1 episodes)
2       zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
3       njKtHnTzXYtK748KM       Akira (1 episodes)
4       pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)' ']'
+++ printf '%s\n' '1    2wkWm2dJdptKw92Sy       Pink Mizu Dorobou Ame Dorobou (1 episodes)
2       zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
3       njKtHnTzXYtK748KM       Akira (1 episodes)
4       pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=4
++ '[' 4 -eq 1 ']'
++ prompt='Select anime: '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s '1        2wkWm2dJdptKw92Sy       Pink Mizu Dorobou Ame Dorobou (1 episodes)
2       zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
3       njKtHnTzXYtK748KM       Akira (1 episodes)
4       pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Select anime: '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Select anime: '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Select anime: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=3
++ '[' -n 3 ']'
++ printf %s '1 2wkWm2dJdptKw92Sy       Pink Mizu Dorobou Ame Dorobou (1 episodes)
2       zgu4NmGPH7Ynt5pSf       Kennosuke-sama (1 episodes)
3       njKtHnTzXYtK748KM       Akira (1 episodes)
4       pEG8rBMvk3CCKMhNR       Hand Maid May: Akiramemasen (1 episodes)'
++ grep -E '^3($|[[:space:]])'
++ cut -f2,3
+ result='njKtHnTzXYtK748KM     Akira (1 episodes)'
+ '[' -z 'njKtHnTzXYtK748KM     Akira (1 episodes)' ']'
++ printf %s 'njKtHnTzXYtK748KM Akira (1 episodes)'
++ cut -f2
+ title='Akira (1 episodes)'
++ printf %s 'Akira (1 episodes)'
++ cut '-d(' -f1
++ tr -d '[:punct:]'
+ allanime_title='Akira '
++ printf %s 'njKtHnTzXYtK748KM Akira (1 episodes)'
++ cut -f1
+ id=njKtHnTzXYtK748KM
++ episodes_list njKtHnTzXYtK748KM
++ episodes_list_gql='query ($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"njKtHnTzXYtK748KM"}' --data-urlencode 'query=query ($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed -nE 's|.*sub":\[([0-9.",]*)\].*|\1|p'
++ sed 's|,|\
|g; s|"||g'
++ sort -n -k 1
+ ep_list=1
+ '[' -z '' ']'
++ printf %s 1
++ nth 'Select episode: ' -m
+++ cat -
++ stdin=1
++ '[' -z 1 ']'
+++ printf '%s\n' 1
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=1
++ '[' 1 -eq 1 ']'
++ printf %s 1
++ cut -f2,3
++ return 0
+ ep_no=1
+ '[' -z 1 ']'
+ tput cuu1
+ tput el
+ tput sc
+ play
++ printf %s 1
++ grep -Eo '^(-1|[0-9]+(\.[0-9]+)?)'
+ start=1
++ printf %s 1
++ grep -Eo '(-1|[0-9]+(\.[0-9]+)?)$'
+ end=1
+ '[' 1 = -1 ']'
+ '[' -z 1 ']'
+ '[' 1 = 1 ']'
+ unset start end
+ '[' '' = -1 ']'
++ printf '%s\n' 1
++ wc -l
++ tr -d '[:space:]'
+ line_count=1
+ '[' 1 '!=' 1 ']'
+ '[' -n '' ']'
+ play_episode
+ aniskip_title='Akira (1 episodes)'
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ get_episode_url
+ episode_embed_gql='query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}'
++ curl -e https://allanime.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"njKtHnTzXYtK748KM","translationType":"sub","episodeString":"1"}' --data-urlencode 'query=query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ tr '{}' '\n'
++ sed 's|\\u002F|\/|g;s|\\||g'
++ sed -nE 's|.*sourceUrl":"--([^"]*)".*sourceName":"([^"]*)".*|\2 :\1|p'
+ resp='Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590f174e515c5d574b175652734c70566c4260614c730f0c007375174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0a010e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e0f0e5c0e5e0f0c0e0f0a5a0b0d0b0d0b090b010e0b0f0e0b5a0b0b0b090b5e0b0f0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ mktemp -d
+ cache_dir=/tmp/tmp.pRHO7GpSnG
+ providers='1 2 3 4 5'
+ for provider in $providers
+ for provider in $providers
+ generate_link 1
+ case $1 in
+ provider_init wixmp '/Default :/p'
+ provider_name=wixmp
+ for provider in $providers
+ generate_link 2
+ case $1 in
+ provider_init dropbox '/Sak :/p'
+ provider_name=dropbox
+ for provider in $providers
+ generate_link 3
+ case $1 in
+ provider_init wetransfer '/Kir :/p'
+ provider_name=wetransfer
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590f174e515c5d574b175652734c70566c4260614c730f0c007375174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0a010e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e0f0e5c0e5e0f0c0e0f0a5a0b0d0b0d0b090b010e0b0f0e0b5a0b0b0b090b5e0b0f0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
+ for provider in $providers
+ generate_link 4
+ case $1 in
+ provider_init sharepoint '/S-mp4 :/p'
+ provider_name=sharepoint
++ sed -n '/Default :/p'
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590f174e515c5d574b175652734c70566c4260614c730f0c007375174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0a010e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e0f0e5c0e5e0f0c0e0f0a5a0b0d0b0d0b090b010e0b0f0e0b5a0b0b0b090b5e0b0f0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
+ wait
+ generate_link 5
+ case $1 in
+ provider_init gogoanime '/Luf-mp4 :/p'
+ provider_name=gogoanime
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590f174e515c5d574b175652734c70566c4260614c730f0c007375174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0a010e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e0f0e5c0e5e0f0c0e0f0a5a0b0d0b0d0b090b010e0b0f0e0b5a0b0b0b090b5e0b0f0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ head -1
++ sed -n '/Sak :/p'
++ sed -n '/Kir :/p'
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590f174e515c5d574b175652734c70566c4260614c730f0c007375174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0a010e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e0f0e5c0e5e0f0c0e0f0a5a0b0d0b0d0b090b010e0b0f0e0b5a0b0b0b090b5e0b0f0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ cut -d: -f2
++ head -1
++ sed -n '/S-mp4 :/p'
++ cut -d: -f2
++ head -1
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590f174e515c5d574b175652734c70566c4260614c730f0c007375174b4d5a1709
++ sed 's/../&\
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0d0e5d0e0f0f0c0f0a0e590e010f0b0f0d0f0a0f5e0a010e0f0e000e5e0e5a0e0b0a010e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e000e5b0c5c0f0a0c5d0e000d0a0f5b0d5d0d5e0f0a0c5c0b090b0a0b5d0c5c0c5a0d010b0f0d010f0d0f0b0e0c0a0c0f5a
/g'
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010e0f0e5c0e5e0f0c0e0f0a5a0b0d0b0d0b090b010e0b0f0e0b5a0b0b0b090b5e0b0f0b080a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b080a5a0b0e0b0c0d0a0b0f0b0d0b5b0b0d0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ sed -n '/Luf-mp4 :/p'
++ head -1
++ cut -d: -f2
++ cut -d: -f2
++ sed 's/../&\
++ head -1
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
/g'
++ sed 's/../&\
/g'
++ tr -d '\n'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ cut -d: -f2
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ sed 's/../&\
/g'
++ sed 's/../&\
++ tr -d '\n'
++ tr -d '\n'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
++ sed 's/\/clock/\/clock\.json/'
/g'
+ provider_id=
+ '[' -n '' ']'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/^01$/9/g;s/^08$/0/g;s/^05$/=/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^07$/?/g;s/^00$/8/g;s/^5c$/d/g;s/^0f$/7/g;s/^5e$/f/g;s/^17$/\//g;s/^54$/l/g;s/^09$/1/g;s/^48$/p/g;s/^4f$/w/g;s/^0e$/6/g;s/^5b$/c/g;s/^5d$/e/g;s/^0d$/5/g;s/^53$/k/g;s/^1e$/\&/g;s/^5a$/b/g;s/^59$/a/g;s/^4a$/r/g;s/^4c$/t/g;s/^4e$/v/g;s/^57$/o/g;s/^51$/i/g;'
+ provider_id=
+ provider_id=
+ '[' -n '' ']'
+ '[' -n '' ']'
++ sed 's/\/clock/\/clock\.json/'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
+ provider_id=
+ '[' -n '' ']'
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6329686c4d724e68527c5e5f724d31323e4d4b593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b686c4d724e68527c5e5f724d31323e4d4b593759757364247b'
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6329686c4d724e68527c5e5f724d31323e4d4b593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b686c4d724e68527c5e5f724d31323e4d4b593759757364247b' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6329686c4d724e68527c5e5f724d31323e4d4b593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b686c4d724e68527c5e5f724d31323e4d4b593759757364247b'
++ curl -e https://allanime.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c2429756f72637529656e6774726a697375727f2967686f6b6329686c4d724e68527c5e5f724d31323e4d4b593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36302b36345237353c35373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b686c4d724e68527c5e5f724d31323e4d4b593759757364247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+ case "$episode_link" in
+ '[' -n 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw' ']'
+ printf '%s\n' 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+ '[' -z '' ']'
+ printf '\033[1;32m%s\033[0m Links Fetched\n' sharepoint
sharepoint Links Fetched
++ cat /tmp/tmp.pRHO7GpSnG/1 /tmp/tmp.pRHO7GpSnG/2 /tmp/tmp.pRHO7GpSnG/3 /tmp/tmp.pRHO7GpSnG/4 /tmp/tmp.pRHO7GpSnG/5
++ sed 's|^Mp4-||g;/http/!d'
++ sort -g -r -s
+ links='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+ rm -r /tmp/tmp.pRHO7GpSnG
++ select_quality 1080p
++ case "$1" in
+++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+++ grep -m 1 1080p
++ result=
++ '[' -z '' ']'
++ printf 'Specified quality not found, defaulting to best\n'
Specified quality not found, defaulting to best
+++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+++ head -n1
++ result='Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
++ printf %s 'Mp4 >https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
++ cut '-d>' -f2
+ episode='https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+ '[' -z 'https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw' ']'
+ case "$player_function" in
+ '[' 0 = 0 ']'
+ replay='https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+ unset episode
+ nohup mpv.exe --profile=anime '--force-media-title=Akira Episode 1' 'https://myanime.sharepoint.com/sites/chartlousty/_layouts/15/download.aspx?share=EcasQDsslkpNkd12d7m7geIB3tLjoIGp72L6aCysRNjeIw'
+ update_history
+ grep -q -- njKtHnTzXYtK748KM /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ sed -E 's/^[^\t]+\tnjKtHnTzXYtK748KM\t/1\tnjKtHnTzXYtK748KM\t/' /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ mv /c/Users/<USER>/.local/state/ani-cli/ani-hsts.new /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' 0 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ tput rc
+ tput ed
+ '[' mpv.exe = download ']'
+ '[' mpv.exe = debug ']'
++ printf 'next\nreplay\nprevious\nselect\nhistory\nchange_quality\nsearch_new\nquit'
++ nth 'Playing episode 1 of Akira (1 episodes)... '
+++ cat -
++ stdin='next
replay
previous
select
history
change_quality
search_new
quit'
++ '[' -z 'next
replay
previous
select
history
change_quality
search_new
quit' ']'
+++ printf '%s\n' 'next
replay
previous
select
history
change_quality
search_new
quit'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=8
++ '[' 8 -eq 1 ']'
++ prompt='Playing episode 1 of Akira (1 episodes)... '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s 'next
replay
previous
select
history
change_quality
search_new
quit'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Playing episode 1 of Akira (1 episodes)... '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Playing episode 1 of Akira (1 episodes)... '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Playing episode 1 of Akira (1 episodes)... '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=quit
++ '[' -n quit ']'
++ printf %s 'next
replay
previous
select
history
change_quality
search_new
quit'
++ grep -E '^quit($|[[:space:]])'
++ cut -f2,3
+ cmd=quit
+ case "$cmd" in
+ exit 0