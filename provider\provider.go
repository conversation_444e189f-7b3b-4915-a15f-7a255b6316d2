package provider

import (
	"fmt"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"strconv"
)

// ProviderType represents different types of video providers
const (
	ProviderWixMP    = "wixmp"
	ProviderDropbox  = "dropbox"
	ProviderWeTransfer = "wetransfer"
	ProviderSharePoint = "sharepoint"
	ProviderGogoAnime = "gogoanime"
)

// parseURL parses a URL string and returns the parsed URL
func parseURL(urlStr string) (*url.URL, error) {
	return url.Parse(urlStr)
}

// Provider represents a video provider
// Each provider has its own specific URL processing logic
type Provider struct {
	Name     string
	URL      string
	Quality  string
	Priority int
	Type    string
}

// ProviderManager manages different video providers
// It handles URL processing and quality selection
type ProviderManager struct {
	providers []Provider
	quality   string
}

// NewProviderManager creates a new provider manager
func NewProviderManager(quality string) *ProviderManager {
	return &ProviderManager{
		quality: quality,
	}
}

// AddProvider adds a new provider to the manager
func (p *ProviderManager) AddProvider(provider Provider) {
	p.providers = append(p.providers, provider)
}

// GetProviders returns all providers
func (p *ProviderManager) GetProviders() []Provider {
	return p.providers
}

// SelectQuality selects a provider based on quality preference
// If exact quality isn't found, returns the best available
func (p *ProviderManager) SelectQuality() *Provider {
	// Sort providers by quality (highest to lowest)
	sort.Slice(p.providers, func(i, j int) bool {
		qi, _ := parseQuality(p.providers[i].Quality)
		qj, _ := parseQuality(p.providers[j].Quality)
		return qi > qj
	})

	// Try to find exact match first
	for _, provider := range p.providers {
		if strings.EqualFold(provider.Quality, p.quality) {
			return &provider
		}
	}

	// If not found, return highest quality
	if len(p.providers) > 0 {
		fmt.Printf("Specified quality not found, defaulting to best (%s)\n", p.providers[0].Quality)
		return &p.providers[0]
	}

	return nil
}

// parseQuality converts quality string to numeric value for sorting
func parseQuality(quality string) (int, error) {
	quality = strings.ToLower(quality)
	switch quality {
	case "1080p":
		return 1080, nil
	case "720p":
		return 720, nil
	case "480p":
		return 480, nil
	case "360p":
		return 360, nil
	default:
		// Try to parse as number
		if num, err := strconv.Atoi(strings.TrimSuffix(quality, "p")); err == nil {
			return num, nil
		}
		return 0, fmt.Errorf("unknown quality: %s", quality)
	}
}

// ProcessURL processes a URL and returns the best quality version
func ProcessURL(url string) (string, error) {
	// First, check if it's a YouTube URL (starts with --)
	if strings.HasPrefix(url, "--") {
		return processYouTubeURL(url)
	}

	// Create provider manager
	manager := NewProviderManager("best")

	// Get all providers for this URL
	providers, err := getProviders(url)
	if err != nil {
		return "", err
	}

	// Add providers to manager
	for _, prov := range providers {
		manager.AddProvider(prov)
	}

	// Select best quality provider
	selected := manager.SelectQuality()
	if selected == nil {
		return "", fmt.Errorf("no suitable providers found")
	}

	return selected.URL, nil
}

// processYouTubeURL processes YouTube URLs with custom decoding
func processYouTubeURL(rawURL string) (string, error) {
	// Remove the -- prefix
	url := rawURL[2:]

	// Custom decoding mapping from bash script
	decodingMap := map[string]string{
		"01": "9", "08": "0", "05": "=", "0a": "2", "0b": "3",
		"0c": "4", "07": "?", "00": "8", "5c": "d", "0f": "7",
		"5e": "f", "17": "/", "54": "l", "09": "1", "48": "p",
		"4f": "w", "0e": "6", "5b": "c", "5d": "e", "0d": "5",
		"53": "k", "1e": "&", "5a": "b", "59": "a", "4a": "r",
		"4c": "t", "4e": "v", "57": "o", "51": "i",
	}

	// Decode using custom mapping
	decoded := []byte{}
	for i := 0; i < len(url); i += 2 {
		if i+2 > len(url) {
			break
		}
		key := url[i:i+2]
		if val, ok := decodingMap[key]; ok {
			decoded = append(decoded, val...)
		} else {
			// If no mapping found, keep original characters
			decoded = append(decoded, key...)
		}
	}

	// Add /clock.json suffix
	return string(decoded) + "/clock.json", nil
}

// getProviders returns all available providers for a URL
func getProviders(url string) ([]Provider, error) {
	// Parse URL
	parsedURL, err := parseURL(url)
	if err != nil {
		return nil, err
	}

	// Determine provider type based on URL
	providers := []Provider{}
	switch {
	case strings.Contains(parsedURL.Host, "wixmp"):
		providers = append(providers, Provider{
			Name:    ProviderWixMP,
			URL:     url,
			Quality: extractQualityFromURL(url),
		})
	case strings.Contains(parsedURL.Host, "dropbox"):
		providers = append(providers, Provider{
			Name:    ProviderDropbox,
			URL:     url,
			Quality: "mp4",
		})
	case strings.Contains(parsedURL.Host, "wetransfer"):
		providers = append(providers, Provider{
			Name:    ProviderWeTransfer,
			URL:     url,
			Quality: "mp4",
		})
	case strings.Contains(parsedURL.Host, "sharepoint"):
		providers = append(providers, Provider{
			Name:    ProviderSharePoint,
			URL:     url,
			Quality: "mp4",
		})
	case strings.Contains(parsedURL.Host, "gogoanime"):
		providers = append(providers, Provider{
			Name:    ProviderGogoAnime,
			URL:     url,
			Quality: extractQualityFromURL(url),
		})
	default:
		return nil, fmt.Errorf("unknown provider: %s", parsedURL.Host)
	}

	return providers, nil
}

// extractQualityFromURL extracts quality information from URL
func extractQualityFromURL(url string) string {
	// Extract quality from URL path or query parameters
	// This should be implemented based on the specific URL format
	// For example, for WixMP URLs, quality might be in the path
	// For GogoAnime, quality might be in query parameters
	// For now, just return a default value
	return "best"
}

// processWixMPURL handles WixMP URLs with quality variants
func processWixMPURL(url string) (string, error) {
	// Extract base URL
	baseURL := strings.Replace(url, "repackager.wixmp.com/", "", 1)
	baseURL = strings.Replace(baseURL, ".urlset", "", 1)

	// Extract quality from URL
	qualityPattern := regexp.MustCompile(`/([^/]*),/mp4`)
	match := qualityPattern.FindStringSubmatch(url)
	if len(match) < 2 {
		return "", fmt.Errorf("invalid WixMP URL format")
	}

	quality := match[1]
	return fmt.Sprintf("%s,%s,/mp4", baseURL, quality), nil
}

// processGogoAnimeURL handles GogoAnime URLs with M3U8 playlists
func processGogoAnimeURL(url string) (string, error) {
	// Check if it's an M3U8 playlist
	if strings.Contains(url, "original.m3u") {
		return url, nil
	}

	// Extract relative path
	relativePath := strings.TrimSuffix(url, "/playlist.m3u8")
	relativePath = strings.TrimSuffix(relativePath, "/index.m3u8")

	// Get playlist and process segments
	playlistURL, err := parseURL(url)
	if err != nil {
		return "", fmt.Errorf("invalid URL format: %v", err)
	}

	// Get segments from playlist
	// This is a simplified version - you might want to implement full M3U8 parsing
	segments := strings.Split(playlistURL.Path, "/")
	if len(segments) < 2 {
		return "", fmt.Errorf("invalid GogoAnime URL format")
	}

	// Return the first segment as URL
	return fmt.Sprintf("%s://%s/%s", playlistURL.Scheme, playlistURL.Host, segments[len(segments)-1]), nil
}
