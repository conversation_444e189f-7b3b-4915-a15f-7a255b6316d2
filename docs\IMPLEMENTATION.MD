To replicate the process of fetching video links from the AllAnime API in Go, as done in the `ani-cli` bash script, we need to follow a series of steps that involve making HTTP requests, processing JSON responses, decoding provider URLs, fetching video links, and selecting the best link based on quality. Below, I’ll provide detailed instructions and a complete Go implementation that mirrors this functionality, ultimately preparing a video link to pass to a player like `mpv`.

---

### Overview of the Process

1. **Send GraphQL Query**: Make an initial API call to retrieve embed URLs for a specific episode.
2. **Process Embed URLs**: Extract and decode provider-specific URLs from the response.
3. **Fetch Video Links**: For each provider, make a second HTTP request to get actual video links.
4. **Parse and Select Links**: Handle different response formats, extract video links with resolutions, and select the best one based on quality.
5. **Prepare for Playback**: Output the selected link (and optional flags like subtitles or referer) for use with a player.

We’ll implement this in Go using standard libraries like `net/http` for HTTP requests, `encoding/json` for JSON parsing, and `sort` for link selection, along with concurrency to fetch links from multiple providers simultaneously.

---

### Step-by-Step Instructions and Code

#### 1. Define Constants and Data Structures

First, set up constants for API endpoints and headers, and define structs to model the GraphQL request/response and video link data.

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "sort"
    "strings"
    "sync"
)

const (
    allanimeAPI  = "https://api.allanime.day/api"
    allanimeBase = "allanime.day"
    allanimeRefr = "https://allmanga.to"
    userAgent    = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
)

// GraphQLRequest represents the request body for the episode query
type GraphQLRequest struct {
    Query     string                 `json:"query"`
    Variables map[string]interface{} `json:"variables"`
}

// EpisodeResponse models the GraphQL response
type EpisodeResponse struct {
    Data struct {
        Episode struct {
            EpisodeString string `json:"episodeString"`
            SourceUrls    []struct {
                SourceURL  string `json:"sourceUrl"`
                SourceName string `json:"sourceName"`
            } `json:"sourceUrls"`
        } `json:"episode"`
    } `json:"data"`
}

// VideoLink holds information about a video stream
type VideoLink struct {
    Resolution string
    URL        string
    IsM3U8     bool
    Referer    string
    Subtitle   string
}

// Provider mapping from number to sourceName
var providerNames = map[int]string{
    1: "Default",   // wixmp
    2: "Yt-mp4",    // youtube
    3: "S-mp4",     // sharepoint
    4: "Luf-Mp4",   // hianime
}
```

#### 2. Define the Hex Decoding Map

The bash script decodes `sourceUrl` strings by replacing hex pairs with characters. We’ll replicate this with a Go map.

```go
var hexToChar = map[string]string{
    "79": "A", "7a": "B", "7b": "C", "7c": "D", "7d": "E", "7e": "F", "7f": "G",
    "70": "H", "71": "I", "72": "J", "73": "K", "74": "L", "75": "M", "76": "N",
    "77": "O", "68": "P", "69": "Q", "6a": "R", "6b": "S", "6c": "T", "6d": "U",
    "6e": "V", "6f": "W", "60": "X", "61": "Y", "62": "Z", "59": "a", "5a": "b",
    "5b": "c", "5c": "d", "5d": "e", "5e": "f", "5f": "g", "50": "h", "51": "i",
    "52": "j", "53": "k", "54": "l", "55": "m", "56": "n", "57": "o", "48": "p",
    "49": "q", "4a": "r", "4b": "s", "4c": "t", "4d": "u", "4e": "v", "4f": "w",
    "40": "x", "41": "y", "42": "z", "08": "0", "09": "1", "0a": "2", "0b": "3",
    "0c": "4", "0d": "5", "0e": "6", "0f": "7", "00": "8", "01": "9", "15": "-",
    "16": ".", "67": "_", "46": "~", "02": ":", "17": "/", "07": "?", "1b": "#",
    "63": "[", "65": "]", "78": "@", "19": "!", "1c": "$", "1e": "&", "10": "(",
    "11": ")", "12": "*", "13": "+", "14": ",", "03": ";", "05": "=", "1d": "%",
}

// decodeSourceURL decodes a hex-encoded sourceUrl into a provider ID
func decodeSourceURL(encoded string) string {
    if strings.HasPrefix(encoded, "--") {
        encoded = encoded[2:]
    }
    var decoded strings.Builder
    for i := 0; i < len(encoded)-1; i += 2 {
        pair := encoded[i : i+2]
        if char, ok := hexToChar[pair]; ok {
            decoded.WriteString(char)
        } else {
            decoded.WriteString(pair)
        }
    }
    result := decoded.String()
    if strings.Contains(result, "/clock") {
        result = strings.Replace(result, "/clock", "/clock.json", 1)
    }
    return result
}
```

#### 3. Fetch Embed URLs via GraphQL

Send the initial GraphQL query to get `sourceUrls` for the episode.

```go
// getEmbedURLs fetches embed URLs from the AllAnime API
func getEmbedURLs(showID, translationType, episodeString string) (*EpisodeResponse, error) {
    query := `query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {
        episode(showId: $showId translationType: $translationType episodeString: $episodeString) {
            episodeString
            sourceUrls
        }
    }`
    variables := map[string]interface{}{
        "showId":          showID,
        "translationType": translationType,
        "episodeString":   episodeString,
    }
    requestBody, err := json.Marshal(GraphQLRequest{Query: query, Variables: variables})
    if err != nil {
        return nil, fmt.Errorf("marshaling request: %v", err)
    }

    req, err := http.NewRequest("POST", allanimeAPI, bytes.NewReader(requestBody))
    if err != nil {
        return nil, fmt.Errorf("creating request: %v", err)
    }
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("User-Agent", userAgent)
    req.Header.Set("Referer", allanimeRefr)

    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("executing request: %v", err)
    }
    defer resp.Body.Close()

    var episodeResp EpisodeResponse
    if err := json.NewDecoder(resp.Body).Decode(&episodeResp); err != nil {
        return nil, fmt.Errorf("decoding response: %v", err)
    }
    return &episodeResp, nil
}
```

#### 4. Fetch Video Links for Each Provider

Use goroutines to fetch links concurrently for each provider, decoding `sourceUrl` to get the `providerID`.

```go
// fetchVideoLinksForProvider fetches video links for a given provider
func fetchVideoLinksForProvider(sourceURL, sourceName, expectedName string) ([]VideoLink, error) {
    if sourceName != expectedName {
        return nil, nil // Skip if sourceName doesn’t match provider
    }
    providerID := decodeSourceURL(sourceURL)
    url := "https://" + allanimeBase + "/" + providerID

    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        return nil, fmt.Errorf("creating request for %s: %v", url, err)
    }
    req.Header.Set("User-Agent", userAgent)
    req.Header.Set("Referer", allanimeRefr)

    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("fetching %s: %v", url, err)
    }
    defer resp.Body.Close()

    return parseVideoLinks(resp.Body, providerID)
}

// parseVideoLinks parses the response to extract video links
func parseVideoLinks(body io.Reader, providerID string) ([]VideoLink, error) {
    bodyBytes, err := io.ReadAll(body)
    if err != nil {
        return nil, fmt.Errorf("reading response body: %v", err)
    }
    responseStr := string(bodyBytes)

    if strings.Contains(responseStr, "repackager.wixmp.com") {
        // Handle Wixmp (simplified; adapt based on actual response)
        return []VideoLink{{URL: responseStr, Resolution: "unknown"}}, nil
    } else if strings.Contains(responseStr, "master.m3u8") {
        return parseM3U8Links(responseStr, providerID)
    }
    // Default case: assume direct link or JSON with links
    var data struct {
        Links []struct {
            Link       string `json:"link"`
            Resolution string `json:"resolutionStr"`
            HLS        bool   `json:"hls"`
            Subtitles  []struct {
                Src string `json:"src"`
            } `json:"subtitles"`
            Headers struct {
                Referer string `json:"Referer"`
            } `json:"headers"`
        } `json:"links"`
    }
    if err := json.Unmarshal(bodyBytes, &data); err == nil && len(data.Links) > 0 {
        var links []VideoLink
        for _, link := range data.Links {
            vl := VideoLink{
                Resolution: link.Resolution,
                URL:        link.Link,
                IsM3U8:     link.HLS,
                Referer:    link.Headers.Referer,
            }
            if len(link.Subtitles) > 0 {
                vl.Subtitle = link.Subtitles[0].Src
            }
            links = append(links, vl)
        }
        return links, nil
    }
    // Fallback: treat as direct link
    return []VideoLink{{URL: responseStr, Resolution: "unknown"}}, nil
}

// parseM3U8Links fetches and parses an M3U8 playlist
func parseM3U8Links(responseStr, providerID string) ([]VideoLink, error) {
    type LinkData struct {
        Links []struct {
            Link      string `json:"link"`
            HLS       bool   `json:"hls"`
            Subtitles []struct {
                Src string `json:"src"`
            } `json:"subtitles"`
            Headers struct {
                Referer string `json:"Referer"`
            } `json:"headers"`
        } `json:"links"`
    }

    var data LinkData
    if err := json.Unmarshal([]byte(responseStr), &data); err != nil || len(data.Links) == 0 {
        return nil, fmt.Errorf("parsing M3U8 JSON: %v", err)
    }

    m3u8URL := data.Links[0].Link
    referer := data.Links[0].Headers.Referer
    subtitle := ""
    if len(data.Links[0].Subtitles) > 0 {
        subtitle = data.Links[0].Subtitles[0].Src
    }

    req, err := http.NewRequest("GET", m3u8URL, nil)
    if err != nil {
        return nil, fmt.Errorf("creating M3U8 request: %v", err)
    }
    req.Header.Set("User-Agent", userAgent)
    req.Header.Set("Referer", referer)

    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("fetching M3U8: %v", err)
    }
    defer resp.Body.Close()

    bodyBytes, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("reading M3U8 body: %v", err)
    }
    playlist := string(bodyBytes)

    if !strings.Contains(playlist, "#EXTM3U") {
        return nil, fmt.Errorf("invalid M3U8 playlist")
    }

    var links []VideoLink
    lines := strings.Split(playlist, "\n")
    for i := 0; i < len(lines)-1; i++ {
        if strings.HasPrefix(lines[i], "#EXT-X-STREAM-INF") {
            resolution := ""
            if strings.Contains(lines[i], "1920x1080") {
                resolution = "1080p"
            } else if strings.Contains(lines[i], "1280x720") {
                resolution = "720p"
            } else if strings.Contains(lines[i], "640x360") {
                resolution = "360p"
            }
            streamURL := lines[i+1]
            if !strings.HasPrefix(streamURL, "http") {
                baseURL := strings.TrimSuffix(m3u8URL, "master.m3u8")
                streamURL = baseURL + streamURL
            }
            links = append(links, VideoLink{
                Resolution: resolution,
                URL:        streamURL,
                IsM3U8:     true,
                Referer:    referer,
                Subtitle:   subtitle,
            })
            i++ // Skip the URL line
        }
    }
    return links, nil
}
```

#### 5. Main Function to Get Video Links

Tie it all together, fetching and selecting the best link.

```go
// getVideoLinks retrieves and selects the best video link
func getVideoLinks(showID, translationType, episodeString, quality string) (VideoLink, error) {
    embedResp, err := getEmbedURLs(showID, translationType, episodeString)
    if err != nil {
        return VideoLink{}, err
    }

    var wg sync.WaitGroup
    linkCh := make(chan []VideoLink, len(providerNames))
    for providerNum, providerName := range providerNames {
        wg.Add(1)
        go func(num int, name string) {
            defer wg.Done()
            for _, source := range embedResp.Data.Episode.SourceUrls {
                links, err := fetchVideoLinksForProvider(source.SourceURL, source.SourceName, name)
                if err == nil && len(links) > 0 {
                    linkCh <- links
                }
            }
        }(providerNum, providerName)
    }

    wg.Wait()
    close(linkCh)

    var allLinks []VideoLink
    for links := range linkCh {
        allLinks = append(allLinks, links...)
    }

    if len(allLinks) == 0 {
        return VideoLink{}, fmt.Errorf("no video links found")
    }

    // Sort by resolution (assuming numeric values like 1080p, 720p)
    sort.Slice(allLinks, func(i, j int) bool {
        resI := strings.TrimSuffix(allLinks[i].Resolution, "p")
        resJ := strings.TrimSuffix(allLinks[j].Resolution, "p")
        if resI == "" || resJ == "" {
            return allLinks[i].Resolution > allLinks[j].Resolution
        }
        ri, _ := strconv.Atoi(resI)
        rj, _ := strconv.Atoi(resJ)
        return ri > rj
    })

    // Select link matching quality
    for _, link := range allLinks {
        if link.Resolution == quality {
            return link, nil
        }
    }
    return allLinks[0], nil // Default to best (first) link
}

func main() {
    showID := "ed6654HMukxd8TKJ7" // Example show ID
    translationType := "sub"
    episodeString := "1"
    quality := "1080p"

    link, err := getVideoLinks(showID, translationType, episodeString, quality)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }

    fmt.Printf("Selected Video Link:\n")
    fmt.Printf("URL: %s\n", link.URL)
    if link.Resolution != "" {
        fmt.Printf("Resolution: %s\n", link.Resolution)
    }
    if link.Referer != "" {
        fmt.Printf("Referer: %s\n", link.Referer)
    }
    if link.Subtitle != "" {
        fmt.Printf("Subtitle: %s\n", link.Subtitle)
    }

    // Example: Pass to mpv (uncomment to execute)
    // cmd := exec.Command("mpv", link.URL, "--force-media-title=Mushoku Tensei Episode 1",
    //     fmt.Sprintf("--sub-file=%s", link.Subtitle), fmt.Sprintf("--referrer=%s", link.Referer))
    // if err := cmd.Run(); err != nil {
    //     fmt.Printf("Error running mpv: %v\n", err)
    // }
}
```

---

### Explanation of the Code

- **GraphQL Request**: `getEmbedURLs` sends the initial query and unmarshals the response into a structured format.
- **Concurrent Fetching**: Goroutines fetch links for each provider, matching `sourceName` to the expected provider name and decoding `sourceUrl`.
- **Response Parsing**: `parseVideoLinks` handles different response types:
  - Direct links (e.g., SharePoint mp4).
  - M3U8 playlists, fetching and parsing the playlist to extract streams.
  - JSON-formatted responses with link metadata.
- **Link Selection**: `getVideoLinks` collects all links, sorts them by resolution, and selects the desired quality or the best available.
- **Error Handling**: Each function returns errors to ensure robustness.

---

### Notes and Improvements

- **M3U8 Parsing**: The current M3U8 parser is basic. For production use, consider a library like `github.com/grafov/m3u8` for robust parsing.
- **Response Variability**: The code assumes certain response formats based on the bash script examples. Test with actual API responses to refine parsing logic.
- **Player Integration**: The `main` function shows how to prepare the link for `mpv`. Uncomment and adjust the `exec.Command` call to integrate with your player.
- **Timeouts and Retries**: Add HTTP client timeouts and retry logic for reliability in a production environment.

This implementation provides a complete, self-contained solution to fetch and select video links from AllAnime in Go, ready to pass to a player like `mpv`.