Below is a detailed breakdown of what is happening in the `snippet.log` file, which is a log of the `ani-cli` Bash script executed with `bash -x` for debugging. This log traces the execution of the `get_episode_url` function and subsequent steps to fetch and play an anime episode. I’ll explain each step and then provide guidance on how to replicate this functionality in Go, referencing the `ani-cli` script where necessary.

---

### Detailed Breakdown of `snippet.log`

#### 1. **Function Call: `get_episode_url`**
- **What Happens**: The log begins with the `get_episode_url` function being invoked. This function is responsible for retrieving the URL of an anime episode based on a show ID, translation type (sub/dub), and episode number.
- **Script Reference**: In `ani-cli`, `get_episode_url` is defined to fetch embed URLs from the AllAnime API, process them into direct video links, and select one based on quality.

#### 2. **GraphQL Query Definition**
- **What Happens**: A GraphQL query is assigned to `episode_embed_gql`:
  ```bash
  episode_embed_gql='query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}'
  ```
- **Purpose**: This query requests episode details (specifically `sourceUrls`) from the AllAnime API for a given `showId` (`QusmPJR29sg3G35CB`), `translationType` (`sub`), and `episodeString` (`1`).
- **Script Reference**: This is part of `get_episode_url`, used to construct the API request.

#### 3. **API Request with `curl`**
- **What Happens**: A `curl` command sends a GET request to `https://api.allanime.day/api`:
  ```bash
  curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"QusmPJR29sg3G35CB","translationType":"sub","episodeString":"1"}' --data-urlencode 'query=query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
  ```
  - `-e https://allmanga.to`: Sets the referrer header.
  - `-s`: Silent mode.
  - `-G`: Uses GET method with URL-encoded data.
  - `--data-urlencode`: Encodes the GraphQL variables and query.
  - `-A`: Sets the user agent to mimic Firefox.
- **Output**: The response is processed with `tr` and `sed` to extract `sourceUrls` and their provider names into `resp`:
  ```
  resp='Yt-mp4 :504c4c48...1709
  S-mp4 :17594851...0c0f5a
  Luf-Mp4 :17594851...0c0f5a'
  ```
- **Purpose**: Fetches embed URLs from providers like YouTube (`Yt-mp4`), SharePoint (`S-mp4`), and HiAnime (`Luf-Mp4`).

#### 4. **Processing Providers**
- **What Happens**: The script creates a temporary directory (`cache_dir`) and iterates over four providers (`1 2 3 4`) using `generate_link` in parallel:
  - **Provider 1 (wixmp)**: No `Default` provider is found in `resp`, so `provider_id` is empty, and no links are generated.
  - **Provider 2 (youtube)**: Decodes the `Yt-mp4` ID into `https://tools.fast4speed.rsvp//media8/videos/QusmPJR29sg3G35CB/sub/1`.
  - **Provider 3 (sharepoint)**: Decodes the `S-mp4` ID into a SharePoint URL.
  - **Provider 4 (hianime)**: Decodes the `Luf-Mp4` ID into a `/apivtwo/clock.json` URL.
- **Script Reference**: `generate_link` calls `provider_init` to set `provider_name` and decode `provider_id`, then `get_links` fetches direct URLs.

#### 5. **Fetching Direct Links with `get_links`**
- **YouTube**:
  - URL: `https://tools.fast4speed.rsvp//media8/videos/QusmPJR29sg3G35CB/sub/1`
  - Output: `Yt >https://tools.fast4speed.rsvp//media8/videos/QusmPJR29sg3G35CB/sub/1`
- **HiAnime**:
  - URL: `/apivtwo/clock.json?...`
  - Response: An m3u8 URL (`https://frostywinds57.live/.../master.m3u8`) with subtitles.
  - Processing:
    - Extracts referrer (`https://megacloud.club/`) and subtitle URL.
    - Fetches the m3u8 file and parses it into quality streams (1080p, 720p, 360p).
  - Output:
    ```
    1080p cc>https://frostywinds57.live/.../index-f1-v1-a1.m3u8
    720p cc>https://frostywinds57.live/.../index-f2-v1-a1.m3u8
    360p cc>https://frostywinds57.live/.../index-f3-v1-a1.m3u8
    m3u8_refr >https://megacloud.club/
    subtitle >https://s.megastatics.com/subtitle/e0ee3b7466e93d2c10a98215f8d5cee1/eng-2.vtt
    ```
- **SharePoint**:
  - URL: A SharePoint download link.
  - Output: `Mp4 >https://myanime.sharepoint.com/...`
- **Script Reference**: `get_links` handles different URL types (m3u8, mp4) and extracts stream info.

#### 6. **Selecting Quality with `select_quality`**
- **What Happens**: The script selects the 1080p link:
  - Input `links`: All fetched links.
  - Quality: `1080p`.
  - Output: `result=1080p cc>https://frostywinds57.live/.../index-f1-v1-a1.m3u8`.
  - Sets `subtitle` and `refr_flag` for m3u8 playback.
  - Final `episode`: `https://frostywinds57.live/.../index-f1-v1-a1.m3u8`.
- **Script Reference**: `select_quality` filters links by quality and sets playback flags.

#### 7. **Playing the Episode**
- **What Happens**: The episode is played with `mpv.exe`:
  ```bash
  nohup mpv.exe --force-media-title="Mushoku Tensei II Isekai Ittara Honki Dasu Part 2 Episode 1" https://frostywinds57.live/.../index-f1-v1-a1.m3u8 --sub-file=https://s.megastatics.com/subtitle/e0ee3b7466e93d2c10a98215f8d5cee1/eng-2.vtt --referrer=https://megacloud.club/
  ```
- **Purpose**: Launches MPV in the background to stream the 1080p video with subtitles.

---

### Replicating in Go

To replicate this in Go, you’ll need to handle HTTP requests, JSON parsing, URL decoding, m3u8 parsing, and media playback. Here’s how to approach each step:

#### 1. **Send HTTP Requests**
- **Package**: Use `net/http`.
- **Action**: Replicate the `curl` command to fetch embed URLs.
- **Example**:
  ```go
  func fetchEpisodeData(showID, translationType, episodeString string) (string, error) {
      client := &http.Client{}
      req, err := http.NewRequest("GET", "https://api.allanime.day/api", nil)
      if err != nil {
          return "", err
      }
      q := req.URL.Query()
      q.Add("variables", fmt.Sprintf(`{"showId":"%s","translationType":"%s","episodeString":"%s"}`, showID, translationType, episodeString))
      q.Add("query", "query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}")
      req.URL.RawQuery = q.Encode()
      req.Header.Set("Referer", "https://allmanga.to")
      req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0")
      resp, err := client.Do(req)
      if err != nil {
          return "", err
      }
      defer resp.Body.Close()
      body, err := ioutil.ReadAll(resp.Body)
      if err != nil {
          return "", err
      }
      return string(body), nil
  }
  ```

#### 2. **Parse JSON Responses**
- **Package**: Use `encoding/json`.
- **Action**: Parse the API response to extract `sourceUrls`.
- **Example**:
  ```go
  type EpisodeResponse struct {
      Data struct {
          Episode struct {
              SourceUrls []struct {
                  SourceUrl  string `json:"sourceUrl"`
                  SourceName string `json:"sourceName"`
              } `json:"sourceUrls"`
          } `json:"episode"`
      } `json:"data"`
  }

  func parseEpisodeResponse(body string) ([]map[string]string, error) {
      var resp EpisodeResponse
      if err := json.Unmarshal([]byte(body), &resp); err != nil {
          return nil, err
      }
      urls := make([]map[string]string, 0)
      for _, url := range resp.Data.Episode.SourceUrls {
          urls = append(urls, map[string]string{
              "name": url.SourceName,
              "url":  strings.TrimPrefix(url.SourceUrl, "--"),
          })
      }
      return urls, nil
  }
  ```

#### 3. **Decode Provider IDs**
- **Action**: Translate the `sed` decoding logic from `provider_init`.
- **Example**:
  ```go
  func decodeProviderID(encoded string) string {
      // Mapping from ani-cli's sed substitutions
      decodeMap := map[string]string{
          "79": "A", "7a": "B", "7b": "C", /* ... */ "1d": "%",
      }
      var result strings.Builder
      for i := 0; i < len(encoded); i += 2 {
          pair := encoded[i : i+2]
          if decoded, ok := decodeMap[pair]; ok {
              result.WriteString(decoded)
          } else {
              result.WriteString(pair)
          }
      }
      return strings.Replace(result.String(), "/clock", "/clock.json", 1)
  }
  ```

#### 4. **Handle m3u8 Files**
- **Package**: Use `github.com/grafov/m3u8`.
- **Action**: Fetch and parse m3u8 files to extract stream URLs.
- **Example**:
  ```go
  import "github.com/grafov/m3u8"

  func fetchM3U8Streams(url, referrer string) ([]string, error) {
      req, _ := http.NewRequest("GET", url, nil)
      req.Header.Set("Referer", referrer)
      req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0")
      resp, err := http.DefaultClient.Do(req)
      if err != nil {
          return nil, err
      }
      defer resp.Body.Close()
      playlist, listType, err := m3u8.DecodeFrom(resp.Body, true)
      if err != nil || listType != m3u8.MASTER {
          return nil, err
      }
      master := playlist.(*m3u8.MasterPlaylist)
      streams := make([]string, 0)
      for _, variant := range master.Variants {
          streams = append(streams, fmt.Sprintf("%dp>%s%s", variant.Resolution.Height, strings.TrimSuffix(url, "master.m3u8"), variant.URI))
      }
      return streams, nil
  }
  ```

#### 5. **Select Quality**
- **Action**: Filter links by quality and extract playback parameters.
- **Example**:
  ```go
  func selectQuality(links []string, quality string) (string, string, string) {
      for _, link := range links {
          if strings.HasPrefix(link, quality) {
              parts := strings.SplitN(link, ">", 2)
              episode := parts[1]
              subtitle := "" // Fetch from response if available
              referrer := "https://megacloud.club/" // Fetch from response
              return episode, subtitle, referrer
          }
      }
      return links[0], "", "" // Default to best
  }
  ```

#### 6. **Play the Episode**
- **Package**: Use `os/exec` to run `mpv`.
- **Example**:
  ```go
  func playEpisode(url, title, subtitle, referrer string) error {
      cmd := exec.Command("mpv",
          "--force-media-title="+title,
          url,
          "--sub-file="+subtitle,
          "--referrer="+referrer,
      )
      cmd.Stdout = os.Stdout
      cmd.Stderr = os.Stderr
      return cmd.Start()
  }
  ```

#### **Complete Go Program**
```go
package main

import (
    "fmt"
    "io/ioutil"
    "net/http"
    "strings"
    "os/exec"
)

func main() {
    showID := "QusmPJR29sg3G35CB"
    translationType := "sub"
    episodeString := "1"
    quality := "1080p"

    // Fetch embed URLs
    body, err := fetchEpisodeData(showID, translationType, episodeString)
    if err != nil {
        fmt.Println("Error:", err)
        return
    }

    // Parse response
    providers, err := parseEpisodeResponse(body)
    if err != nil {
        fmt.Println("Error:", err)
        return
    }

    // Process providers
    var links []string
    for _, p := range providers {
        switch p["name"] {
        case "Yt-mp4":
            decoded := decodeProviderID(p["url"])
            links = append(links, "Yt>"+decoded)
        case "Luf-Mp4":
            decoded := decodeProviderID(p["url"])
            resp, _ := http.Get("https://allanime.day" + decoded)
            body, _ := ioutil.ReadAll(resp.Body)
            // Parse JSON and fetch m3u8 (simplified)
            m3u8URL := "https://frostywinds57.live/.../master.m3u8" // From log
            streams, _ := fetchM3U8Streams(m3u8URL, "https://megacloud.club/")
            links = append(links, streams...)
        case "S-mp4":
            decoded := decodeProviderID(p["url"])
            links = append(links, "Mp4>"+decoded)
        }
    }

    // Select quality
    episode, subtitle, referrer := selectQuality(links, quality)
    if episode == "" {
        fmt.Println("No link found")
        return
    }

    // Play
    err = playEpisode(episode, "Mushoku Tensei II Episode 1", subtitle, referrer)
    if err != nil {
        fmt.Println("Error playing:", err)
    }
}
```

---

### Notes
- **Error Handling**: Add robust error checking in production code.
- **Concurrency**: Use goroutines to fetch links in parallel, mimicking the Bash script’s `&`.
- **Dependencies**: Install `github.com/grafov/m3u8` with `go get`.
- **Subtitles/Referrer**: Extract these dynamically from the JSON response as shown in the log.

This should replicate the core functionality of the log in Go. Let me know if you need further details!