# Windsurf AI Rules

- When fixing or modifying any golang code related to this project, always double-check the changes against the following reference files:
  1. Use the information from all files in the "docs" directory to ensure the proper organization, structure, methodology and logic are followed when replicating the bash script (the files are flow.txt, example_flow1.txt, example_flow2.txt, STEPS.MD, TRANSLATE.MD and COMPLETE_STEPS.MD)
  2. The original bash script file `ani-cli` to verify that methods and implementations stay consistent with the original design.

- Ensure that any modifications respect the flow and method conventions outlined in these files before finalizing the fix.

- If there is any ambiguity or conflict between the files and the proposed fix, flag it and request clarification before proceeding.

---

Before applying any changes to the Go code, always:

Compare the proposed changes against the logic and instructions of all files located in the "docs" directory (the files are flow.txt, example_flow1.txt, example_flow2.txt, STEPS.MD, TRANSLATE.MD and COMPLETE_STEPS.MD) to verify that the organization, flow, and structure are consistent with the documented process.

Cross-check with the original bash script `ani-cli` to ensure the Go code uses equivalent methods that achieve the same functionality. Keep reiterating this process until the Go code matches the bash script's functionality.

Verify the Go code's functionality by running the bash script `ani-cli` and comparing the output to the expected results.

If the Go code doesn't match the bash script's functionality, repeat the process until they match.

If the Go code matches the bash script's functionality, proceed to the next step.

Verify that the Go code is properly formatted and follows the Go coding conventions.

Confirm that the implementation aligns with both references before finalizing any modifications.

Note: I am using Windows 10 as my OS and fzf is currently installed.