package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

type DB struct {
	conn       *sql.DB
	dbPath     string
	backupPath string
}

type HistoryEntry struct {
	ID           int       `json:"id"`
	AnimeID      string    `json:"anime_id"`
	Title        string    `json:"title"`
	CurrentEp    int       `json:"current_ep"`
	TotalEps     int       `json:"total_eps"`
	WatchedEps   []int     `json:"watched_eps"`
	LastWatched  time.Time `json:"last_watched"`
	Quality      string    `json:"quality"`
	Mode         string    `json:"mode"` // sub/dub
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func NewDB(dataDir string) (*DB, error) {
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %v", err)
	}

	dbPath := filepath.Join(dataDir, "history.db")
	backupPath := filepath.Join(dataDir, "history.db.backup")

	db := &DB{
		dbPath:     dbPath,
		backupPath: backupPath,
	}

	if err := db.connect(); err != nil {
		return nil, err
	}

	if err := db.createTables(); err != nil {
		return nil, err
	}

	return db, nil
}

func (db *DB) connect() error {
	conn, err := sql.Open("sqlite3", db.dbPath+"?_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000")
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}
	
	db.conn = conn
	return nil
}

func (db *DB) createTables() error {
	query := `
	CREATE TABLE IF NOT EXISTS history (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		anime_id TEXT NOT NULL UNIQUE,
		title TEXT NOT NULL,
		current_ep INTEGER DEFAULT 1,
		total_eps INTEGER DEFAULT 0,
		watched_eps TEXT DEFAULT '[]',
		last_watched DATETIME DEFAULT CURRENT_TIMESTAMP,
		quality TEXT DEFAULT '1080p',
		mode TEXT DEFAULT 'sub',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_anime_id ON history(anime_id);
	CREATE INDEX IF NOT EXISTS idx_last_watched ON history(last_watched DESC);
	`

	if _, err := db.conn.Exec(query); err != nil {
		return fmt.Errorf("failed to create tables: %v", err)
	}

	return nil
}

func (db *DB) CreateBackup() error {
	// Create backup before any write operation
	if _, err := os.Stat(db.dbPath); os.IsNotExist(err) {
		return nil // No database to backup
	}

	src, err := os.Open(db.dbPath)
	if err != nil {
		return fmt.Errorf("failed to open source database: %v", err)
	}
	defer src.Close()

	dst, err := os.Create(db.backupPath)
	if err != nil {
		return fmt.Errorf("failed to create backup file: %v", err)
	}
	defer dst.Close()

	if _, err := dst.ReadFrom(src); err != nil {
		return fmt.Errorf("failed to copy database: %v", err)
	}

	return nil
}

func (db *DB) RestoreFromBackup() error {
	if _, err := os.Stat(db.backupPath); os.IsNotExist(err) {
		return fmt.Errorf("backup file does not exist")
	}

	// Close current connection
	if db.conn != nil {
		db.conn.Close()
	}

	// Copy backup to main database
	src, err := os.Open(db.backupPath)
	if err != nil {
		return fmt.Errorf("failed to open backup file: %v", err)
	}
	defer src.Close()

	dst, err := os.Create(db.dbPath)
	if err != nil {
		return fmt.Errorf("failed to create database file: %v", err)
	}
	defer dst.Close()

	if _, err := dst.ReadFrom(src); err != nil {
		return fmt.Errorf("failed to restore database: %v", err)
	}

	// Reconnect
	return db.connect()
}

func (db *DB) AddOrUpdateEntry(entry HistoryEntry) error {
	// Create backup before write
	if err := db.CreateBackup(); err != nil {
		fmt.Printf("Warning: Failed to create backup: %v\n", err)
	}

	watchedEpsJSON, err := json.Marshal(entry.WatchedEps)
	if err != nil {
		return fmt.Errorf("failed to marshal watched episodes: %v", err)
	}

	query := `
	INSERT INTO history (anime_id, title, current_ep, total_eps, watched_eps, last_watched, quality, mode, created_at, updated_at)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	ON CONFLICT(anime_id) DO UPDATE SET
		title = excluded.title,
		current_ep = excluded.current_ep,
		total_eps = excluded.total_eps,
		watched_eps = excluded.watched_eps,
		last_watched = excluded.last_watched,
		quality = excluded.quality,
		mode = excluded.mode,
		updated_at = excluded.updated_at
	`

	now := time.Now()
	if entry.CreatedAt.IsZero() {
		entry.CreatedAt = now
	}
	entry.UpdatedAt = now

	_, err = db.conn.Exec(query, entry.AnimeID, entry.Title, entry.CurrentEp, entry.TotalEps, 
		string(watchedEpsJSON), entry.LastWatched, entry.Quality, entry.Mode, entry.CreatedAt, entry.UpdatedAt)
	
	return err
}

func (db *DB) GetEntry(animeID string) (*HistoryEntry, error) {
	query := `SELECT id, anime_id, title, current_ep, total_eps, watched_eps, last_watched, quality, mode, created_at, updated_at 
			  FROM history WHERE anime_id = ?`
	
	row := db.conn.QueryRow(query, animeID)
	
	var entry HistoryEntry
	var watchedEpsJSON string
	
	err := row.Scan(&entry.ID, &entry.AnimeID, &entry.Title, &entry.CurrentEp, &entry.TotalEps,
		&watchedEpsJSON, &entry.LastWatched, &entry.Quality, &entry.Mode, &entry.CreatedAt, &entry.UpdatedAt)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	
	if err := json.Unmarshal([]byte(watchedEpsJSON), &entry.WatchedEps); err != nil {
		entry.WatchedEps = []int{}
	}
	
	return &entry, nil
}

func (db *DB) GetContinueOptions() ([]HistoryEntry, error) {
	query := `SELECT id, anime_id, title, current_ep, total_eps, watched_eps, last_watched, quality, mode, created_at, updated_at 
			  FROM history 
			  WHERE current_ep < total_eps OR total_eps = 0
			  ORDER BY last_watched DESC 
			  LIMIT 20`
	
	rows, err := db.conn.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var entries []HistoryEntry
	for rows.Next() {
		var entry HistoryEntry
		var watchedEpsJSON string
		
		err := rows.Scan(&entry.ID, &entry.AnimeID, &entry.Title, &entry.CurrentEp, &entry.TotalEps,
			&watchedEpsJSON, &entry.LastWatched, &entry.Quality, &entry.Mode, &entry.CreatedAt, &entry.UpdatedAt)
		if err != nil {
			continue
		}
		
		if err := json.Unmarshal([]byte(watchedEpsJSON), &entry.WatchedEps); err != nil {
			entry.WatchedEps = []int{}
		}
		
		entries = append(entries, entry)
	}
	
	return entries, nil
}

func (db *DB) MarkEpisodeWatched(animeID string, episode int) error {
	entry, err := db.GetEntry(animeID)
	if err != nil {
		return err
	}
	
	if entry == nil {
		return fmt.Errorf("anime not found in history")
	}
	
	// Add episode to watched list if not already watched
	watched := false
	for _, ep := range entry.WatchedEps {
		if ep == episode {
			watched = true
			break
		}
	}
	
	if !watched {
		entry.WatchedEps = append(entry.WatchedEps, episode)
	}
	
	entry.CurrentEp = episode
	entry.LastWatched = time.Now()
	
	return db.AddOrUpdateEntry(*entry)
}

func (db *DB) DeleteEntry(animeID string) error {
	if err := db.CreateBackup(); err != nil {
		fmt.Printf("Warning: Failed to create backup: %v\n", err)
	}
	
	_, err := db.conn.Exec("DELETE FROM history WHERE anime_id = ?", animeID)
	return err
}

func (db *DB) ClearHistory() error {
	if err := db.CreateBackup(); err != nil {
		fmt.Printf("Warning: Failed to create backup: %v\n", err)
	}
	
	_, err := db.conn.Exec("DELETE FROM history")
	return err
}

func (db *DB) Close() error {
	if db.conn != nil {
		return db.conn.Close()
	}
	return nil
}