package url

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
)

const (
	AllanimeRefr = "https://allanime.to"
	UserAgent    = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
)

type Provider struct {
	Name     string
	ID       string
	Quality  string
	URL      string
	Priority int
}

type Extractor struct {
	client *http.Client
}

func NewExtractor() *Extractor {
	return &Extractor{
		client: &http.Client{},
	}
}

// ExtractLinks extracts video links from the embed URL
func (e *Extractor) ExtractLinks(embedURL string, quality string, priority float64) ([]Provider, error) {
	// Handle YouTube URLs (base64 encoded)
	if strings.HasPrefix(embedURL, "--") {
		parts := strings.Split(embedURL, "--")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid YouTube URL format")
		}
		
		// Decode using the specific mapping from the bash script
		decoded := strings.ReplaceAll(parts[1], "01", "9")
		decoded = strings.ReplaceAll(decoded, "08", "0")
		decoded = strings.ReplaceAll(decoded, "05", "=")
		decoded = strings.ReplaceAll(decoded, "0a", "2")
		decoded = strings.ReplaceAll(decoded, "0b", "3")
		decoded = strings.ReplaceAll(decoded, "0c", "4")
		decoded = strings.ReplaceAll(decoded, "07", "?")
		decoded = strings.ReplaceAll(decoded, "00", "8")
		decoded = strings.ReplaceAll(decoded, "5c", "d")
		decoded = strings.ReplaceAll(decoded, "0f", "7")
		decoded = strings.ReplaceAll(decoded, "5e", "f")
		decoded = strings.ReplaceAll(decoded, "17", "/")
		decoded = strings.ReplaceAll(decoded, "54", "l")
		decoded = strings.ReplaceAll(decoded, "09", "1")
		decoded = strings.ReplaceAll(decoded, "48", "p")
		decoded = strings.ReplaceAll(decoded, "4f", "w")
		decoded = strings.ReplaceAll(decoded, "0e", "6")
		decoded = strings.ReplaceAll(decoded, "5b", "c")
		decoded = strings.ReplaceAll(decoded, "5d", "e")
		decoded = strings.ReplaceAll(decoded, "0d", "5")
		decoded = strings.ReplaceAll(decoded, "53", "k")
		decoded = strings.ReplaceAll(decoded, "1e", "&")
		decoded = strings.ReplaceAll(decoded, "5a", "b")
		decoded = strings.ReplaceAll(decoded, "59", "a")
		decoded = strings.ReplaceAll(decoded, "4a", "r")
		decoded = strings.ReplaceAll(decoded, "4c", "t")
		decoded = strings.ReplaceAll(decoded, "4e", "v")
		decoded = strings.ReplaceAll(decoded, "57", "o")
		decoded = strings.ReplaceAll(decoded, "51", "i")
		decoded = strings.ReplaceAll(decoded, "52", "n")
		decoded = strings.ReplaceAll(decoded, "55", "m")
		decoded = strings.ReplaceAll(decoded, "56", "g")
		decoded = strings.ReplaceAll(decoded, "58", "h")
		decoded = strings.ReplaceAll(decoded, "4d", "s")
		decoded = strings.ReplaceAll(decoded, "4b", "q")
		decoded = strings.ReplaceAll(decoded, "49", "y")
		decoded = strings.ReplaceAll(decoded, "47", "u")
		decoded = strings.ReplaceAll(decoded, "45", "x")
		decoded = strings.ReplaceAll(decoded, "43", "z")
		decoded = strings.ReplaceAll(decoded, "41", "j")
		
		// Handle clock.json URLs
		if strings.HasSuffix(decoded, "/clock.json") {
			// Make request to clock.json endpoint
			clockURL := fmt.Sprintf("https://blog.allanime.day/apivtwo/clock?%s", decoded[strings.Index(decoded, "?")+1:])
			
			clockReq, err := http.NewRequest("GET", clockURL, nil)
			if err != nil {
				return nil, fmt.Errorf("failed to create clock request: %v", err)
			}
			clockReq.Header.Set("User-Agent", UserAgent)
			clockReq.Header.Set("Referer", AllanimeRefr)

			clockResp, err := e.client.Do(clockReq)
			if err != nil {
				return nil, fmt.Errorf("failed to fetch clock.json: %v", err)
			}
			defer clockResp.Body.Close()

			clockBody, err := io.ReadAll(clockResp.Body)
			if err != nil {
				return nil, fmt.Errorf("failed to read clock.json response: %v", err)
			}

			// Parse clock.json response
			providers := []Provider{}
			if strings.Contains(string(clockBody), "repackager.wixmp.com") {
				providers = append(providers, e.extractWixmpLinks(clockBody, quality, priority)...) // Wixmp provider
			} else if strings.Contains(string(clockBody), "vipanicdn") || strings.Contains(string(clockBody), "anifastcdn") {
				providers = append(providers, e.extractVipanicLinks(clockBody, quality, priority)...) // Vipanic/Anifast provider
			}
			return providers, nil
		}
		
		// Otherwise return the decoded URL
		providers := []Provider{
			{
				Name:     "YouTube",
				Quality:  quality,
				URL:      decoded,
				Priority: int(priority), // Use the priority from the source
			},
		}
		return providers, nil
	}

	// Get the embed page content
	req, err := http.NewRequest("GET", embedURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}
	req.Header.Set("User-Agent", UserAgent)
	req.Header.Set("Referer", AllanimeRefr)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch embed page: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Extract links from embed page
	providers := []Provider{}
	
	// Wixmp provider
	if strings.Contains(string(body), "repackager.wixmp.com") {
		providers = append(providers, e.extractWixmpLinks(body, quality, priority)...) // Wixmp provider
	}
	
	// Vipanic/Anifast provider
	if strings.Contains(string(body), "vipanicdn") || strings.Contains(string(body), "anifastcdn") {
		providers = append(providers, e.extractVipanicLinks(body, quality, priority)...) // Vipanic/Anifast provider
	}
	
	// If no providers found, return the original URL
	if len(providers) == 0 {
		providers = append(providers, Provider{
			Name:     "Embed",
			Quality:  quality,
			URL:      embedURL,
			Priority: int(priority),
		})
	}
	
	return providers, nil
}

// extractWixmpLinks extracts links from Wixmp provider
func (e *Extractor) extractWixmpLinks(body []byte, quality string, priority float64) []Provider {
	providers := []Provider{}
	
	// Extract base URL
	baseURL := string(bytes.TrimPrefix(bytes.TrimSuffix(body, []byte(".urlset")), []byte("repackager.wixmp.com/")))
	
	// Extract quality and resolution
	qualityRegex := regexp.MustCompile(`.*hls.*"resolutionStr":"([^"]*)".*`)
	matches := qualityRegex.FindAllStringSubmatch(string(body), -1)
	
	for _, match := range matches {
		if len(match) > 1 {
			quality := match[1]
			providers = append(providers, Provider{
				Name:     "Wixmp",
				Quality:  quality,
				URL:      fmt.Sprintf("%s.%s", baseURL, quality),
				Priority: 1, // Wixmp is usually high priority
			})
		}
	}
	
	return providers
}

// extractVipanicLinks extracts links from Vipanic/Anifast provider
func (e *Extractor) extractVipanicLinks(body []byte, quality string, priority float64) []Provider {
	providers := []Provider{}
	
	// Check if it's m3u8
	if strings.Contains(string(body), "original.m3u") {
		providers = append(providers, Provider{
			Name:     "Vipanic",
			Quality:  quality,
			URL:      string(body),
			Priority: int(priority),
		})
	} else {
		// Extract base URL
		baseURL := string(bytes.SplitN(body, []byte("/mp4"), 2)[0])
		
		// Get m3u8 playlist
		req, err := http.NewRequest("GET", string(body), nil)
		if err != nil {
			return providers
		}
		req.Header.Set("User-Agent", UserAgent)
		req.Header.Set("Referer", AllanimeRefr)

		resp, err := e.client.Do(req)
		if err != nil {
			return providers
		}
		defer resp.Body.Close()

		playlist, err := io.ReadAll(resp.Body)
		if err != nil {
			return providers
		}

		// Parse m3u8 playlist
		lines := bytes.Split(playlist, []byte{10}) // \n
		for _, line := range lines {
			if bytes.HasPrefix(line, []byte{35}) || len(line) == 0 { // #
				continue
			}
			
			// Extract quality and URL
			qualityRegex := regexp.MustCompile(`#EXT-X-STREAM-INF:.*RESOLUTION=([^,]*),.*`)
			matches := qualityRegex.FindAllStringSubmatch(string(line), -1)
			if len(matches) > 0 && len(matches[0]) > 1 {
				quality := matches[0][1]
				url := fmt.Sprintf("%s/mp4/%s", baseURL, string(bytes.Trim(line, "\n")))
				providers = append(providers, Provider{
					Name:     "Vipanic",
					Quality:  quality,
					URL:      url,
					Priority: int(priority),
				})
			}
		}
	}
	return providers
}

// extractWixmpLink extracts a single Wixmp link
func (e *Extractor) extractWixmpLink(body []byte, quality string, priority float64) Provider {
	// Extract base URL
	baseURL := string(bytes.TrimPrefix(bytes.TrimSuffix(body, []byte(".urlset")), []byte("repackager.wixmp.com/")))
	
	// Extract quality and resolution
	qualityRegex := regexp.MustCompile(`.*hls.*"resolutionStr":"([^"]*)".*`)
	matches := qualityRegex.FindAllStringSubmatch(string(body), -1)
	
	if len(matches) > 0 && len(matches[0]) > 1 {
		quality := matches[0][1]
		return Provider{
			Name:     "Wixmp",
			Quality:  quality,
			URL:      fmt.Sprintf("%s.%s", baseURL, quality),
			Priority: 1, // Wixmp is usually high priority
		}
	}
	
	return Provider{}
}

// SelectQuality selects the best matching quality
func (e *Extractor) SelectQuality(providers []Provider, quality string) (string, error) {
	var selected Provider
	
	// Try exact match first
	for _, p := range providers {
		if p.Quality == quality {
			selected = p
			break
		}
	}
	
	// If no exact match, try best quality
	if selected.URL == "" {
		selected = providers[0]
	}
	
	return selected.URL, nil
}
