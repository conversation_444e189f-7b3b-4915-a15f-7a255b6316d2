The bash script performs the initial search via a specific sequence of steps involving user input, formatting, an HTTP request to an API, processing the API response, and presenting results for selection.

Here's a breakdown of the process as seen in the script and how you would achieve the equivalent in Go:

**1. Get User Input (Anime Title)**

*   **Bash Script:** The script first checks configuration variables like `use_external_menu`. If not using an external menu (like `rofi`), it displays a prompt "Search anime: " using `printf` and reads the user's input into the `query` variable using `read -r query`. If the query is empty, it exits.
*   **Go Equivalent:**
    *   Use Go's standard library for reading from standard input (`os.Stdin`).
    *   You'll need `bufio` to read a line of text.
    *   Prompt the user using `fmt.Print` or `fmt.Println`.
    *   Read the input line.
    *   Trim any leading/trailing whitespace, especially the newline character.
    *   Check if the input is empty; if so, you can handle this by exiting or returning an error.

```go
package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
)

func getUserQuery() (string, error) {
	fmt.Print("Search anime: ")
	reader := bufio.NewReader(os.Stdin)
	query, err := reader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("error reading input: %w", err)
	}
	query = strings.TrimSpace(query) // Remove leading/trailing whitespace including newline

	if query == "" {
		return "", fmt.Errorf("search query cannot be empty")
	}

	return query, nil
}
```

**2. Format the Query**

*   **Bash Script:** The script replaces spaces in the user's `query` with `+` characters using `sed "s| |+|g"`. This is part of preparing the query for the API request.
*   **Go Equivalent:**
    *   Use Go's string manipulation functions. `strings.ReplaceAll` is suitable for this.

```go
// Assuming 'query' is the string obtained from getUserQuery()
formattedQuery := strings.ReplaceAll(query, " ", "+")
```
*(Note: While `net/url.QueryEscape` is generally preferred for building URL parameters to handle special characters correctly, the script specifically uses space-to-plus replacement. To precisely match the script's *method*, we use `strings.ReplaceAll`. However, for robustness, `QueryEscape` is usually better outside of direct script replication.)*

**3. Perform the API Search Request**

*   **Bash Script:** The `search_anime` function is called with the formatted query. This function uses `curl` to make an HTTP GET request to `https://api.allanime.day/api`. The request includes:
    *   `Referer` header: Set to `https://allmanga.to` using `-e "$allanime_refr"`.
    *   `User-Agent` header: Set using `-A "$agent"` (the `agent` variable is defined in).
    *   Query parameters: The GraphQL `query` and `variables` are URL-encoded using `--data-urlencode` and sent as GET parameters. The `variables` include the formatted user `query`, `limit: 40`, `page: 1`, `translationType: "$mode"` (defaulting to `sub` from), etc.
    *   The GraphQL query structure is defined within the script.
*   **Go Equivalent:**
    *   Use the `net/http` package to perform the GET request.
    *   Use the `net/url` package to construct the request URL and properly encode the query parameters (`query` and `variables`).
    *   Define Go structs that mirror the *expected JSON structure* of the API response based on the fields requested in the GraphQL query (`_id`, `name`, `availableEpisodes` with `sub`/`dub`).

```go
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	// ... other imports
)

// Define structs to match the expected JSON response structure
type AvailableEpisodes struct {
	Sub int `json:"sub"`
	Dub int `json:"dub"`
}

type Anime struct {
	ID                string            `json:"_id"`
	Name              string            `json:"name"`
	AvailableEpisodes AvailableEpisodes `json:"availableEpisodes"`
	Typename          string            `json:"__typename"` // Added from query
}

type Edge struct {
	Node Anime `json:"node"` // Structure based on GraphQL 'edges { node { ... } }'
}

type ShowsData struct {
	Edges []Edge `json:"edges"`
}

type SearchResponse struct {
	Data struct {
		Shows ShowsData `json:"shows"`
	} `json:"data"`
}

// Constants based on script variables
const (
	allanimeAPI    = "https://api.allanime.day/api"
	allanimeReferer = "https://allmanga.to"
	userAgent      = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0" // Example user agent from script
	defaultMode    = "sub" // Default mode from script
)

// This GraphQL query is taken directly from the search_anime function
const searchGraphQLQuery = `query( $search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType ) { shows( search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin ) { edges { _id name availableEpisodes __typename } }}`

func searchAnime(query string, mode string) (*SearchResponse, error) {
	// Prepare GraphQL variables
	variables := map[string]interface{}{
		"search": map[string]interface{}{
			"allowAdult":    false,
			"allowUnknown":  false,
			"query":         query, // Use the formatted query
		},
		"limit":           40,
		"page":            1,
		"translationType": mode,
		"countryOrigin":   "ALL",
	}

	variablesJSON, err := json.Marshal(variables)
	if err != nil {
		return nil, fmt.Errorf("error marshalling variables: %w", err)
	}

	// Construct the URL with query parameters
	// The script uses GET with data-urlencode, so we build query parameters
	u, err := url.Parse(allanimeAPI)
	if err != nil {
		return nil, fmt.Errorf("error parsing API URL: %w", err)
	}

	q := u.Query()
	q.Set("query", searchGraphQLQuery)
	q.Set("variables", string(variablesJSON)) // Pass variables as a JSON string
	u.RawQuery = q.Encode() // Encode parameters into the query string

	// Create HTTP client and request
	client := &http.Client{}
	req, err := http.NewRequest("GET", u.String(), nil) // Use GET method
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Set headers as the script does
	req.Header.Set("Referer", allanimeReferer)
	req.Header.Set("User-Agent", userAgent)
	// Add other headers if necessary (though script only shows these basic ones for search)

	// Perform the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error performing request: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Read and parse the response body (JSON)
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	var searchResp SearchResponse
	if err := json.Unmarshal(bodyBytes, &searchResp); err != nil {
		// The script uses sed for parsing, which is error-prone.
		// JSON unmarshalling is the correct Go way, but if it fails,
		// the script's output parsing might still have worked in some edge cases.
		// We'll report a JSON parsing error here.
		return nil, fmt.Errorf("error parsing JSON response: %w", err)
	}

	return &searchResp, nil
}

// Helper function to get episode count based on mode
func getEpisodeCount(ae AvailableEpisodes, mode string) int {
	if mode == "dub" {
		return ae.Dub
	}
	return ae.Sub // Default to sub or if mode is "sub"
}
```

**4. Process and Format Results**

*   **Bash Script:** The script pipes the `curl` output through several `sed` commands. These commands parse the JSON-like string (after some initial `tr` and `sed` for basic structuring), specifically using a regex to extract the `_id`, `name`, and the episode count for the selected `mode`. The output is formatted into lines like `_id name (episode_count episodes)`, stored in `anime_list`. If `anime_list` is empty, it calls `die "No results found!"`.
*   **Go Equivalent:**
    *   Iterate through the parsed `SearchResponse` Go struct (`searchResp.Data.Shows.Edges`).
    *   For each `Edge`, access the `Node` (which is an `Anime` struct).
    *   Extract the `ID`, `Name`, and the relevant episode count using the `getEpisodeCount` helper based on the current `mode`.
    *   Format each result into a string similar to the script's output.
    *   Collect these formatted strings into a slice.
    *   If the slice is empty, print "No results found!".

```go
// Assuming 'searchResp' is the result from searchAnime() and 'mode' is the selected mode
func formatSearchResults(searchResp *SearchResponse, mode string) []string {
	var results []string
	for _, edge := range searchResp.Data.Shows.Edges {
		anime := edge.Node
		episodeCount := getEpisodeCount(anime.AvailableEpisodes, mode)
		// Format the string similar to the script's sed output
		// Note: The script includes 'Show' line breaks, but the final sed produces
		// lines starting with ID, then name, then episode count.
		// We'll output lines ready for selection.
		formatted := fmt.Sprintf("%s %s (%d episodes)", anime.ID, anime.Name, episodeCount)
		results = append(results, formatted)
	}
	return results
}
```

**5. Display Results and Handle Selection (using fzf or go-fzf)**

*   **Bash Script:** The script prepends line numbers to the `anime_list` and pipes the result to the `nth` function. The `nth` function uses `launcher`, which defaults to using the `fzf` command-line tool unless `use_external_menu` is set (then uses `rofi`). `fzf` (or `rofi`) presents an interactive list with the prompt "Select anime: " and allows the user to select one entry. The selected line is captured.
*   **Go Equivalent:**
    *   Take the slice of formatted result strings.
    *   **Check for `fzf` executable:** Use `exec.LookPath("fzf")`.
    *   **If `fzf` is found:**
        *   Use `os/exec` to run the `fzf` command.
        *   Pipe the formatted results (joined by newlines) to `fzf`'s standard input.
        *   Pass the `--prompt "Select anime: "` flag to `fzf`.
        *   Capture `fzf`'s standard output, which will be the selected line.
        *   Handle potential errors or cancellation (e.g., user pressing Ctrl+C or Esc in fzf usually exits with a non-zero status).
    *   **If `fzf` is NOT found:**
        *   Use the `github.com/koki-develop/go-fzf` library.
        *   Call the library's function (e.g., `fzf.Run`) with the slice of results and the prompt option.
        *   Capture the selected result string and its index. Handle errors or cancellation.
    *   Return the selected result string.

```go
package main

import (
	"bytes"
	"fmt"
	"os/exec"
	"strings"

	// Note: This is an external dependency, not part of the standard library.
	// You'll need to add this dependency (go get github.com/koki-develop/go-fzf)
	"github.com/koki-develop/go-fzf"
	// ... other imports
)

func selectAnime(results []string) (string, error) {
	if len(results) == 0 {
		return "", fmt.Errorf("no results to select from")
	}

	// Add line numbers before passing to fzf/go-fzf, similar to the script's `nl`
	// fzf/go-fzf can handle this internally with options, but formatting it ourselves
	// matches the script's data preparation step.
	var numberedResults []string
	for i, result := range results {
		// Format line number with padding, similar to `nl -w 2 | sed 's/^[[:space:]]//'`
		numberedResults = append(numberedResults, fmt.Sprintf("%2d %s", i+1, result))
	}

	// Check if fzf executable is available
	fzfPath, err := exec.LookPath("fzf")
	if err == nil {
		// fzf executable found, use it via os/exec
		// The script pipes the list to fzf's stdin
		input := strings.Join(numberedResults, "\n")
		cmd := exec.Command(fzfPath, "--prompt", "Select anime: ") // Set the prompt
		cmd.Stdin = bytes.NewReader([]byte(input))
		// Capture stdout to get the selected line
		output, err := cmd.Output()
		if err != nil {
			// fzf often exits with 130 on cancellation (Ctrl+C/Esc)
			// You might want more sophisticated error checking here
			return "", fmt.Errorf("fzf selection failed or cancelled: %w", err)
		}
		// Trim newline from fzf output
		selectedLine := strings.TrimSpace(string(output))
		if selectedLine == "" {
			return "", fmt.Errorf("no anime selected")
		}
		return selectedLine, nil

	} else {
		// fzf executable not found, use the go-fzf library
		fmt.Println("fzf executable not found, using built-in go-fzf library.")

		// go-fzf library usage
		// The library handles the interactive menu in the terminal
		f, err := fzf.New(fzf.WithPrompt("Select anime: ")) // Set the prompt
		if err != nil {
			return "", fmt.Errorf("error initializing go-fzf: %w", err)
		}

		// Run fzf with the list of strings
		// The library returns the selected item(s) or error/cancellation
		indices, err := f.Run(numberedResults)
		if err != nil {
			if err == fzf.ErrAbort {
				return "", fmt.Errorf("go-fzf selection cancelled")
			}
			return "", fmt.Errorf("go-fzf selection failed: %w", err)
		}

		if len(indices) == 0 {
			return "", fmt.Errorf("no anime selected")
		}
		// go-fzf can return multiple indices if multi-select is enabled,
		// but for initial search, we expect one selection based on script flow
		selectedIndex := indices
		selectedLine := numberedResults[selectedIndex]

		return selectedLine, nil
	}
}
```

**6. Extract Selected Anime Information**

*   **Bash Script:** The script takes the selected line from `fzf`/`rofi` (stored in `result`) and uses `cut -f1` and `cut -f2` (after piping through `nl` earlier) to extract the `_id` and `title` (name).
*   **Go Equivalent:**
    *   Take the selected result string.
    *   The formatted string is like `" ID Name (Episode Count)"`.
    *   Split the string by space. The first part should be the line number, the second the ID.
    *   Extract the second field (the ID).

```go
// Assuming 'selectedResult' is the string returned from selectAnime()
func extractAnimeID(selectedResult string) (string, error) {
	// The format is " NN ID Name (Episode Count)" where NN is the line number
	// We need to skip the line number (NN) and get the ID.
	// Splitting by space and taking the second field seems reliable based on the formatting.
	parts := strings.Fields(selectedResult) // Splits by one or more whitespace characters
	if len(parts) < 2 { // Expecting at least line number, ID, and name
		return "", fmt.Errorf("unexpected format for selected result: %s", selectedResult)
	}
	// parts is the line number (e.g., " 1" or "10")
	// parts is the ID
	// parts[2:] is the name and episode count
	animeID := parts
	return animeID, nil
}

func main() {
	// 1. Get User Input
	query, err := getUserQuery()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}

	// 2. Format the Query
	formattedQuery := strings.ReplaceAll(query, " ", "+") // Matching script's sed

	// 3. Perform the API Search
	// Use default mode 'sub' unless overridden, similar to the script's logic
	searchResp, err := searchAnime(formattedQuery, defaultMode)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Search failed: %v\n", err)
		os.Exit(1)
	}

	// Check if any results were returned
	if searchResp == nil || len(searchResp.Data.Shows.Edges) == 0 {
		fmt.Println("No results found!") // Matching script's output
		os.Exit(0) // Exit gracefully if no results
	}

	// 4. Format Results for Display/Selection
	formattedResults := formatSearchResults(searchResp, defaultMode)

	// 5. Display Results and Handle Selection (using fzf or go-fzf)
	selectedResult, err := selectAnime(formattedResults)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Selection cancelled or failed: %v\n", err)
		os.Exit(1)
	}

	// 6. Extract Selected Anime ID
	selectedID, err := extractAnimeID(selectedResult)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error processing selection: %v\n", err)
		os.Exit(1)
	}

	// --- End of Initial Search and Selection Phase ---
	// At this point, you have the 'selectedID' and could proceed to
	// get the episode list and play/download, but we stop here as requested.

	fmt.Printf("Selected Anime ID: %s\n", selectedID)
	fmt.Printf("Selected Anime Details Line: %s\n", selectedResult)

	// The rest of the script (getting episode list, selecting episode,
	// fetching episode links, playing, history, etc.) would follow here.
}
```

To run this Go code, you would save it as a `.go` file (e.g., `anigo_search.go`).

1.  Ensure you have Go installed.
2.  Add the external `go-fzf` dependency: `go get github.com/koki-develop/go-fzf`.
3.  Run the code: `go run anigo_search.go`.

This Go implementation replicates the specific steps taken by the bash script for the initial anime search: prompting, formatting, making the exact HTTP request to the API with the specified headers and parameters, processing the structured JSON response (instead of fragile `sed` parsing), and using either the external `fzf` command or the `go-fzf` library for interactive selection, mirroring the script's reliance on these tools.

Remember that this implementation *only* covers the initial search and selection of an anime title, stopping once the anime ID is identified, as requested. The bash script proceeds further to get episode lists, select episodes, fetch video links, and play/download.

-----------------------------

Okay, let's break down the next steps in the `ani-cli` script after an anime title has been selected, focusing on how it finds and presents the episode list, and then how this could be translated into Go.

Based on the sources, once an anime is selected (either by searching or from history), its `_id` and `name` (title) are stored. The script then proceeds to get the list of available episodes for that selected anime using the `episodes_list` function.

Here's how the script handles getting and presenting the episode list:

1.  **Fetching the Episode List:**
    *   The `episodes_list` function is called with the selected anime's `_id`.
    *   This function uses a specific GraphQL query defined as `episodes_list_gql`. This query takes the `showId` as a variable.
    *   The script then makes a `curl` request to the `${allanime_api}/api` endpoint. This request includes the `showId` in the variables and the `episodes_list_gql` query. It also uses the `$agent` and `$allanime_refr` for the request.
    *   The response from the API is processed using a series of `sed` commands. These commands are designed to extract the episode list specific to the current `mode` (either `sub` or `dub`) from the JSON response. The extracted list is initially a comma-separated string of episode numbers (like `[0,1,1.5,"2","3"]`).
    *   The `sed` commands further process this string to convert the commas into newlines (`sed 's|,|\<br>|g'`), remove quotes (`s|"||g'`), and finally sort the episode numbers numerically (`sort -n -k 1`).
    *   The resulting newline-separated list of episode numbers is stored in the `ep_list` variable.

2.  **Presenting and Selecting the Episode:**
    *   After getting the `ep_list`, the script checks if an episode number (`$ep_no`) was already provided as a command-line argument (using `-e` or `--episode`).
    *   If `$ep_no` is empty (meaning no specific episode was requested via options), the script uses the `nth` function to present the `ep_list` to the user for interactive selection.
    *   The `nth` function pipes the `ep_list` (received via stdin from the previous command) into either `fzf` or `rofi`. The choice depends on the `$use_external_menu` variable.
    *   `fzf` or `rofi` displays the list of episodes with a prompt like "Select episode: ".
    *   The user interacts with the menu to select one or more episodes. The `--multi-select` flag can be used with `rofi` or the `-m` flag with `fzf` depending on `$use_external_menu`. The script uses the `$multi_selection_flag` variable derived from these settings.
    *   The selected episode number(s) are returned by `fzf`/`rofi` and captured by the `launcher` function, then processed by `cut` and stored in the `$line` variable within `nth`.
    *   The `nth` function processes the `$line` variable to determine if a single episode or a range/multiple episodes were selected. It then filters the original `ep_list` to return the chosen episode(s).
    *   This result is stored in the `$ep_no` variable.
    *   If `$ep_no` is still empty after the selection (e.g., the user canceled), the script exits.

**In summary, the script uses `curl` and `sed` to query an API for the episode list and then uses `fzf` or `rofi` via the `nth` function to create an interactive menu for the user to select episodes.**

---

Now, let's consider how you could replicate this process in Go:

1.  **Fetching the Episode List in Go:**
    *   You would need to use Go's standard library `net/http` package to make the HTTP request to the API endpoint (`https://api.allanime.day/api`).
    *   You would construct the request with the necessary headers (`User-Agent`, `Referer`) mirroring the script's `$agent` and `$allanime_refr`.
    *   The GraphQL query and variables (`showId`, `translationType`) would be included in the request body, likely as a JSON payload in a POST request, although the script uses GET with data-urlencode. You'd need to determine the exact API requirements, but typically GraphQL queries are sent as POST requests with a JSON body containing `query` and `variables` fields.
    *   After making the request, you would read the response body.
    *   Instead of `sed`, you would use Go's `encoding/json` package to parse the JSON response into a Go struct. You would need to define structs that match the structure of the API's response, particularly the part containing the `availableEpisodesDetail` which holds the episode numbers for `sub` and `dub` modes.
    *   Once parsed, you can access the slice (or map, depending on the JSON structure) containing the episode numbers for the selected `mode`.
    *   You would then process this data to get a clean list of episode numbers (e.g., convert numbers/strings to a consistent format) and sort them numerically, potentially storing them in a `[]string` or `[]float64`.

    Here's a conceptual Go code structure for fetching:

    ```go
    package main

    import (
    	"bytes"
    	"encoding/json"
    	"fmt"
    	"io"
    	"net/http"
    	"sort"
    	"strings"
    )

    const (
    	allanimeAPI = "https://api.allanime.day/api"
    	userAgent   = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
    	referer     = "https://allmanga.to"
    )

    // Define structs to match the relevant parts of the API response
    // This is an educated guess based on the sed parsing in the script,
    // you'd need the exact API response structure for correct parsing.
    type EpisodeDetails struct {
        Sub []string `json:"sub"` // or []float64 or mixed interface{}
        Dub []string `json:"dub"` // or []float64 or mixed interface{}
        // Add other modes if necessary
    }

    type Show struct {
        AvailableEpisodesDetail EpisodeDetails `json:"availableEpisodesDetail"`
        // Add other fields like _id, name etc. if needed
    }

    type EpisodeResponseData struct {
        Show Show `json:"show"`
    }

    type EpisodeGraphQLResponse struct {
        Data EpisodeResponseData `json:"data"`
    }

    func getEpisodeList(showID, mode string) ([]string, error) {
    	// The GraphQL query from the script
    	query := `query ($showId: String!) { show( _id: $showId ) { _id availableEpisodesDetail }}`

    	// Variables for the query
    	variables := map[string]string{
    		"showId": showID,
    	}

    	// Construct the request body (simulating the script's GET with data-urlencode,
        // but typically GraphQL is POST with JSON body. Let's simulate script's method for now)
        // For a real app, consider using a proper GraphQL client or a POST request with JSON body.
        reqBody := map[string]interface{}{
            "query": query,
            "variables": variables,
        }

        jsonBody, err := json.Marshal(reqBody)
        if err != nil {
            return nil, fmt.Errorf("failed to marshal request body: %w", err)
        }

        // The script uses GET with --data-urlencode, which is unusual for GraphQL.
        // Let's simulate the GET request URL construction as seen in the script,
        // though a standard POST JSON approach is more typical for GraphQL.
        // For clarity, let's use the script's GET method here.
        // We need to URL encode the variables and query.
        // In Go, net/url is used for this.
        // The script's curl command is `curl -G "${allanime_api}/api" --data-urlencode "variables=..." --data-urlencode "query=..."`
        // This translates to a GET request where data is sent as query parameters.

        variablesEncoded := url.QueryEscape(string(jsonBody)) // This might not be how allanime expects it if variables is a map
        queryEncoded := url.QueryEscape(query)

        requestURL := fmt.Sprintf("%s?variables=%s&query=%s", allanimeAPI, variablesEncoded, queryEncoded)


    	req, err := http.NewRequest("GET", requestURL, nil) // Script uses GET
    	if err != nil {
    		return nil, fmt.Errorf("failed to create request: %w", err)
    	}

    	req.Header.Set("User-Agent", userAgent)
    	req.Header.Set("Referer", referer)
        // Note: Content-Type is not needed for GET request body

    	client := &http.Client{}
    	resp, err := client.Do(req)
    	if err != nil {
    		return nil, fmt.Errorf("failed to send request: %w", err)
    	}
    	defer resp.Body.Close()

    	if resp.StatusCode != http.StatusOK {
    		bodyBytes, _ := io.ReadAll(resp.Body)
    		return nil, fmt.Errorf("API request failed with status code %d: %s", resp.StatusCode, string(bodyBytes))
    	}

    	// Read the response body
    	body, err := io.ReadAll(resp.Body)
    	if err != nil {
    		return nil, fmt.Errorf("failed to read response body: %w", err)
    	}

        // The script uses heavy sed parsing. In Go, we'd unmarshal JSON.
        // However, the sed regex `.*$mode\":\[([0-9.\",]*)\].*` is brittle and might
        // not parse correctly into the simple struct above if the JSON structure
        // is more complex or inconsistent (e.g., episode numbers as strings or numbers).
        // A more robust approach might involve parsing the raw JSON manually
        // or using a more flexible JSON library if the structure is highly dynamic,
        // but let's try unmarshalling into the struct based on the regex hint.

        var graphqlResp EpisodeGraphQLResponse
        err = json.Unmarshal(body, &graphqlResp)
        if err != nil {
            // If unmarshalling fails, maybe try to replicate the script's regex approach
            // using Go's regex package, but this is generally less reliable than JSON unmarshalling.
            // Let's assume standard JSON unmarshalling works for now.
            return nil, fmt.Errorf("failed to unmarshal JSON response: %w", err)
        }

        var episodes []string
        // Access the episode list based on the specified mode
        switch mode {
        case "sub":
            // Need to convert []interface{} or []float64 to []string if that's how it parses
            // Assuming it parses directly into []string based on the regex `([0-9.\",]*)` capture
            episodes = graphqlResp.Data.Show.AvailableEpisodesDetail.Sub
        case "dub":
            episodes = graphqlResp.Data.Show.AvailableEpisodesDetail.Dub
        default:
             return nil, fmt.Errorf("unsupported mode: %s", mode)
        }


        // Clean up and sort the episode list as the script does
        // The script sorts numerically (`sort -n -k 1`).
        // If episodes are strings like "1", "1.5", "10", we need a custom sort.
        // If they are numbers, sort is easier. Assuming strings like "1", "2", "10".
        // Let's sort numerically.
        sort.SliceStable(episodes, func(i, j int) bool {
            // Attempt to parse as floats for numerical sorting
            epi, err1 := strconv.ParseFloat(episodes[i], 64)
            epj, err2 := strconv.ParseFloat(episodes[j], 64)

            if err1 == nil && err2 == nil {
                return epi < epj
            }
            // If parsing fails for some, fall back to string comparison or handle error
            // For simplicity, if parsing fails, let's put the non-parsable ones at the end or sort as strings
            if err1 != nil && err2 != nil {
                 return episodes[i] < episodes[j] // Both fail, string sort
            }
            if err1 != nil {
                return false // i fails, put it after j
            }
            if err2 != nil {
                return true // j fails, put it after i
            }
             return episodes[i] < episodes[j] // Fallback string sort
        })


    	return episodes, nil
    }
    ```
    *Self-correction:* The script's `sed` regex `s|.*$mode\":\[([0-9.\",]*)\].*|\1|p` captures the content *inside* the `[` and `]`. The characters inside `([0-9.\",]*)` are digits, periods, quotes, and commas. This suggests the content is likely a comma-separated string of numbers or quoted strings. My initial struct `[]string` might be correct, but sorting needs careful handling of potential strings vs. numbers and decimal episodes like "1.5". I'll adjust the sorting logic concept to handle potential floats. Also, the script uses `GET` with `--data-urlencode` which is unusual for GraphQL but seems to be the API's expected method, so I'll reflect that in the Go translation snippet.

2.  **Presenting and Selecting in Go:**
    *   Replicating the `fzf`/`rofi` interactive menu directly in Go is not a standard library feature. You have a few options:
        *   **Execute External Command:** The most direct translation is to shell out to `fzf` or `rofi` using Go's `os/exec` package, piping the episode list to its stdin and capturing its stdout (the user's selection). This keeps the dependency on external tools, just like the script.
        *   **Use a TUI Library:** You could use a Go library for building Terminal User Interfaces (TUI) to create a custom interactive selection list within your Go application. This avoids the external tool dependency but requires learning the library.
        *   **Simple Text Input:** A simpler, less interactive method would be to print a numbered list of episodes to the console and ask the user to enter the number corresponding to their choice using `fmt.Scanln`.

    Given the script's strong reliance on `fzf`/`rofi`, replicating the **External Command** approach with `fzf` would be the most faithful translation of this part of the logic.

    Conceptual Go code structure for selection using `fzf`:

    ```go
    package main

    import (
    	"bytes"
    	"fmt"
    	"os"
    	"os/exec"
        "strings"
    )

    func selectEpisode(episodeList []string, useExternalMenu bool, multiSelect bool) ([]string, error) {
        var selectedEpisodes []string
        var cmd *exec.Cmd
        var prompt string

        // Decide which tool to use based on settings (simulating script's logic)
        if useExternalMenu {
            // Use rofi or other external menu tool
            // Assuming rofi, check script for exact args
            menuTool := "rofi" // or whatever external menu tool is configured
             // The script uses external_menu "$1" "$2" "$external_menu_args" where $1 is rofi args, $2 is prompt, $3 is extra args
             // And launcher calls external_menu "$1" "$2" "$external_menu_args" where $1 is multi_flag, $2 is prompt
            rofiArgs := []string{"-dmenu", "-i", "-width", "1500"} // Example args from script
            if multiSelect {
                 rofiArgs = append(rofiArgs, "-multi-select") // Example multi-select flag
            }
            // The prompt is passed as the second argument to external_menu
            prompt = "Select episode: " // From script
            rofiArgs = append(rofiArgs, "-p", prompt)

            // Need to handle potential external_menu_args from script
            // For simplicity, just using basic args for now
            cmd = exec.Command(menuTool, rofiArgs...)

        } else {
             // Use fzf (default in script if no external menu)
             // The script uses fzf "$1" --reverse --cycle --prompt "$2" where $1 is multi_flag, $2 is prompt
            fzfArgs := []string{"--reverse", "--cycle"} // Example args from script
             // The script uses multi_selection_flag which is "-m" for fzf
             if multiSelect {
                 fzfArgs = append(fzfArgs, "-m") // Example multi-select flag
             }
             // The prompt is passed as the second argument
             prompt = "Select episode: " // From script
            fzfArgs = append(fzfArgs, "--prompt", prompt)

            cmd = exec.Command("fzf", fzfArgs...)
        }


        // Provide the episode list to the external command's stdin
        input := strings.Join(episodeList, "\n")
        cmd.Stdin = strings.NewReader(input)

        // Capture the output (user's selection)
        var stdout, stderr bytes.Buffer
        cmd.Stdout = &stdout
        cmd.Stderr = &stderr // Capture stderr for debugging

        // Run the command
        err := cmd.Run()
        if err != nil {
             // fzf/rofi often exit with status 1 if cancelled.
             // Need to check stderr or command exit code for specific error cases vs cancellation.
             // The script checks for empty output and exits. Let's do that.
        }

        // Get the selected line(s) from stdout
        selected := strings.TrimSpace(stdout.String())

        if selected == "" {
            // User cancelled or no selection
            return nil, fmt.Errorf("no episode selected") // Or return nil, nil and handle as cancel
        }

        // Split the selected output into individual episode numbers
        // fzf/rofi with multi-select return selections separated by newlines by default
        selectedEpisodes = strings.Split(selected, "\n")

        // The script's nth function does more complex processing to handle ranges
        // and single selections after getting the line(s) from launcher.
        // This part would need to be replicated if ranges/multi-selection are supported.
        // For simplicity here, assuming the external tool returns the final episode numbers.
        // If range selection (like 5-6) is done in the external tool and returned as "5-6",
        // you'd need logic here to expand that range into individual episode numbers.
        // If the tool returns individual numbers on separate lines, Split("\n") is enough.
        // Based on the script's nth processing `grep -E '^'"${line}"'($|[[:space:]])'` or `sed -n '/^'"${line_start}"'$/,/^'"${line_end}$"'/p'`, it seems
        // the tool returns either a single episode number or the start and end of a range on separate lines.
        // Replicating that exact logic in Go based on the split output would be necessary for full fidelity.
        // For now, let's assume Split("\n") gives the list of selected individual episodes.

        return selectedEpisodes, nil
    }
    ```
    *Self-correction:* The script's `nth` function processes the *raw* output from `launcher` (`fzf` or `rofi`) which might include the numeric prefix added by `nl -w 2` or just the episode number. It then uses `cut` and `grep`/`sed` to get the final episode numbers based on the selection. The Go `selectEpisode` function above assumes `fzf`/`rofi` *only* output the episode number(s). A more accurate translation would involve piping the `nl -w 2` output *with* the original data structure (like the script does `printf "%s" "$anime_list" | nl -w 2 | sed 's/^[[:space:]]//' | nth ...`), getting the selected line(s) that include the index and episode info, and then parsing that selected line(s) to extract just the episode number, similar to the `cut -f1` and `grep "$id" | cut -f2 | sed ...` logic in the script. The Go version would likely need to pass a structured list (e.g., index + episode number) to `fzf` and then parse the selected line(s) to get the episode. I'll refine the Go conceptual code to show this:

    Refined Conceptual Go code structure for selection using `fzf` (more faithful to script parsing):

    ```go
    package main

    import (
    	"bytes"
    	"fmt"
    	"os/exec"
    	"strconv"
    	"strings"
    )

    // Assuming we have a list of episodes obtained from getEpisodeList
    // epList := []string{"1", "1.5", "2", "3", "10"}

    func selectEpisodeFromList(epList []string, useExternalMenu bool, multiSelect bool) ([]string, error) {
    	// Prepare the list for the selector tool, adding a numeric index like `nl`
    	var inputLines []string
    	for i, ep := range epList {
    		// Format like script's nl output: " 1  1.5" or "10  10"
            // The script uses `nl -w 2 | sed 's/^[[:space:]]//'` which removes leading spaces if width is 2
            // Let's format with fixed width for alignment in the selector tool
            inputLines = append(inputLines, fmt.Sprintf("%2d\t%s", i+1, ep))
    	}
        inputString := strings.Join(inputLines, "\n")


    	var cmd *exec.Cmd
    	prompt := "Select episode: " // From script

    	if useExternalMenu {
    		// Use rofi or other external menu tool
    		menuTool := "rofi"
    		rofiArgs := []string{"-dmenu", "-i", "-width", "1500", "-p", prompt}
    		if multiSelect {
    			rofiArgs = append(rofiArgs, "-multi-select") // Example flag
    		}
    		cmd = exec.Command(menuTool, rofiArgs...)
    	} else {
    		// Use fzf
    		fzfArgs := []string{"--reverse", "--cycle", "--prompt", prompt}
    		if multiSelect {
    			fzfArgs = append(fzfArgs, "-m") // Example flag
    		}
    		cmd = exec.Command("fzf", fzfArgs...)
    	}

    	// Provide the prepared list to the external command's stdin
    	cmd.Stdin = strings.NewReader(inputString)

    	// Capture the output (user's selected line(s))
    	var stdout, stderr bytes.Buffer
    	cmd.Stdout = &stdout
    	cmd.Stderr = &stderr // Capture stderr for debugging

    	err := cmd.Run()
    	if err != nil {
    		// Handle potential errors, e.g., command not found, or fzf/rofi exit code on cancel
            // A common pattern is to check the exit code if the command ran but returned non-zero
            if exitError, ok := err.(*exec.ExitError); ok {
                // fzf/rofi typically exit with 1 on cancel
                if exitError.ExitCode() == 1 {
                     return nil, fmt.Errorf("selection cancelled") // Or nil, nil for cancel
                }
                 // Other exit codes might indicate errors
                 return nil, fmt.Errorf("selector tool exited with status %d: %s", exitError.ExitCode(), stderr.String())
            }
    		return nil, fmt.Errorf("failed to run selector tool: %w", err)
    	}

    	selectedOutput := strings.TrimSpace(stdout.String())
    	if selectedOutput == "" {
             return nil, fmt.Errorf("no episode selected") // Should be caught by exit code 1, but good fallback
    	}

    	// Parse the selected line(s) to get the episode numbers
    	// The script uses `cut -f1` on the `nl` output to get the selected index,
    	// then uses that index to look up the original data.
    	// Or, if not using index selection mode, it parses the selected line(s) directly.
    	// Replicating script's `nth` logic:
    	// It takes the launcher output ($line), which is the selected row(s).
	    // If $line is multiline, it takes first and last lines as start/end of range.
	    // If $line is single line, it matches that line in the original stdin and cuts fields 2 and 3.
        // If multiline, it uses sed to get lines from start to end and cuts fields 2 and 3.
        // Let's assume for simplicity the fzf/rofi output is just the selected item string(s),
        // which, given the input format "%2d\t%s", would be something like " 5	2.5".
        // We need to parse this output string(s) to extract the episode number part.

        // If multiSelect is true, output can be multiple lines.
        selectedLines := strings.Split(selectedOutput, "\n")
        var finalSelectedEpisodes []string

        // Parse each selected line
        for _, line := range selectedLines {
             // The script's nth function seems to cut fields 2 and 3 from the original input
             // after finding the selected line(s). Let's replicate that.
             // Our input format is "%2d\t%s", so episode number is field 2 (after the tab).
             parts := strings.Split(line, "\t")
             if len(parts) > 1 {
                episodeNum := parts // Episode number is the second part
                finalSelectedEpisodes = append(finalSelectedEpisodes, episodeNum)
             } else {
                 // Handle unexpected output format
                 fmt.Fprintf(os.Stderr, "Warning: unexpected selector output format for line '%s'\n", line)
             }
        }


        // The script also handles selecting ranges (e.g., 5-6) which involves
        // looking up the episode numbers corresponding to the start and end of the range
        // in the original ep_list and then iterating through that range.
        // If your selector tool supports range input (like "5-6" input selecting episodes 5 through 6),
        // you would need logic here to parse that range string and find the corresponding episodes in epList.
        // If the tool only returns individual selections (even for ranges if it expands them),
        // the above parsing of selected lines is sufficient.
        // Based on the script using `sed -n '/^'"${line_start}"'$/,/^'"${line_end}$"'/p'` on the *original* stdin,
        // it seems the tool *might* return just the start and end lines of a selected range.
        // Replicating the script's full nth logic for range selection would add complexity.
        // Let's stick to parsing individual selected lines for now, as a common multi-select behavior.

    	return finalSelectedEpisodes, nil
    }

    // Need to add imports for fmt, os/exec, bytes, strings, strconv, sort, net/url, io
    // Also need url.QueryEscape and strconv.ParseFloat
    ```

This detailed breakdown covers how the script fetches the episode list using API calls and `sed` parsing, and how it presents and handles user selection using `fzf` or `rofi` via the `nth` function. The Go conceptual translation shows how to approach the API call using `net/http` and JSON parsing, and how to replicate the interactive selection by executing an external command like `fzf` and parsing its output, mirroring the script's dependency and UI approach.

------------------

Following our previous discussion about the structure and initial steps of the `ani-cli` bash script, let's delve into how the script handles user episode selection, searches for sources, extracts links, selects quality, and finally initiates playback using `mpv`.

Here is a breakdown of the process as described in the sources:

1.  **Episode Selection**
    *   After searching for an anime using `search_anime` (or selecting from history using `process_hist_entry`), the script obtains a list of available episodes for the selected anime by calling the `episodes_list` function.
    *   `episodes_list` makes a GraphQL query (`episodes_list_gql`) to the `allanime_api` using the anime `showId`.
    *   The response is processed using `sed`, `grep`, `sort`, and `tr` to extract the episode numbers available for the selected `mode` (sub or dub), typically resulting in a sorted list of episode numbers.
    *   The user is then presented with this list of episode numbers to select from. This selection is handled by the `nth` function, which internally uses either `fzf` (default) or `rofi` (if `--rofi` or `use_external_menu` is enabled) via the `launcher` function. The prompt shown to the user is "Select episode: ".
    *   The selected episode number (or a range of numbers) is stored in the `ep_no` variable.

2.  **Source Searching (Getting Embed URLs)**
    *   Once the episode number (`$ep_no`) is determined, the script calls the `play` function, which in turn calls `play_episode`.
    *   If the `$episode` variable is empty (meaning a source URL hasn't been found yet for this playback attempt), the `play_episode` function calls `get_episode_url`.
    *   The `get_episode_url` function is responsible for finding the embed URLs for the specific episode.
    *   It constructs a GraphQL query (`episode_embed_gql`) that takes the `showId`, `translationType` (`$mode`), and `episodeString` (`$ep_no`) as variables.
    *   This query is sent to the `allanime_api` using `curl`, along with the `$agent` user agent and `$allanime_refr` referer.
    *   The JSON response from the API is then processed using `tr` and `sed` to extract lines containing `sourceUrl` and `sourceName` for different providers.

3.  **Link Decoding/Extraction**
    *   After getting the embed URLs, `get_episode_url` iterates through a list of `providers` (numbered 1, 2, 3, 4).
    *   For each provider number, it calls the `generate_link` function.
    *   The `generate_link` function uses a `case` statement to call `provider_init` with the provider name and a specific `sed` regex based on the provider number (1: wixmp, 2: youtube, 3: sharepoint, 4: hianime/luf-mp4).
    *   `provider_init` sets the `provider_name` and extracts a `provider_id` from the API response using the specified regex and a complex `sed` expression that appears to decode some obfuscated string.
    *   If a `provider_id` is found, `generate_link` calls `get_links` with this ID.
    *   The `get_links` function fetches the content of the source URL using `curl`.
    *   It then parses the response to find actual video links (`link`, `url`) and associated information like resolution (`resolutionStr`) or m3u8 details. This parsing relies heavily on `sed`.
    *   **Specific Link Handling:**
        *   For `wixmp.com` URLs, it extracts a base link and combines it with identifiers found in the path to form multiple resolution links.
        *   For `master.m3u8` URLs (common for hianime/default provider), it fetches the m3u8 playlist, extracts stream URLs and resolutions, and also searches for a subtitle URL (`subtitles: [{...}]`). It stores the m3u8 referer in a cache file.
        *   Other links found are printed directly.
        *   Links containing `tools.fast4speed.rsvp` are also specially marked as `Yt` and the `allanime_refr` is used as the referer later.
    *   The output of `get_links` for each provider is directed to a temporary file in a cache directory (`$cache_dir`).
    *   After all `generate_link` processes finish (using `wait`), the script concatenates these temporary files, sorts the links (presumably by resolution), and stores them in the `links` variable. The temporary cache directory is then removed.

4.  **Quality Selection**
    *   After obtaining all possible links in the `links` variable, `get_episode_url` calls the `select_quality` function, passing the desired `$quality` setting.
    *   `select_quality` filters the `$links` list based on the `$quality` argument.
    *   If `$quality` is "best", it takes the first link (which is the highest resolution due to sorting).
    *   If `$quality` is "worst", it takes the last link matching a resolution pattern.
    *   Otherwise, it tries to find a link that exactly matches the specified quality string (e.g., "720p").
    *   If the specified quality is not found, it defaults to the "best" quality.
    *   It also checks the player function (`$player_function`) to remove specific types of links (like soft subs or Yt links) if the player is `android`, `iSH`, or `vlc` due to potential compatibility issues with referers.
    *   Based on the selected link (`$result`), it determines if a subtitle file (`subs_flag`) or referrer URL (`refr_flag`) is needed for playback, extracting them from the `$links` variable if available (specifically for m3u8 links).
    *   Finally, it extracts the video URL itself from the selected link (`$result`) and stores it in the `$episode` variable.
    *   A check is performed to ensure a valid source URL was found for the episode; otherwise, the script exits with an error.

5.  **Playback Initiation**
    *   Back in `play_episode`, with the `$episode` variable set (containing the final video URL), and potentially `$subs_flag` and `$refr_flag` set, the script prepares to call the player.
    *   It sets the media title for the player using the anime title and episode number.
    *   If `skip_intro` is enabled, it calls `ani-skip` to get skip times and stores them in `skip_flag`.
    *   It uses a `case` statement based on `$player_function` to determine how to launch the player.
    *   For `mpv*` or `flatpak_mpv`, it constructs the command: `$player_function $skip_flag --force-media-title="..." "$episode" $subs_flag $refr_flag`.
    *   The `nohup ... >/dev/null 2>&1 &` part is used to detach the player process, allowing the script to continue or exit, unless `--no-detach` is specified.
    *   If `--exit-after-play` is set, the script waits for the `mpv` process to finish and exits with its exit code.
    *   Other players like `android_mpv`, `android_vlc`, `iina`, `vlc`, `syncplay`, `catt`, and a special `iSH` case have their specific command structures.
    *   The script stores the played episode URL in a `replay` variable and updates the history file (`update_history`).
    *   If an external menu was used, the script waits for the player process to finish.

**Replication in Go:**

To replicate this flow in Go, you would need to implement the following components:

*   **HTTP Client:** Use Go's `net/http` package to make GET requests to the `allanime_api`, `animeschedule.net`, and the various source URLs. Ensure you handle setting headers like `User-Agent` (`$agent`), `Referer` (`$allanime_refr` or `$m3u8_refr`), and potentially `Origin` if needed (not explicitly shown in sources, but often required for APIs).
*   **GraphQL Query Construction:** Build the GraphQL query strings (`episode_embed_gql`, `episodes_list_gql`, etc.) and encode the variables correctly for the `--data-urlencode` option used by `curl`.
*   **Response Parsing:** Instead of `sed`, `grep`, `cut`, `tr`, and `awk`, you would use Go's standard library:
    *   For JSON responses (from `allanime_api` and `animeschedule`), use the `encoding/json` package to unmarshal the data into Go structs. Define structs that match the expected JSON structure (e.g., for show results, episode sources).
    *   For text-based parsing (like `m3u8` playlists or custom formats from `get_links`), use string manipulation functions, regular expressions (`regexp` package), and potentially dedicated parsing libraries if the formats are complex. The script's heavy use of `sed` for parsing hints at the need for robust string/regex handling.
*   **Link Extraction Logic:** Implement the logic from `generate_link` and `get_links` to identify provider URLs, fetch their content, and extract the actual video stream URLs. This will involve the conditional logic (`case` statements) seen in the script. Specifically, handle the `m3u8` playlist parsing and subtitle extraction.
*   **Quality Selection Logic:** Implement the filtering and selection logic from `select_quality` based on the requested quality string ("best", "worst", "720p", etc.). Include the logic for determining `refr_flag` and `subs_flag`.
*   **Player Invocation:** Use Go's `os/exec` package to execute the external media player (`mpv`, `vlc`, etc.). You will need to construct the command line arguments correctly, including the video URL, title, subtitle file path, referrer URL, and any player-specific flags (like `skip_flag` if you integrate `ani-skip`).
*   **User Interaction:** For selecting anime and episodes, you could use standard input/output, or interface with external tools like `fzf` or `rofi` via `os/exec`, piping data as the script does.
*   **History Management:** Implement the logic to read, update, and write to the history file (`$histfile`).
*   **Configuration/Options:** Parse command-line arguments using the `flag` package or a more powerful library, and handle environment variables (`ANI_CLI_MODE`, `ANI_CLI_PLAYER`, etc.) as seen in the script's setup section.
*   **Dependency Checks:** (Optional but good practice) Check if required player executables are available in the system's PATH or specific locations.
*   **Error Handling:** Implement robust error handling for HTTP requests, parsing failures, and player invocation issues, similar to the `die` function in the script.

Replicating the precise `sed` commands, especially the decoding logic in `provider_init`, will likely be the most intricate part, requiring careful translation of the bash expressions into Go's string manipulation and regex capabilities.

-------------------

Let's break down how the `ani-cli` script handles episode selection, source searching, decoding, quality selection, and playback, and how you can replicate this process in Go.

**1. Episode Selection**

*   **Script Method:** After finding an anime, the script calls the `episodes_list` function with the anime's `showId`. `episodes_list` makes a GraphQL query to `${allanime_api}/api` using `curl`. This query, `episodes_list_gql`, specifically asks for `availableEpisodesDetail` for the given `showId`. The raw JSON response is then processed with `sed` to extract the episode numbers based on the selected `$mode` (sub or dub), clean them up, and sort them numerically. The resulting list of episode numbers is stored in `ep_list`. The script then presents this list to the user via the `nth` function, which uses `launcher` (either `fzf` or `rofi`) to get the user's selection. The selected episode number (or range) is stored in `$ep_no`.
*   **Go Replication:**
    *   **GraphQL Query:** Construct the GraphQL query string in Go. You can define it as a multiline string.
    *   **HTTP Request:** Use the `net/http` package to perform a POST or GET request to the API endpoint (`${allanime_api}/api`). You'll need to set the `Referer` (`$allanime_refr`) and `User-Agent` (`$agent`) headers using `req.Header.Set("Referer", refererValue)` and `req.Header.Set("User-Agent", userAgentValue)`. Encode the GraphQL query and variables (the `showId`) as data for the request. You can use `net/url.Values` or manually construct the query parameters for a GET, or encode a JSON payload for a POST if the API supports it (the script uses GET with `--data-urlencode`).
    *   **JSON Parsing:** Read the response body using `ioutil.ReadAll` or `io.Copy`. The API returns JSON. You need to define Go structs that match the structure of the `availableEpisodesDetail` part of the JSON response. Use the `encoding/json` package's `json.Unmarshal` function to parse the JSON into your structs.
    *   **Extract Episode Numbers:** Iterate through the unmarshaled data structure to extract the episode numbers for the desired `$mode` (sub or dub). Store these in a Go slice of strings or floats.
    *   **Sort Episodes:** Use Go's `sort` package to sort the episode numbers numerically. Convert them to numbers (e.g., `float64`) for sorting and then back to strings if needed.
    *   **User Selection:**
        *   **Basic CLI:** Print the sorted episode list to the console using `fmt.Println`. Read the user's input using `bufio.NewReader(os.Stdin).ReadString('\n')`. Parse the input to handle single numbers or ranges (e.g., "5", "10-12").
        *   **External Menu (`fzf`/`rofi`):** To replicate the `launcher`/`nth` behavior, use the `os/exec` package to run `fzf` or `rofi`. Pipe the episode list (formatted with numbers if needed) to the standard input of the external process using `cmd.StdinPipe()`. Read the selected line from `cmd.StdoutPipe()`. Parse the selected line to get the episode number(s). This requires careful handling of processes and pipes in Go.
    *   **Store Selection:** Store the chosen episode number(s) in a variable.

**2. Source Searching (Getting Embed URLs)**

*   **Script Method:** Once `$ep_no` is set, the `play` function calls `play_episode`, which in turn calls `get_episode_url`. `get_episode_url` makes another GraphQL query, `episode_embed_gql`, to the `allanime_api`, providing the `showId`, `translationType` (`$mode`), and `episodeString` (`$ep_no`). This query asks for `sourceUrls`. The `curl` command includes the `$allanime_refr` and `$agent` headers. The script then uses `tr` and `sed` to parse the JSON response, extracting lines that contain `sourceUrl` and `sourceName`, formatted as `"SourceName :--sourceUrl"`. This parsed output is stored in the `resp` variable.
*   **Go Replication:**
    *   **GraphQL Query:** Define the `episode_embed_gql` query string in Go.
    *   **HTTP Request:** Use `net/http` to make a GET request to the API endpoint with the appropriate variables (`showId`, `translationType`, `episodeString`) encoded in the URL query parameters. Include the `Referer` and `User-Agent` headers.
    *   **JSON Parsing:** Read the response body. Although the script uses `sed` to extract specific patterns, the API response is likely JSON. Define Go structs to match the structure of the `episode` and `sourceUrls` objects in the JSON response. Use `json.Unmarshal` to parse the JSON. The `sourceUrls` field should contain a slice of objects, each with `sourceName` and `sourceUrl`.
    *   **Store Source Data:** Store the extracted `sourceName` and `sourceUrl` pairs (the equivalent of the script's `resp` variable) in a Go slice of custom structs (e.g., `[]struct{ Name, URL string }`).

**3. Link Decoding/Extraction**

This is arguably the most complex part to replicate directly due to the script's reliance on intricate `sed` commands for parsing and decoding.

*   **Script Method:** `get_episode_url` iterates through a predefined list of `providers` (1, 2, 3, 4). For each provider number, it calls `generate_link`. `generate_link` uses a `case` statement to map the provider number to a name (wixmp, youtube, sharepoint, hianime) and a specific `sed` regex pattern. It calls `provider_init` with the name and regex.
    *   `provider_init`: This function is crucial for decoding. It takes the `resp` variable (the output from `get_episode_url` containing `"SourceName :--sourceUrl"` lines), applies the provider-specific `sed` regex (`sed -n "$2"`) to find the relevant line, extracts the part after `":"` (`cut -d':' -f2`), and then performs a complex character-by-character hex decoding and substitution using a long chain of `sed` commands. This decoded string, with `/clock` replaced by `/clock.json`, is stored in `provider_id`.
    *   `generate_link`: If `provider_id` is not empty, `generate_link` calls `get_links` with `provider_id` as an argument.
    *   `get_links`: This function fetches the content from the URL specified by `provider_id` using `curl` with the `$allanime_refr` header. It then parses the response (`episode_link`) using more `sed` commands to find actual video links and resolution information.
        *   **wixmp.com**: Extracts parts of the URL and combines them with identifiers to create multiple resolution links, sorted numerically.
        *   **master.m3u8**: Fetches the m3u8 playlist content using `curl` with a newly extracted `m3u8_refr` (which is cached). It parses the m3u8 playlist using `sed` to extract stream URLs and resolutions, adding a "cc>" prefix and the relative link path. It also searches the original response for a subtitle URL (`subtitle >...`).
        *   **Other links**: Prints the link directly if found.
        *   **tools.fast4speed.rsvp**: Checks if the URL contains this string and marks it with "Yt >".
    *   The output of `get_links` is redirected to temporary files named after the provider number in a temporary cache directory (`$cache_dir`).
    *   After all `generate_link` calls finish (`wait`), the script concatenates the temporary files, sorts the combined links (`sort -g -r -s`), and stores them in the `links` variable. The cache directory is then removed.
*   **Go Replication:**
    *   **Decode `provider_id`:** This `sed` chain `sed 's/../&\<br>/g' | sed 's/^79$/A/g;...' | tr -d '\n' | sed "s/\/clock/\/clock\.json/"` is a hex decoding with custom character mapping.
        *   In Go, you would iterate through the extracted `provider_id` string two characters at a time.
        *   Convert each two-character substring from hexadecimal to an integer using `strconv.ParseInt(hexString, 16, 64)`.
        *   Use a `switch` statement or a `map[int]string` to map the resulting integer values to the specific characters (`A`, `B`, `C`, ..., `a`, `b`, `c`, ..., `0`, `1`, `2`, ...).
        *   Concatenate the resulting characters to form the decoded string.
        *   Finally, use `strings.ReplaceAll(decodedString, "/clock", "/clock.json")`.
    *   **Fetch Source Content:** Use `net/http.Get` to fetch the content of the decoded `provider_id` URL. Set the `Referer` header to `$allanime_refr`.
    *   **Parse Source Content (`get_links` logic):**
        *   Read the response body.
        *   Use Go's `strings` functions and the `regexp` package to implement the parsing logic that the script does with `sed`. Look for patterns like `"link": "..."` and `"resolutionStr": "..."` or `"hls", "url": "..."`.
        *   **wixmp.com**: If the URL matches, use `strings.Split` and `fmt.Sprintf` to construct the multiple resolution links. Sort the resulting Go slice.
        *   **master.m3u8**: If the URL matches:
            *   Extract the `m3u8_refr` using regex on the *original* source response (`resp`).
            *   Fetch the m3u8 playlist content with another `net/http.Get` using the extracted `m3u8_refr`.
            *   Parse the m3u8 content line by line. Use regex (`regexp.Compile`) to find lines starting with `#EXT-X-STREAM-INF` and extract the resolution (e.g., `...x1080`) and the subsequent URL line. Format the output as "resolution >url" with the "cc>" prefix and relative path as the script does.
            *   Search the *original* source response (`resp`) for the subtitle URL pattern (`"subtitles":[{"lang":"en"...`) using regex.
        *   **tools.fast4speed.rsvp**: Check for the string using `strings.Contains` and add the "Yt >" prefix.
    *   **Concurrency:** Use Go Goroutines and `sync.WaitGroup` to fetch links from different providers concurrently, similar to the script's `&` followed by `wait`.
    *   **Collect and Sort Links:** Collect the links found by processing each provider into a central Go slice of strings (e.g., `[]string{"1080 >url1", "720 >url2", "subtitle >suburl", "m3u8_refr >refr"}`). Implement a custom sort function for this slice that sorts numerically based on the resolution part ("1080", "720"), prioritizing higher resolutions, while keeping non-resolution lines (like subtitle or referer) appropriately.
    *   **Store Links:** Store the sorted slice of link strings in a variable (equivalent to the script's `links`).

**4. Quality Selection**

*   **Script Method:** The `select_quality` function takes the list of `links` and the desired quality string. It first filters out subtitle and referer links if the player is `android`, `iSH`, or `vlc` due to compatibility issues. It then uses a `case` statement to select the link: `best` takes the first link (highest resolution after sorting), `worst` takes the last link matching a resolution pattern, and any other string attempts to find a link with that exact quality (e.g., "720p"). If the specified quality isn't found, it defaults to `best`. It checks the selected link (`$result`) for the "cc>" prefix or "tools.fast4speed.rsvp" to determine if a referrer (`refr_flag`) or subtitle file (`subs_flag`) is needed, extracting their URLs from the `$links` variable. The final video URL is extracted from the selected link (part after ">") and stored in `$episode`. An error is thrown if no valid source is found.
*   **Go Replication:**
    *   **Filtering:** Implement this logic using conditional statements (`if player == "android" || ...`). Create a new slice containing only the compatible links based on the player type.
    *   **Quality Selection:** Implement a Go function that takes the filtered slice of link strings and the quality string. Use a `switch` statement for "best" and "worst". For specific qualities, iterate through the slice, check if a link string `strings.Contains(link, qualityString)`, and select the first match. If no match is found, default to the first element of the slice (the "best").
    *   **Extract URL, Subtitle, Referer:** Once the desired link string is selected, use `strings.Split(selectedLink, ">")` to separate the quality/prefix part from the URL. The URL is the second part (``).
    *   Check the selected link string for "cc>" or "tools.fast4speed.rsvp" prefixes using `strings.Contains`.
    *   If "cc>" is present, iterate through the *original* `links` slice to find the line starting with "subtitle >" and extract the subtitle URL.
    *   If "cc>" is present, iterate through the *original* `links` slice to find the line starting with "m3u8_refr >" and extract the referrer URL.
    *   If "tools.fast4speed.rsvp" is present, set the referrer URL to `$allanime_refr`.
    *   Store the extracted video URL, subtitle URL (if found), and referrer URL (if found) in variables.
    *   Add an error check: if the video URL variable is empty after this process, report that no valid sources were found.

**5. Playback Initiation (with mpv)**

*   **Script Method:** Back in `play_episode`, with the `$episode` variable containing the final video URL, the script constructs the command to launch the player. For `mpv*` or `flatpak_mpv`:
    *   It includes `$skip_flag` if `skip_intro` is enabled (obtained from `ani-skip`).
    *   It adds `--force-media-title="..."` with the anime title and episode number.
    *   It adds `$subs_flag` if a subtitle URL was found (`--sub-file=...`).
    *   It adds `$refr_flag` if a referrer was needed (`--referrer=...`).
    *   The command is executed using `nohup ... >/dev/null 2>&1 &` to run in the background unless `--no-detach` is used.
    *   If `--no-detach` is used, it waits for `mpv` to exit and potentially exits the script itself if `--exit-after-play` is enabled.
*   **Go Replication (for mpv):**
    *   **Construct Command:** Use `os/exec.Command` to build the command for the `mpv` executable.
    *   The first argument is the player path/name (e.g., `"mpv"`).
    *   Create a slice of strings for arguments.
    *   Add `--force-media-title` followed by the title string.
    *   Add the video URL.
    *   If a subtitle URL was found, add the flag `--sub-file=` followed by the URL.
    *   If a referrer URL was found, add the flag `--referrer=` followed by the URL.
    *   If `skip_intro` is enabled and `ani-skip` integration is desired, you would need to run `ani-skip` first using `os/exec.Command` to get the skip arguments and add them to the `mpv` command arguments slice.
    *   **Execute Command:**
        *   For detached playback (`nohup ... &`): Call `cmd.Start()`. This starts the process and returns immediately. You'll likely want to redirect `Stdout` and `Stderr` to `/dev/null` (or the equivalent in Go) using `cmd.Stdout = os.DevNull` and `cmd.Stderr = os.DevNull`.
        *   For attached playback: Call `cmd.Run()`. This will wait for the process to complete. To capture the exit code, you can check the error returned by `cmd.Run()`. If it's an `ExitError`, you can access the status code.
    *   **Handle `--no-detach` and `--exit-after-play`:** Use conditional logic based on your Go configuration variables (equivalent to `$no_detach`, `$exit_after_play`) to decide whether to use `cmd.Start()` or `cmd.Run()`, and whether to `os.Exit(exitCode)` after `cmd.Run()`.

This detailed breakdown covers the core steps the script takes and how to translate the logic into Go using appropriate standard library packages and techniques. The primary challenge lies in accurately replicating the complex parsing and decoding steps done with `sed` using Go's string manipulation, regex, and potentially JSON parsing capabilities.

------------------------

