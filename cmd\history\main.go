package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"

	"github.com/stl3/go-ani/database"
)

func main() {
	var (
		clear   = flag.Bool("clear", false, "Clear all history")
		delete  = flag.Bool("delete", false, "Delete specific anime from history")
		backup  = flag.Bool("backup", false, "Create backup of database")
		restore = flag.Bool("restore", false, "Restore from backup")
		list    = flag.Bool("list", false, "List all history entries")
	)
	flag.Parse()

	homeDir, err := os.UserHomeDir()
	if err != nil {
		fmt.Printf("Error getting home directory: %v\n", err)
		return
	}

	dataDir := filepath.Join(homeDir, ".go-ani")
	db, err := database.NewDB(dataDir)
	if err != nil {
		fmt.Printf("Error initializing database: %v\n", err)
		return
	}
	defer db.Close()

	switch {
	case *clear:
		if err := db.ClearHistory(); err != nil {
			fmt.Printf("Error clearing history: %v\n", err)
		} else {
			fmt.Println("History cleared successfully")
		}

	case *delete:
		entries, err := db.GetContinueOptions()
		if err != nil {
			fmt.Printf("Error getting history: %v\n", err)
			return
		}
		if len(entries) == 0 {
			fmt.Println("No history entries found")
			return
		}
		fmt.Println("Select anime to delete:")
		for i, entry := range entries {
			fmt.Printf("%d. %s\n", i+1, entry.Title)
		}
		fmt.Print("Enter number: ")
		var choice int
		if _, err := fmt.Scanln(&choice); err != nil || choice < 1 || choice > len(entries) {
			fmt.Println("Invalid selection")
			return
		}
		selected := entries[choice-1]
		if err := db.DeleteEntry(selected.AnimeID); err != nil {
			fmt.Printf("Error deleting entry: %v\n", err)
		} else {
			fmt.Printf("Deleted '%s' from history\n", selected.Title)
		}

	case *backup:
		if err := db.CreateBackup(); err != nil {
			fmt.Printf("Error creating backup: %v\n", err)
		} else {
			fmt.Println("Backup created successfully")
		}

	case *restore:
		if err := db.RestoreFromBackup(); err != nil {
			fmt.Printf("Error restoring from backup: %v\n", err)
		} else {
			fmt.Println("Database restored from backup successfully")
		}

	case *list:
		entries, err := db.GetContinueOptions()
		if err != nil {
			fmt.Printf("Error getting history: %v\n", err)
			return
		}

		if len(entries) == 0 {
			fmt.Println("No history entries found")
			return
		}

		fmt.Println("History entries:")
		for _, entry := range entries {
			fmt.Printf("- %s (Episode %d/%d) - Last watched: %s\n",
				entry.Title, entry.CurrentEp, entry.TotalEps, entry.LastWatched.Format("2006-01-02 15:04"))
		}

	default:
		flag.Usage()
	}
}