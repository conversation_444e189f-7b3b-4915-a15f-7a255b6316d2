package api

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

const (
	VersionNumber = "1.0.0"
	UserAgent     = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
	AllanimeRefr  = "https://allanime.to"
	AllanimeBase  = "https://allanime.to"
	AllanimeAPI   = "https://api.allanime.to"
	ProxyURL      = "http://127.0.0.1:8080"
)

// Client represents the AllAnime API client
type Client struct {
	baseURL string
	referer string
	proxy   string
}

// NewClient creates a new AllAnime API client
func NewClient() *Client {
	return &Client{
		baseURL: AllanimeAPI,
		referer: AllanimeRefr,
		proxy:   ProxyURL,
	}
}

// SearchAnime searches for anime with the given query and translation type
func (c *Client) SearchAnime(query string, translationType string) ([]SearchResult, error) {
	fmt.Printf("Searching for anime with query: %s, type: %s\n", query, translationType)

	variables := map[string]interface{}{
		"search": map[string]interface{}{
			"allowAdult":   false,
			"allowUnknown": false,
			"query":        query,
		},
		"limit":           40,
		"page":            1,
		"translationType": translationType,
		"countryOrigin":   "ALL",
	}

	fmt.Printf("Sending GraphQL request with variables: %+v\n", variables)
	response, err := c.graphqlRequest(searchQuery, variables)
	if err != nil {
		fmt.Printf("GraphQL request failed: %v\n", err)
		return nil, fmt.Errorf("API request failed: %v", err)
	}

	// Debug the raw response
	fmt.Printf("Raw response: %s\n", response.Raw)

	// Parse response
	edges := response.Get("data.shows.edges").Array()
	fmt.Printf("Found %d results\n", len(edges))

	var results []SearchResult
	for _, edge := range edges {
		node := edge.Get("node")
		if node.Exists() {
			id := node.Get("_id").String()
			title := node.Get("name").String()
			episodes := node.Get("availableEpisodes").Int()
			fmt.Printf("Found anime: ID=%s, Title=%s, Episodes=%d\n", id, title, episodes)
			results = append(results, SearchResult{
				ID:       id,
				Title:    title,
				Episodes: int(episodes),
			})
		}
	}

	return results, nil
}

// GetEpisodesList retrieves the list of available episodes for a show
func (c *Client) GetEpisodesList(showID string) ([]EpisodeSource, error) {
	variables := map[string]interface{}{
		"showId": showID,
	}

	fmt.Printf("Getting episodes for show: %s\n", showID)
	response, err := c.graphqlRequest(episodeListQuery, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to get episode list: %v", err)
	}

	show := response.Get("data.show").Value().(map[string]interface{})
	if show == nil {
		return nil, fmt.Errorf("show not found")
	}

	availableEpisodes := show["availableEpisodesDetail"].([]interface{})
	var episodes []EpisodeSource
	for _, ep := range availableEpisodes {
		e := ep.(map[string]interface{})
		episodeString := e["episodeString"].(string)
		episodes = append(episodes, EpisodeSource{
			URL:      "",
			Type:     "",
			Language: "",
			Quality:  episodeString,
		})
	}

	return episodes, nil
}

// GetEpisodeSources retrieves the available sources for an episode
func (c *Client) GetEpisodeSources(showID, episodeString, translationType string) ([]EpisodeSource, error) {
	variables := map[string]interface{}{
		"showId":          showID,
		"translationType": translationType,
		"episodeString":   episodeString,
	}

	fmt.Printf("Getting sources for episode: %s - %s\n", showID, episodeString)
	response, err := c.graphqlRequest(episodeSourcesQuery, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to get episode sources: %v", err)
	}

	episode := response.Get("data.episode").Value().(map[string]interface{})
	if episode == nil {
		return nil, fmt.Errorf("episode not found")
	}

	sourceURLs := episode["sourceUrls"].([]interface{})
	var sources []EpisodeSource
	for _, url := range sourceURLs {
		s := url.(map[string]interface{})
		sources = append(sources, EpisodeSource{
			URL:      s["url"].(string),
			Type:     s["type"].(string),
			Language: s["language"].(string),
			Quality:  s["quality"].(string),
		})
	}

	return sources, nil
}

// graphqlRequest makes a GraphQL request to the API
func (c *Client) graphqlRequest(query string, variables map[string]interface{}) (gjson.Result, error) {
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		// Create request body
		reqBody, err := json.Marshal(map[string]interface{}{
			"query":     query,
			"variables": variables,
		})
		if err != nil {
			return gjson.Result{}, fmt.Errorf("error marshaling request body: %v", err)
		}

		// Debug request body
		fmt.Printf("Request body: %s\n", string(reqBody))

		// Create request
		req, err := http.NewRequest("POST", fmt.Sprintf("%s/graphql", c.baseURL), bytes.NewBuffer(reqBody))
		if err != nil {
			return gjson.Result{}, fmt.Errorf("error creating request: %v", err)
		}

		// Add headers
		req.Header.Set("Referer", c.referer)
		req.Header.Set("Origin", c.referer)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", UserAgent)
		req.Header.Set("Accept", "application/json")
		req.Header.Set("Accept-Language", "en-US,en;q=0.9")
		req.Header.Set("Sec-Fetch-Site", "cross-site")
		req.Header.Set("Sec-Fetch-Mode", "cors")
		req.Header.Set("Sec-Fetch-Dest", "empty")

		// Debug headers
		fmt.Printf("Request headers: %+v\n", req.Header)

		// Set timeout
		client := &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				Proxy: http.ProxyURL(&url.URL{
					Scheme: "http",
					Host:   c.proxy,
				}),
			},
		}

		// Execute request
		fmt.Printf("Sending request to: %s/graphql\n", c.baseURL)
		resp, err := client.Do(req)
		if err != nil {
			fmt.Printf("Request attempt %d failed: %v\n", i+1, err)
			if i < maxRetries-1 {
				time.Sleep(time.Second * time.Duration(i+1))
				continue
			}
			return gjson.Result{}, fmt.Errorf("failed to make request after %d attempts: %v", maxRetries, err)
		}
		defer resp.Body.Close()

		// Debug response status
		fmt.Printf("Response status: %d %s\n", resp.StatusCode, resp.Status)

		// Read response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("Error reading response body: %v\n", err)
			if i < maxRetries-1 {
				time.Sleep(time.Second * time.Duration(i+1))
				continue
			}
			return gjson.Result{}, fmt.Errorf("failed to read response after %d attempts: %v", maxRetries, err)
		}

		// Debug response body
		fmt.Printf("Response body: %s\n", string(body))

		// Check for Cloudflare challenge
		if resp.StatusCode == 522 || resp.StatusCode >= 500 {
			fmt.Printf("Cloudflare challenge detected, retrying with delay...\n")
			if i < maxRetries-1 {
				// Add a longer delay for Cloudflare challenges
				time.Sleep(5 * time.Second * time.Duration(i+1))
				continue
			}
		}

		if resp.StatusCode != http.StatusOK {
			fmt.Printf("Non-200 status code received: %d\n", resp.StatusCode)
			return gjson.Result{}, fmt.Errorf("server returned status code %d: %s", resp.StatusCode, string(body))
		}

		return gjson.ParseBytes(body), nil
	}
	return gjson.Result{}, fmt.Errorf("max retries exceeded")
}
		referer: referer,
	}
}

const searchQuery = `query($search: SearchInput, $limit: Int, $page: Int, $translationType: TranslationTypeEnum, $countryOrigin: CountryOriginEnum) {
	shows(search: $search, limit: $limit, page: $page, translationType: $translationType, countryOrigin: $countryOrigin) {
		edges {
			node {
				_id
				name
				availableEpisodes
			}
		}
	}
}`

const episodeListQuery = `query($showId: String!) {
	show(_id: $showId) {
		_id
		availableEpisodesDetail {
			episodeString
		}
	}
}`

const episodeSourcesQuery = `query($showId: String!, $translationType: TranslationTypeEnum!, $episodeString: String!) {
	episode(showId: $showId, translationType: $translationType, episodeString: $episodeString) {
		episodeString
		sourceUrls {
			url
			type
			language
			quality
		}
	}
}`

type SearchResult struct {
	ID       string
	Title    string
	Episodes int
}

type EpisodeSource struct {
	URL      string
func (c *Client) GetEpisodeSources(showID string, episodeString string, translationType string) ([]EpisodeSource, error) {
	variables := map[string]interface{}{
		"showId":          showID,
		"episodeString":   episodeString,
		"translationType": translationType,
	}

	response, err := c.graphqlRequest(episodeSourcesQuery, variables)
	if err != nil {
		return nil, fmt.Errorf("API request failed: %v", err)
	}

	var sources []EpisodeSource
	response.Get("data.episode.sourceUrls").ForEach(func(_, value gjson.Result) bool {
		sourceUrl := value.Get("url").String()
		sourceType := value.Get("type").String()
		quality := value.Get("priority").String()
		
		downloadUrl := value.Get("downloads.downloadUrl").String()
		sourceName := value.Get("sourceName").String()

		// Skip if URL is empty
		if sourceUrl == "" && downloadUrl == "" {
			return true
		}

		// Handle YouTube URLs (start with --)
		if strings.HasPrefix(sourceUrl, "--") {
			// Extract the base64 encoded part
			parts := strings.Split(sourceUrl, "--")
			if len(parts) != 2 {
				return true
			}
			
			// Decode using the specific mapping from the bash script
			decoded := strings.ReplaceAll(parts[1], "01", "9")
			decoded = strings.ReplaceAll(decoded, "08", "0")
			decoded = strings.ReplaceAll(decoded, "05", "=")
			decoded = strings.ReplaceAll(decoded, "0a", "2")
			decoded = strings.ReplaceAll(decoded, "0b", "3")
			decoded = strings.ReplaceAll(decoded, "0c", "4")
			decoded = strings.ReplaceAll(decoded, "07", "?")
			decoded = strings.ReplaceAll(decoded, "00", "8")
			decoded = strings.ReplaceAll(decoded, "5c", "d")
			decoded = strings.ReplaceAll(decoded, "0f", "7")
			decoded = strings.ReplaceAll(decoded, "5e", "f")
			decoded = strings.ReplaceAll(decoded, "17", "/")
			decoded = strings.ReplaceAll(decoded, "54", "l")
			decoded = strings.ReplaceAll(decoded, "09", "1")
			decoded = strings.ReplaceAll(decoded, "48", "p")
			decoded = strings.ReplaceAll(decoded, "4f", "w")
			decoded = strings.ReplaceAll(decoded, "0e", "6")
			decoded = strings.ReplaceAll(decoded, "5b", "c")
			decoded = strings.ReplaceAll(decoded, "5d", "e")
			decoded = strings.ReplaceAll(decoded, "0d", "5")
			decoded = strings.ReplaceAll(decoded, "53", "k")
			decoded = strings.ReplaceAll(decoded, "1e", "&")
			decoded = strings.ReplaceAll(decoded, "5a", "b")
			decoded = strings.ReplaceAll(decoded, "59", "a")
			decoded = strings.ReplaceAll(decoded, "4a", "r")
			decoded = strings.ReplaceAll(decoded, "4c", "t")
			decoded = strings.ReplaceAll(decoded, "4e", "v")
			decoded = strings.ReplaceAll(decoded, "57", "o")
			decoded = strings.ReplaceAll(decoded, "51", "i")
			
			// Add /clock.json to the end
			sourceUrl = decoded + "/clock.json"
			sourceType = "youtube"
			quality = "1080p" // YouTube URLs are typically 1080p
		}

		// Handle data: URLs
		if strings.HasPrefix(sourceUrl, "data:") {
			parts := strings.Split(sourceUrl, ",")
			if len(parts) != 2 {
				return true
			}
			decoded, err := base64.StdEncoding.DecodeString(parts[1])
			if err != nil {
				return true
			}
			sourceUrl = string(decoded)
		}

		// Handle special URLs
		if strings.Contains(sourceUrl, "repackager.wixmp.com") {
			// Extract base URL and quality from wixmp URL
			baseURL := strings.Split(sourceUrl, ",")[0]
			qualityParts := strings.Split(sourceUrl, "/")
			for _, part := range qualityParts {
				if strings.HasSuffix(part, "p") {
					quality = part
					break
				}
			}
			sourceUrl = baseURL
			sourceType = "mp4"
		} else if strings.Contains(sourceUrl, "vipanicdn") || strings.Contains(sourceUrl, "anifastcdn") {
			if strings.Contains(sourceUrl, "original.m3u") {
				// Keep original m3u8 URL
				sourceType = "hls"
			} else {
				// Extract base URL and quality from m3u8 URL
				baseURL := sourceUrl[:strings.LastIndex(sourceUrl, "/")+1]
				quality = strings.TrimSuffix(sourceUrl[strings.LastIndex(sourceUrl, "/")+1:], ".m3u8")
				sourceUrl = baseURL + quality + ".m3u8"
				sourceType = "hls"
			}
		}

		// Try to extract quality from source name or URL
		if strings.Contains(sourceName+sourceUrl, "1080") {
			quality = "1080p"
		} else if strings.Contains(sourceName+sourceUrl, "720") {
			quality = "720p"
		} else if strings.Contains(sourceName+sourceUrl, "480") {
			quality = "480p"
		} else {
			quality = "1080p" // Default to high quality if not specified
		}

		// Clean up source type
		switch {
		case strings.Contains(sourceUrl, ".m3u8"):
			sourceType = "hls"
		case strings.Contains(sourceUrl, ".mp4"):
			sourceType = "mp4"
		case sourceType == "":
			sourceType = "iframe" // Default to iframe if type not specified
		}

		// Add source to list
		sources = append(sources, EpisodeSource{
			URL:      sourceUrl,
			Type:     sourceType,
			Quality:  quality,
			Language: translationType,
		})

		return true
	})

	// Sort sources by quality (highest first)
	sort.Slice(sources, func(i, j int) bool {
		// Extract numeric quality values
		qi := strings.TrimSuffix(sources[i].Quality, "p")
		qj := strings.TrimSuffix(sources[j].Quality, "p")
		vi, _ := strconv.Atoi(qi)
		vj, _ := strconv.Atoi(qj)
		return vi > vj
	})

	return sources, nil
}

func (c *Client) graphqlRequest(query string, variables map[string]interface{}) (gjson.Result, error) {
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		// Create request body
		reqBody, err := json.Marshal(map[string]interface{}{
			"query":     query,
			"variables": variables,
		})
		if err != nil {
			return gjson.Result{}, fmt.Errorf("error marshaling request body: %v", err)
		}

		// Debug request body
		fmt.Printf("Request body: %s\n", string(reqBody))

		// Create request
		req, err := http.NewRequest("POST", fmt.Sprintf("%s/graphql", c.baseURL), bytes.NewBuffer(reqBody))
		if err != nil {
			return gjson.Result{}, fmt.Errorf("error creating request: %v", err)
		}

		// Add headers
		req.Header.Set("Referer", c.referer)
		req.Header.Set("Origin", c.referer)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", UserAgent)
		req.Header.Set("Accept", "application/json")
		req.Header.Set("Accept-Language", "en-US,en;q=0.9")
		req.Header.Set("Sec-Fetch-Site", "cross-site")
		req.Header.Set("Sec-Fetch-Mode", "cors")
		req.Header.Set("Sec-Fetch-Dest", "empty")

		// Debug headers
		fmt.Printf("Request headers: %+v\n", req.Header)

		// Set timeout
		client := &http.Client{
			Timeout: 30 * time.Second,
		}

		// Execute request
		fmt.Printf("Sending request to: %s/graphql\n", c.baseURL)
		resp, err := client.Do(req)
		if err != nil {
			fmt.Printf("Request attempt %d failed: %v\n", i+1, err)
			if i < maxRetries-1 {
				time.Sleep(time.Second * time.Duration(i+1))
				continue
			}
			return gjson.Result{}, fmt.Errorf("failed to make request after %d attempts: %v", maxRetries, err)
		}
		defer resp.Body.Close()

		// Debug response status
		fmt.Printf("Response status: %d %s\n", resp.StatusCode, resp.Status)

		// Read response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("Error reading response body: %v\n", err)
			if i < maxRetries-1 {
				time.Sleep(time.Second * time.Duration(i+1))
				continue
			}
			return gjson.Result{}, fmt.Errorf("failed to read response after %d attempts: %v", maxRetries, err)
		}

		// Debug response body
		fmt.Printf("Response body: %s\n", string(body))

		// Check for Cloudflare challenge
		if resp.StatusCode == 522 || resp.StatusCode >= 500 {
			fmt.Printf("Cloudflare challenge detected, retrying with delay...\n")
			if i < maxRetries-1 {
				// Add a longer delay for Cloudflare challenges
				time.Sleep(5 * time.Second * time.Duration(i+1))
				continue
			}
		}

		if resp.StatusCode != http.StatusOK {
			fmt.Printf("Non-200 status code received: %d\n", resp.StatusCode)
			return gjson.Result{}, fmt.Errorf("server returned status code %d: %s", resp.StatusCode, string(body))
		}

		return gjson.ParseBytes(body), nil
	}
	return gjson.Result{}, fmt.Errorf("max retries exceeded")
}
