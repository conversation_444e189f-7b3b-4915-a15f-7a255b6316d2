package api

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

type Client struct {
	baseURL string
	proxy   string
}

const (
	apiBaseURL = "https://api.allanime.day/api"
	userAgent  = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
	referer    = "https://allanime.to"
)

func NewClient() *Client {
	return &Client{
		baseURL: apiBaseURL,
		proxy:   "http://127.0.0.1:8080",
	}
}

func (c *Client) apiRequest(endpoint string, query string, variables map[string]interface{}) (gjson.Result, error) {
	const maxRetries = 3

	for i := 0; i < maxRetries; i++ {
		// Create request
		req, err := http.NewRequest("GET", apiBaseURL+endpoint, nil)
		if err != nil {
			return gjson.Result{}, fmt.Errorf("failed to create request: %v", err)
		}

		// Convert variables to JSON string
		variablesJSON, err := json.Marshal(variables)
		if err != nil {
			return gjson.Result{}, fmt.Errorf("failed to make request after %d attempts: %v", maxRetries, err)
		}

		// URL encode the parameters
		q := req.URL.Query()
		q.Add("query", strings.ReplaceAll(strings.ReplaceAll(query, "\n", " "), "\t", " "))
		q.Add("variables", strings.ReplaceAll(strings.ReplaceAll(string(variablesJSON), "\n", " "), "\t", " "))
		req.URL.RawQuery = q.Encode()

		// Set headers
		req.Header.Set("User-Agent", userAgent)
		req.Header.Set("Referer", referer)
		req.Header.Set("Accept", "application/json")
		req.Header.Set("Accept-Language", "en-US,en;q=0.9")

		// Execute request
		fmt.Printf("Sending request to: %s\n", req.URL.String())
		client := &http.Client{
			Timeout: 30 * time.Second,
		}
		resp, err := client.Do(req)
		if err != nil {
			fmt.Printf("Request attempt %d failed: %v\n", i+1, err)
			if i < maxRetries-1 {
				time.Sleep(5 * time.Second * time.Duration(i+1))
				continue
			}
			return gjson.Result{}, fmt.Errorf("failed to make request after %d attempts: %v", maxRetries, err)
		}
		defer resp.Body.Close()

		// Debug response status
		fmt.Printf("Response status: %d %s\n", resp.StatusCode, resp.Status)

		// Read response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("Error reading response body: %v\n", err)
			if i < maxRetries-1 {
				time.Sleep(5 * time.Second * time.Duration(i+1))
				continue
			}
			return gjson.Result{}, fmt.Errorf("failed to read response after %d attempts: %v", maxRetries, err)
		}

		// Debug response body
		fmt.Printf("Response body: %s\n", string(body))

		if resp.StatusCode != http.StatusOK {
			fmt.Printf("Non-200 status code received: %d\n", resp.StatusCode)
			return gjson.Result{}, fmt.Errorf("server returned status code %d: %s", resp.StatusCode, string(body))
		}

		// Parse response
		response := gjson.ParseBytes(body)
		if !response.Exists() {
			return gjson.Result{}, fmt.Errorf("invalid response format: %s", string(body))
		}

		return response, nil
	}
	return gjson.Result{}, fmt.Errorf("max retries exceeded")
}

const searchQuery = `query($search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType) {
	shows(search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin) {
		edges {
			_id
			name
			availableEpisodes
			__typename
		}
	}
}`

const episodesQuery = `query($showId: String!) {    show(        _id: $showId    ) {        _id availableEpisodesDetail    }}`

const episodeEmbedQuery = `query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) {    episode(        showId: $showId        translationType: $translationType        episodeString: $episodeString    ) {        episodeString sourceUrls    }}`

const episodeListQuery = `query (
	$showId: String!
) {
	show(
		_id: $showId
	) {
		_id
		availableEpisodesDetail
	}
}`

const episodeSourcesQuery = `query (
	$showId: String!
	$translationType: VaildTranslationTypeEnumType!
	$episodeString: String!
) {
	episode(
		showId: $showId
		translationType: $translationType
		episodeString: $episodeString
	) {
		episodeString
		sourceUrls
	}
}`

type SearchResult struct {
	ID       string
	Title    string
	Episodes int
}

type Episode struct {
	EpisodeNumber string `json:"episodeString"`
	SourceURLs    []string
}

type EpisodeDetail struct {
	EpisodeNumber string
	AvailableEpisodes map[string][]string
}

type EpisodeSource struct {
	URL      string  `json:"sourceUrl"`
	Priority float64 `json:"priority"`
	Type     string  `json:"sourceName"`
	Language string
	Quality  string
}

func (c *Client) SearchAnime(query string, translationType string) ([]SearchResult, error) {
	fmt.Printf("Searching for anime with query: %s, type: %s\n", query, translationType)

	// Ensure query is properly formatted
	query = strings.TrimSpace(query)
	if query == "" {
		fmt.Println("Error: Query cannot be empty")
		return nil, fmt.Errorf("empty query")
	}

	// Create variables with proper query
	variables := map[string]interface{}{
		"search": map[string]interface{}{
			"allowAdult":   false,
			"allowUnknown": false,
			"query":        query,
		},
		"limit":           40,
		"page":            1,
		"translationType": translationType,
		"countryOrigin":   "ALL",
	}

	fmt.Printf("Sending API request with variables: %+v\n", variables)
	response, err := c.apiRequest("/api", searchQuery, variables)
	if err != nil {
		fmt.Printf("API request failed: %v\n", err)
		return nil, fmt.Errorf("API request failed: %v", err)
	}

	// Debug the raw response
	fmt.Printf("Raw response: %s\n", response.Raw)

	// Parse response
	edges := response.Get("data.shows.edges").Array()
	fmt.Printf("Found %d results\n", len(edges))

	var results []SearchResult
	for _, edge := range edges {
		id := edge.Get("_id").String()
		title := edge.Get("name").String()
		
		// Get episodes count for the selected translation type
		episodes := edge.Get(fmt.Sprintf("availableEpisodes.%s", translationType)).Int()
		if episodes > 0 {
			// Only add results that have episodes for the selected translation type
			fmt.Printf("Found anime: ID=%s, Title=%s, Episodes=%d\n", id, title, episodes)
			results = append(results, SearchResult{
				ID:       id,
				Title:    title,
				Episodes: int(episodes),
			})
		}
	}

	// Debug the final results
	fmt.Printf("Final results count: %d\n", len(results))
	for i, result := range results {
		fmt.Printf("Result %d: ID=%s, Title=%s, Episodes=%d\n", i+1, result.ID, result.Title, result.Episodes)
	}

	return results, nil
}

func (c *Client) GetEpisodes(showID string) ([]EpisodeDetail, error) {
	fmt.Printf("Getting episodes for show ID: %s\n", showID)

	// Create variables
	variables := map[string]interface{}{
		"showId": showID,
	}

	response, err := c.apiRequest("/api", episodesQuery, variables)
	if err != nil {
		fmt.Printf("Error getting episodes: %v\n", err)
		return nil, fmt.Errorf("error getting episodes: %v", err)
	}

	// Parse response
	show := response.Get("data.show").Map()
	if show == nil {
		fmt.Printf("No show found for ID: %s\n", showID)
		return nil, fmt.Errorf("no show found")
	}

	// Get episode details
	episodes := show["availableEpisodesDetail"].Map()
	var episodeDetails []EpisodeDetail
	for episodeType, episodesList := range episodes {
		// Convert gjson.Result to []string
		episodeStrings := []string{}
		for _, episode := range episodesList.Array() {
			episodeStrings = append(episodeStrings, episode.String())
		}
		
		// Create an episode detail for each episode number
		for _, episodeNum := range episodeStrings {
			episodeDetails = append(episodeDetails, EpisodeDetail{
				EpisodeNumber: episodeNum,
				AvailableEpisodes: map[string][]string{
					episodeType: []string{episodeNum},
				},
			})
		}
	}

	return episodeDetails, nil
}

// GetEpisodeURL retrieves the URL for a specific episode
func (c *Client) GetEpisodeURL(showID, translationType, episodeString string) ([]EpisodeSource, error) {
	fmt.Printf("Getting URL for episode %s of show %s\n", episodeString, showID)

	// Create variables
	variables := map[string]interface{}{
		"showId": showID,
		"translationType": translationType,
		"episodeString": episodeString,
	}

	response, err := c.apiRequest("/api", episodeEmbedQuery, variables)
	if err != nil {
		fmt.Printf("Error getting episode URL: %v\n", err)
		return nil, fmt.Errorf("error getting episode URL: %v", err)
	}

	// Parse response
	episode := response.Get("data.episode").Map()
	if episode == nil {
		fmt.Printf("No episode found for %s\n", episodeString)
		return nil, fmt.Errorf("no episode found")
	}

	// Get source URLs
	sourceURLs := episode["sourceUrls"].Array()
	var sources []EpisodeSource
	for _, url := range sourceURLs {
		source := url.Map()
		if source == nil {
			continue
		}
		
		// Extract URL and type
		sourceURL := source["sourceUrl"].String()
		priority := source["priority"].Float()
		sourceType := source["sourceName"].String()
		
		if sourceURL != "" && sourceType != "" {
			sources = append(sources, EpisodeSource{
				URL:      sourceURL,
				Priority: priority,
				Type:     sourceType,
			})
		}
	}

	return sources, nil
}

func (c *Client) GetEpisodesList(showID string) ([]EpisodeSource, error) {
	variables := map[string]interface{}{
		"showId": showID,
	}

	response, err := c.apiRequest("/api", episodeListQuery, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to get episode list: %v", err)
	}

	show := response.Get("data.show").Value().(map[string]interface{})
	if show == nil {
		return nil, fmt.Errorf("show not found")
	}

	availableEpisodes := show["availableEpisodesDetail"].([]interface{})
	var episodes []EpisodeSource
	for _, ep := range availableEpisodes {
		e := ep.(map[string]interface{})
		episodeString := e["episodeString"].(string)
		episodes = append(episodes, EpisodeSource{
			URL:      "",
			Type:     "",
			Language: "",
			Quality:  episodeString,
		})
	}

	return episodes, nil
}

func (c *Client) GetEpisodeSources(showID string, episodeString string, translationType string) ([]EpisodeSource, error) {
	variables := map[string]interface{}{
		"showId":          showID,
		"translationType": translationType,
		"episodeString":   episodeString,
	}

	fmt.Printf("Getting sources for episode: %s - %s\n", showID, episodeString)
	response, err := c.apiRequest("/api", episodeSourcesQuery, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to get episode sources: %v", err)
	}

	episode := response.Get("data.episode").Value().(map[string]interface{})
	if episode == nil {
		return nil, fmt.Errorf("episode not found")
	}

	sourceURLs := episode["sourceUrls"].([]interface{})
	var sources []EpisodeSource
	for _, url := range sourceURLs {
		s := url.(map[string]interface{})
		sources = append(sources, EpisodeSource{
			URL:      s["url"].(string),
			Type:     s["type"].(string),
			Language: s["language"].(string),
			Quality:  s["quality"].(string),
		})
	}

	return sources, nil
}

func (c *Client) GetEpisodeSourcesDetailed(showID string, episodeString string, translationType string) ([]EpisodeSource, error) {
	variables := map[string]interface{}{
		"showId":          showID,
		"translationType": translationType,
		"episodeString":   episodeString,
	}

	fmt.Printf("Getting sources for episode: %s - %s\n", showID, episodeString)
	response, err := c.apiRequest("/api", episodeSourcesQuery, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to get episode sources: %v", err)
	}

	episode := response.Get("data.episode").Value().(map[string]interface{})
	if episode == nil {
		return nil, fmt.Errorf("episode not found")
	}

	sourceURLs := episode["sourceUrls"].([]interface{})
	var sources []EpisodeSource
	for _, url := range sourceURLs {
		s := url.(map[string]interface{})
		sourceUrl := s["url"].(string)
		sourceType := s["type"].(string)
		quality := s["quality"].(string)

		// Handle YouTube URLs (start with --)
		if strings.HasPrefix(sourceUrl, "--") {
			parts := strings.Split(sourceUrl, "--")
			if len(parts) != 2 {
				continue
			}

			// Decode using the specific mapping from the bash script
			decoded := strings.ReplaceAll(parts[1], "01", "9")
			decoded = strings.ReplaceAll(decoded, "08", "0")
			decoded = strings.ReplaceAll(decoded, "05", "=")
			decoded = strings.ReplaceAll(decoded, "0a", "2")
			decoded = strings.ReplaceAll(decoded, "0b", "3")
			decoded = strings.ReplaceAll(decoded, "0c", "4")
			decoded = strings.ReplaceAll(decoded, "07", "?")
			decoded = strings.ReplaceAll(decoded, "00", "8")
			decoded = strings.ReplaceAll(decoded, "5c", "d")
			decoded = strings.ReplaceAll(decoded, "0f", "7")
			decoded = strings.ReplaceAll(decoded, "5e", "f")
			decoded = strings.ReplaceAll(decoded, "17", "/")
			decoded = strings.ReplaceAll(decoded, "54", "l")
			decoded = strings.ReplaceAll(decoded, "09", "1")
			decoded = strings.ReplaceAll(decoded, "48", "p")
			decoded = strings.ReplaceAll(decoded, "4f", "w")
			decoded = strings.ReplaceAll(decoded, "0e", "6")
			decoded = strings.ReplaceAll(decoded, "5b", "c")
			decoded = strings.ReplaceAll(decoded, "5d", "e")
			decoded = strings.ReplaceAll(decoded, "0d", "5")
			decoded = strings.ReplaceAll(decoded, "53", "k")
			decoded = strings.ReplaceAll(decoded, "1e", "&")
			decoded = strings.ReplaceAll(decoded, "5a", "b")
			decoded = strings.ReplaceAll(decoded, "59", "a")
			decoded = strings.ReplaceAll(decoded, "4a", "r")
			decoded = strings.ReplaceAll(decoded, "4c", "t")
			decoded = strings.ReplaceAll(decoded, "4e", "v")
			decoded = strings.ReplaceAll(decoded, "57", "o")
			decoded = strings.ReplaceAll(decoded, "51", "i")

			// Add /clock.json to the end
			sourceUrl = decoded + "/clock.json"
			sourceType = "youtube"
			quality = "1080p" // YouTube URLs are typically 1080p
		}

		// Handle data: URLs
		if strings.HasPrefix(sourceUrl, "data:") {
			parts := strings.Split(sourceUrl, ",")
			if len(parts) != 2 {
				continue
			}
			decoded, err := base64.StdEncoding.DecodeString(parts[1])
			if err != nil {
				continue
			}
			sourceUrl = string(decoded)
		}

		// Handle special URLs
		if strings.Contains(sourceUrl, "repackager.wixmp.com") {
			// Extract base URL and quality from wixmp URL
			baseURL := strings.Split(sourceUrl, ",")[0]
			qualityParts := strings.Split(sourceUrl, "/")
			for _, part := range qualityParts {
				if strings.HasSuffix(part, "p") {
					quality = part
					break
				}
			}
			sourceUrl = baseURL
			sourceType = "mp4"
		} else if strings.Contains(sourceUrl, "vipanicdn") || strings.Contains(sourceUrl, "anifastcdn") {
			if strings.Contains(sourceUrl, "original.m3u") {
				// Keep original m3u8 URL
				sourceType = "hls"
			} else {
				// Extract base URL and quality from m3u8 URL
				baseURL := sourceUrl[:strings.LastIndex(sourceUrl, "/")+1]
				quality = strings.TrimSuffix(sourceUrl[strings.LastIndex(sourceUrl, "/")+1:], ".m3u8")
				sourceUrl = baseURL + quality + ".m3u8"
				sourceType = "hls"
			}
		}

		// Try to extract quality from source name or URL
		if strings.Contains(sourceUrl, "1080") {
			quality = "1080p"
		} else if strings.Contains(sourceUrl, "720") {
			quality = "720p"
		} else if strings.Contains(sourceUrl, "480") {
			quality = "480p"
		} else {
			quality = "1080p" // Default to high quality if not specified
		}

		// Clean up source type
		switch {
		case strings.Contains(sourceUrl, ".m3u8"):
			sourceType = "hls"
		case strings.Contains(sourceUrl, ".mp4"):
			sourceType = "mp4"
		case sourceType == "":
			sourceType = "iframe" // Default to iframe if type not specified
		}

		// Add source to list
		sources = append(sources, EpisodeSource{
			URL:      sourceUrl,
			Type:     sourceType,
			Language: translationType,
			Quality:  quality,
		})
	}

	return sources, nil
}
