package ui

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"github.com/koki-develop/go-fzf"
)

type MenuProvider interface {
	Show(items []string, prompt string) (string, error)
	ShowMulti(items []string, prompt string) ([]string, error)
}

type NativeMenu struct{}

func (n *NativeMenu) Show(items []string, prompt string) (string, error) {
	var (
		output []byte
		err    error
		text   string
		input  string
		index  int
		f      *fzf.FZF
		selected []int
		scanner *bufio.Scanner
	)

	// If items is nil, just return empty string
	if items == nil {
		scanner = bufio.NewScanner(os.Stdin)
		fmt.Println(prompt)
		scanner.Scan()
		input = scanner.Text()
		return input, nil
	}

	// First try to use system fzf if available
	if _, err = exec.LookPath("fzf"); err == nil {
		cmd := exec.Command("fzf", "--prompt", prompt+"> ", "--no-multi")
		cmd.Stdin = strings.NewReader(strings.Join(items, "\n"))
		output, err = cmd.Output()
		if err != nil {
			return "", err
		}
		text = strings.TrimSpace(string(output))
		if text == "" {
			return "", nil
		}
		return text, nil
	}

	// If system fzf is not available, try go-fzf
	if len(items) > 0 {
		f, err = fzf.New(
			fzf.WithPrompt(prompt + "> "),
			fzf.WithNoLimit(false), // single select
		)
		if err != nil {
			return "", err
		}

		selected, err = f.Find(items, func(i int) string { return items[i] })
		if err != nil {
			return "", err
		}

		if len(selected) > 0 {
			return items[selected[0]], nil
		}
		return "", nil
	}

	// Fallback to native menu if both fzf methods fail
	scanner = bufio.NewScanner(os.Stdin)
	fmt.Println(prompt)
	for i, item := range items {
		fmt.Printf("%d. %s\n", i+1, item)
	}
	fmt.Print("Select number: ")
	scanner.Scan()
	input = scanner.Text()
	if input == "" {
		return "", nil
	}
	
	index, err = strconv.Atoi(input)
	if err != nil || index < 1 || index > len(items) {
		return "", fmt.Errorf("invalid selection")
	}
	return items[index-1], nil
}

func (n *NativeMenu) ShowMulti(items []string, prompt string) ([]string, error) {
	// First try to use system fzf
	if _, err := exec.LookPath("fzf"); err == nil {
		cmd := exec.Command("fzf", "--prompt", prompt+"> ", "--multi")
		cmd.Stdin = strings.NewReader(strings.Join(items, "\n"))
		output, err := cmd.Output()
		if err != nil {
			return nil, err
		}
		return strings.Split(strings.TrimSpace(string(output)), "\n"), nil
	}

	// If system fzf is not available, use go-fzf
	if len(items) > 0 {
		f, err := fzf.New(
			fzf.WithPrompt(prompt + "> "),
			fzf.WithNoLimit(true), // allow multiple selection
		)
		if err != nil {
			return nil, err
		}

		selected, err := f.Find(items, func(i int) string { return items[i] })
		if err != nil {
			return nil, err
		}

		result := make([]string, len(selected))
		for i, idx := range selected {
			result[i] = items[idx]
		}
		return result, nil
	}

	// Fallback to native menu if both fzf methods fail
	scanner := bufio.NewScanner(os.Stdin)
	fmt.Println(prompt)
	fmt.Println("Select numbers (comma-separated):")
	for i, item := range items {
		fmt.Printf("%d. %s\n", i+1, item)
	}
	fmt.Print("Select numbers: ")
	scanner.Scan()
	input := scanner.Text()
	if input == "" {
		return nil, nil
	}

	indices := strings.Split(input, ",")
	selected := make([]string, 0, len(indices))
	for _, indexStr := range indices {
		index, err := strconv.Atoi(strings.TrimSpace(indexStr))
		if err != nil || index < 1 || index > len(items) {
			continue
		}
		selected = append(selected, items[index-1])
	}
	return selected, nil
}

func NewMenu(useRofi bool) MenuProvider {
	return &NativeMenu{}
}
