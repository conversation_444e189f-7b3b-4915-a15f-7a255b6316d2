package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/koki-develop/go-fzf"
	"github.com/stl3/go-ani/api"
	"github.com/stl3/go-ani/database"
	"github.com/stl3/go-ani/ui"
)

func main() {
	// Check for retro UI flag
	for _, arg := range os.Args[1:] {
		if arg == "--retro" || arg == "-retro" {
			if err := ui.RunRetroUI(); err != nil {
				fmt.Printf("Error running retro UI: %v\n", err)
			}
			return
		}
	}

	// Parse command line arguments with flexible parsing
	var err error
	var quality, query string
	var mode string = "sub" // Default to lowercase sub
	var download bool
	var episode string
	var continueWatch bool
	var deleteHistory bool
	var skipIntro bool
	var skipTitle string
	var noDstach bool
	var ekipIntro bool
	var skipTitle string
	var noDetach bool
	var episodeNum int
	var cmdEpisodeNum string

	// Parse arguments manually to allow flags anywhere
	args := os.Args[1:]
	var nonFlagArgs []string

	for i := 0; i < len(args); i++ {
		arg := args[i]
		switch {
		case arg == "-download" || arg == "--download":
			download = true
		case arg == "-episode" || arg == "--episode" || arg == "-e":
			if i+1 < len(args) {
				episode = args[i+1]
				i++ // Skip next arg as it's the value
			}
		case arg == "-quality" || arg == "--quality" || arg == "-q":
			if i+1 < len(args) {
				quality = args[i+1]
				i++ // Skip next arg as it's the value
			}
		case arg == "-mode" || arg == "--mode":
			if i+1 < len(args) {
				mode = args[i+1]
				i++ // Skip next arg as it's the value
			}
		case arg == "-continue" || arg == "--continue" || arg == "-c":
			continueWatch = true
		case arg == "-delete" || arg == "--delete":
			deleteHistory = true
		case arg == "-wipe" || arg == "--wipe":
			// Handle wipe flag - will be processed later
			// Set a flag to handle this after DB initialization
			args = append(args[:i], args[i+1:]...) // Remove this arg
			i--                                    // Adjust index
			// We'll handle wipe after DB init
		case arg == "-version" || arg == "--version" || arg == "-V":
			fmt.Println("go-ani version 1.0.0")
			return
		case arg == "-help" || arg == "--help" || arg == "-h":
			showHelp()
			return
		case strings.HasPrefix(arg, "-"):
			// Unknown flag, ignore or could show warning
			fmt.Printf("Warning: Unknown flag %s\n", arg)
		default:
			// Non-flag argument (anime name)
			nonFlagArgs = append(nonFlagArgs, arg)
		}
	}

	// Set defaults
	if quality == "" {
		quality = "1080p"
	}

	// Combine non-flag arguments into query
	if len(nonFlagArgs) > 0 {
		query = strings.Join(nonFlagArgs, " ")
	}

	// Initialize database
	homeDir, err := os.UserHomeDir()
	if err != nil {
		fmt.Printf("Error getting home directory: %v\n", err)
		return
	}
	dataDir := filepath.Join(homeDir, ".go-ani")
	db, err := database.NewDB(dataDir)
	if err != nil {
		fmt.Printf("Error initializing database: %v\n", err)
		return
	}
	defer db.Close()

	// Handle delete flag
	if deleteHistory {
		handleDeleteHistory(db)
		return
	}

	// Handle wipe flag (check if --wipe was in original args)
	for _, arg := range os.Args[1:] {
		if arg == "-wipe" || arg == "--wipe" {
			handleWipeHistory(db)
			return
		}
	}

	// Set download directory if download flag is set
	var downloadDir string
	if download {
		downloadDir = "."
		if envDir := os.Getenv("ANI_CLI_DOWNLOAD_DIR"); envDir != "" {
			downloadDir = envDir
		}
		// For downloads, we'll handle episode processing differently
	}

	// Handle continue flag
	if continueWatch {
		handleContinueWatch(db)
		return
	}

	// Validate required arguments
	if query == "" {
		fmt.Println("Error: Query is required")
		flag.Usage()
		return
	}

	// Handle spaces in query
	query = strings.TrimSpace(query)
	if query == "" {
		fmt.Println("Error: Query cannot be empty")
		flag.Usage()
		return
	}

	// Parse episode number or range
	var episodeList []int
	if episode != "" {
		episodeList, err = parseEpisodeRange(episode)
		if err != nil {
			fmt.Printf("Error: Invalid episode range: %v\n", err)
			flag.Usage()
			return
		}
		episodeNum = episodeList[0] // Use first episode for initial selection
		cmdEpisodeNum = strconv.Itoa(episodeNum)
	} else {
		episodeNum = 1 // Default to episode 1
		cmdEpisodeNum = "1"
		episodeList = []int{1}
	}

	// Validate translation type
	mode = strings.ToLower(mode)
	if mode != "sub" && mode != "dub" {
		fmt.Printf("Error: Invalid translation type: %s. Must be either 'sub' or 'dub'\n", mode)
		flag.Usage()
		return
	}

	// Create API client
	client := api.NewClient()

	// Search anime
	results, err := client.SearchAnime(query, mode)
	if err != nil {
		fmt.Printf("Error searching for anime: %v\n", err)
		return
	}

	// Display results
	if len(results) == 0 {
		fmt.Println("No results found")
		return
	}

	// Format results for fzf
	var fzfOptions []string
	for _, result := range results {
		fzfOptions = append(fzfOptions, fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes))
	}

	// Auto-select if only one result, otherwise show selection menu
	var selectedAnime api.SearchResult
	if len(results) == 1 {
		selectedAnime = results[0]
		fmt.Printf("Auto-selected: %s\n", selectedAnime.Title)
	} else {
		// Select anime using fzf
		selectedIdx, err := selectWithFzf(fzfOptions, "Select anime")
		if err != nil {
			fmt.Printf("Error selecting anime: %v\n", err)
			return
		}
		if selectedIdx == -1 {
			fmt.Println("No selection made")
			return
		}
		selected := fzfOptions[selectedIdx]

		// Find the selected anime in our results
		selectedAnime = results[0] // Default to first result
		for _, result := range results {
			formatted := fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes)
			if formatted == selected {
				selectedAnime = result
				break
			}
		}
		fmt.Printf("Selected: %s\n", selectedAnime.Title)
	}

	// Declare episodeOptions for potential use in menu loop
	var episodeOptions []string

	// Only get episodes and show selection if no episode was specified from command line
	if episode == "" {
		// Get episodes for the selected anime
		episodes, err := client.GetEpisodes(selectedAnime.ID)
		if err != nil {
			fmt.Printf("Error getting episodes: %v\n", err)
			return
		}

		// Create fzf options
		for _, ep := range episodes {
			subs := ep.AvailableEpisodes["sub"]
			dubs := ep.AvailableEpisodes["dub"]
			subsCount := len(subs)
			dubsCount := len(dubs)

			episodeOptions = append(episodeOptions, fmt.Sprintf("Episode %s (Subs: %d, Dubs: %d)", ep.EpisodeNumber, subsCount, dubsCount))
		}

		// Display available episodes
		fmt.Println("\nAvailable episodes:")
		for _, option := range episodeOptions {
			fmt.Println(option)
		}

		// Select episode using fzf
		episodeIdx, err := selectWithFzf(episodeOptions, "Select episode")
		if err != nil {
			fmt.Printf("Error selecting episode: %v\n", err)
			return
		}
		if episodeIdx == -1 {
			fmt.Println("No episode selected")
			return
		}
		selectedEpisode := episodeOptions[episodeIdx]

		// Extract episode number and mode from the selected string
		parts := strings.Split(selectedEpisode, " ")
		episodeNum, err = strconv.Atoi(parts[1])
		if err != nil {
			fmt.Printf("Error: Invalid episode number: %v\n", err)
			return
		}

		// Determine mode based on subs/dubs count
		if strings.Contains(selectedEpisode, "Subs: 1, Dubs: 0") {
			mode = "sub"
		} else if strings.Contains(selectedEpisode, "Subs: 0, Dubs: 1") {
			mode = "dub"
		}

		cmdEpisodeNum = strconv.Itoa(episodeNum)
		// Update episodeList for downloads
		episodeList = []int{episodeNum}
	} else {
		fmt.Printf("Using specified episode(s): %v\n", episodeList)
	}
	// If episode was specified from command line, episodeNum and episodeList are already set

	// Get episode URLs
	sources, err := client.GetEpisodeURL(selectedAnime.ID, mode, cmdEpisodeNum)
	if err != nil {
		fmt.Printf("Error getting episode sources: %v\n", err)
		return
	}

	// Display available sources
	fmt.Println("\nAvailable sources:")
	for _, source := range sources {
		fmt.Printf("Source: %s, Priority: %.1f, Type: %s\n", source.URL, source.Priority, source.Type)
	}

	// Find the best source - prioritize simpler sources over complex DASH streams
	var bestSource *api.EpisodeSource

	// First, try to find Yt-mp4 source (most reliable)
	for _, source := range sources {
		if source.Type == "Yt-mp4" {
			bestSource = &source
			break
		}
	}

	// If no Yt-mp4, look for other simple sources (updated provider names)
	if bestSource == nil {
		for _, source := range sources {
			if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
				bestSource = &source
				break
			}
		}
	}

	// If still no source, fall back to highest priority
	if bestSource == nil {
		for _, source := range sources {
			if bestSource == nil || source.Priority > bestSource.Priority {
				bestSource = &source
			}
		}
	}

	if bestSource == nil {
		fmt.Println("No suitable sources found or all attempts to play failed for the selected episode and quality.")
		return
	}

	// Play the best source
	fmt.Printf("Playing episode %d with source: %s\n", episodeNum, bestSource.Type)
	fmt.Printf("URL: %s\n", bestSource.URL)

	// Play using mpv with proper arguments
	streamDetails, err := extractStreamInfo(bestSource, quality) // Pass the whole bestSource struct
	if err != nil {
		fmt.Printf("Error extracting stream info for source type %s (URL: %s): %v\n", bestSource.Type, bestSource.URL, err) // Use .Type and .URL
		return
	}

	fmt.Printf("Successfully extracted stream details: %+v\n", streamDetails)
	if download {
		// Download all episodes in the range
		for _, epNum := range episodeList {
			fmt.Printf("\nDownloading episode %d of %d...\n", epNum, len(episodeList))

			// Get sources for this episode
			sources, err := client.GetEpisodeURL(selectedAnime.ID, mode, strconv.Itoa(epNum))
			if err != nil {
				fmt.Printf("Error getting episode %d sources: %v\n", epNum, err)
				continue
			}

			// Find best source
			var epBestSource *api.EpisodeSource
			for _, source := range sources {
				if source.Type == "Yt-mp4" {
					epBestSource = &source
					break
				}
			}
			if epBestSource == nil {
				for _, source := range sources {
					if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
						epBestSource = &source
						break
					}
				}
			}
			if epBestSource == nil {
				for _, source := range sources {
					if epBestSource == nil || source.Priority > epBestSource.Priority {
						epBestSource = &source
					}
				}
			}

			if epBestSource == nil {
				fmt.Printf("No suitable sources found for episode %d\n", epNum)
				continue
			}

			// Extract stream info and download
			epStreamDetails, err := extractStreamInfo(epBestSource, quality)
			if err != nil {
				fmt.Printf("Error extracting stream info for episode %d: %v\n", epNum, err)
				continue
			}

			downloadEpisode(epStreamDetails, selectedAnime.Title, epNum, downloadDir)
		}
		fmt.Println("\nAll downloads completed!")
		return
	} else {
		playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)
	}

	// Update history after playing
	historyEntry := database.HistoryEntry{
		AnimeID:     selectedAnime.ID,
		Title:       selectedAnime.Title,
		CurrentEp:   episodeNum,
		TotalEps:    selectedAnime.Episodes,
		WatchedEps:  []int{episodeNum},
		LastWatched: time.Now(),
		Quality:     quality,
		Mode:        mode,
	}
	if err := db.AddOrUpdateEntry(historyEntry); err != nil {
		fmt.Printf("Warning: Failed to update history: %v\n", err)
	}

	// Post-playback menu loop (similar to original ani-cli)
	for {
		menuOptions := []string{
			"next",
			"replay",
			"previous",
			"select",
			"history",
			"change_quality",
			"search_new",
			"quit",
		}

		// Select menu option using fzf
		menuIdx, err := selectWithFzf(menuOptions, fmt.Sprintf("Playing episode %d of %s... ", episodeNum, selectedAnime.Title))
		if err != nil {
			fmt.Printf("Error using fzf for menu: %v\n", err)
			break
		}
		if menuIdx == -1 {
			break
		}
		selectedOption := menuOptions[menuIdx]

		switch selectedOption {
		case "next":
			// Find next episode
			nextEpisode := episodeNum + 1
			if nextEpisode <= selectedAnime.Episodes {
				episodeNum = nextEpisode
				cmdEpisodeNum = strconv.Itoa(episodeNum)

				// Get sources for next episode
				sources, err := client.GetEpisodeURL(selectedAnime.ID, mode, cmdEpisodeNum)
				if err != nil {
					fmt.Printf("Error getting next episode sources: %v\n", err)
					continue
				}

				// Find best source for next episode using same logic
				var nextBestSource *api.EpisodeSource
				for _, source := range sources {
					if source.Type == "Yt-mp4" {
						nextBestSource = &source
						break
					}
				}
				if nextBestSource == nil {
					for _, source := range sources {
						if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
							nextBestSource = &source
							break
						}
					}
				}
				if nextBestSource == nil {
					for _, source := range sources {
						if nextBestSource == nil || source.Priority > nextBestSource.Priority {
							nextBestSource = &source
						}
					}
				}

				if nextBestSource != nil {
					streamDetails, err = extractStreamInfo(nextBestSource, quality)
					if err != nil {
						fmt.Printf("Error extracting stream info for next episode: %v\n", err)
						continue
					}
					bestSource = nextBestSource
					playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

					// Update history after playing next episode
					if err := db.MarkEpisodeWatched(selectedAnime.ID, episodeNum); err != nil {
						fmt.Printf("Warning: Failed to update history: %v\n", err)
					}
				}
			} else {
				fmt.Println("Already at the last episode")
			}

		case "replay":
			// Replay current episode
			playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

		case "previous":
			// Find previous episode
			prevEpisode := episodeNum - 1
			if prevEpisode >= 1 {
				episodeNum = prevEpisode
				cmdEpisodeNum = strconv.Itoa(episodeNum)

				// Get sources for previous episode
				sources, err := client.GetEpisodeURL(selectedAnime.ID, mode, cmdEpisodeNum)
				if err != nil {
					fmt.Printf("Error getting previous episode sources: %v\n", err)
					continue
				}

				// Find best source for previous episode using same logic
				var prevBestSource *api.EpisodeSource
				for _, source := range sources {
					if source.Type == "Yt-mp4" {
						prevBestSource = &source
						break
					}
				}
				if prevBestSource == nil {
					for _, source := range sources {
						if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
							prevBestSource = &source
							break
						}
					}
				}
				if prevBestSource == nil {
					for _, source := range sources {
						if prevBestSource == nil || source.Priority > prevBestSource.Priority {
							prevBestSource = &source
						}
					}
				}

				if prevBestSource != nil {
					streamDetails, err = extractStreamInfo(prevBestSource, quality)
					if err != nil {
						fmt.Printf("Error extracting stream info for previous episode: %v\n", err)
						continue
					}
					bestSource = prevBestSource
					playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

					// Update history after playing previous episode
					if err := db.MarkEpisodeWatched(selectedAnime.ID, episodeNum); err != nil {
						fmt.Printf("Warning: Failed to update history: %v\n", err)
					}
				}
			} else {
				fmt.Println("Already at the first episode")
			}

		case "select":
			// Check if episodeOptions is empty
			if len(episodeOptions) == 0 {
				fmt.Println("No episodes available for selection")
				continue
			}

			// Re-show episode selection
			newEpisodeIdx, err := selectWithFzf(episodeOptions, "Select episode")
			if err != nil {
				fmt.Printf("Error using fzf for episode selection: %v\n", err)
				continue
			}
			if newEpisodeIdx == -1 {
				continue
			}

			newSelectedEpisode := episodeOptions[newEpisodeIdx]

			// Extract episode number and mode from the selected string
			parts := strings.Split(newSelectedEpisode, " ")
			newEpisodeNum, err := strconv.Atoi(parts[1])
			if err != nil {
				fmt.Printf("Error parsing episode number: %v\n", err)
				continue
			}

			// Determine mode based on subs/dubs count
			var selectedMode string
			if strings.Contains(newSelectedEpisode, "Subs: 1, Dubs: 0") {
				selectedMode = "sub"
			} else if strings.Contains(newSelectedEpisode, "Subs: 0, Dubs: 1") {
				selectedMode = "dub"
			} else {
				selectedMode = mode // fallback to original mode
			}

			episodeNum = newEpisodeNum
			cmdEpisodeNum = strconv.Itoa(episodeNum)

			// Get sources for selected episode
			sources, err := client.GetEpisodeURL(selectedAnime.ID, selectedMode, cmdEpisodeNum)
			if err != nil {
				fmt.Printf("Error getting selected episode sources: %v\n", err)
				continue
			}

			// Find best source for selected episode using same logic
			var selectedBestSource *api.EpisodeSource
			for _, source := range sources {
				if source.Type == "Yt-mp4" {
					selectedBestSource = &source
					break
				}
			}
			if selectedBestSource == nil {
				for _, source := range sources {
					if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
						selectedBestSource = &source
						break
					}
				}
			}
			if selectedBestSource == nil {
				for _, source := range sources {
					if selectedBestSource == nil || source.Priority > selectedBestSource.Priority {
						selectedBestSource = &source
					}
				}
			}

			if selectedBestSource != nil {
				streamDetails, err = extractStreamInfo(selectedBestSource, quality)
				if err != nil {
					fmt.Printf("Error extracting stream info for selected episode: %v\n", err)
					continue
				}
				bestSource = selectedBestSource
				playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

				// Update history after playing selected episode
				if err := db.MarkEpisodeWatched(selectedAnime.ID, episodeNum); err != nil {
					fmt.Printf("Warning: Failed to update history: %v\n", err)
				}
			}

		case "history":
			// Show watch history
			handleHistoryMenu(db)

		case "change_quality":
			// Show quality options
			qualityOptions := []string{"1080p", "720p", "480p", "360p", "best", "worst"}
			qualityIdx, err := selectWithFzf(qualityOptions, "Select quality")
			if err != nil {
				fmt.Printf("Error using fzf for quality selection: %v\n", err)
				continue
			}
			if qualityIdx != -1 {
				newQuality := qualityOptions[qualityIdx]
				quality = newQuality
				fmt.Printf("Quality changed to: %s\n", quality)

				// Re-extract stream with new quality
				streamDetails, err = extractStreamInfo(bestSource, quality)
				if err != nil {
					fmt.Printf("Error extracting stream info with new quality: %v\n", err)
					continue
				}
				playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)
			}

		case "search_new":
			// Search for new anime
			fmt.Print("\033[1;36mEnter search term: \033[0m")
			reader := bufio.NewReader(os.Stdin)
			newQuery, err := reader.ReadString('\n')
			if err != nil {
				fmt.Printf("Error reading input: %v\n", err)
				continue
			}
			newQuery = strings.TrimSpace(newQuery)

			if newQuery == "" {
				fmt.Println("Search term cannot be empty")
				continue
			}

			// Search for new anime
			newResults, err := client.SearchAnime(newQuery, mode)
			if err != nil {
				fmt.Printf("Error searching for anime: %v\n", err)
				continue
			}

			if len(newResults) == 0 {
				fmt.Println("No results found")
				continue
			}

			// Format results for fzf
			var newFzfOptions []string
			for _, result := range newResults {
				newFzfOptions = append(newFzfOptions, fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes))
			}

			// Use fzf to select new anime
			newAnimeIdx, err := selectWithFzf(newFzfOptions, "Select anime")
			if err != nil {
				fmt.Printf("Error using fzf: %v\n", err)
				continue
			}
			if newAnimeIdx == -1 {
				continue
			}
			newSelected := newFzfOptions[newAnimeIdx]

			// Find the selected anime
			for _, result := range newResults {
				formatted := fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes)
				if formatted == newSelected {
					selectedAnime = result
					break
				}
			}

			// Get episodes for new anime
			episodes, err := client.GetEpisodes(selectedAnime.ID)
			if err != nil {
				fmt.Printf("Error getting episodes: %v\n", err)
				continue
			}

			// Update episode options
			var newEpisodeOptions []string
			for _, ep := range episodes {
				subs := ep.AvailableEpisodes["sub"]
				dubs := ep.AvailableEpisodes["dub"]
				subsCount := len(subs)
				dubsCount := len(dubs)
				newEpisodeOptions = append(newEpisodeOptions, fmt.Sprintf("Episode %s (Subs: %d, Dubs: %d)", ep.EpisodeNumber, subsCount, dubsCount))
			}

			// Select episode from new anime
			newEpisodeIdx, err := selectWithFzf(newEpisodeOptions, "Select episode")
			if err != nil {
				fmt.Printf("Error using fzf: %v\n", err)
				continue
			}
			if newEpisodeIdx == -1 {
				continue
			}
			newSelectedEpisode := newEpisodeOptions[newEpisodeIdx]

			// Extract episode number and mode
			parts := strings.Split(newSelectedEpisode, " ")
			episodeNum, err = strconv.Atoi(parts[1])
			if err != nil {
				fmt.Printf("Error parsing episode number: %v\n", err)
				continue
			}

			// Determine mode based on subs/dubs count
			var selectedMode string
			if strings.Contains(newSelectedEpisode, "Subs: 1, Dubs: 0") {
				selectedMode = "sub"
			} else if strings.Contains(newSelectedEpisode, "Subs: 0, Dubs: 1") {
				selectedMode = "dub"
			} else {
				selectedMode = mode // fallback to original mode
			}

			cmdEpisodeNum = strconv.Itoa(episodeNum)

			// Get sources for new episode
			sources, err := client.GetEpisodeURL(selectedAnime.ID, selectedMode, cmdEpisodeNum)
			if err != nil {
				fmt.Printf("Error getting episode sources: %v\n", err)
				continue
			}

			// Find best source using same logic
			var newBestSource *api.EpisodeSource
			for _, source := range sources {
				if source.Type == "Yt-mp4" {
					newBestSource = &source
					break
				}
			}
			if newBestSource == nil {
				for _, source := range sources {
					if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
						newBestSource = &source
						break
					}
				}
			}
			if newBestSource == nil {
				for _, source := range sources {
					if newBestSource == nil || source.Priority > newBestSource.Priority {
						newBestSource = &source
					}
				}
			}

			if newBestSource != nil {
				streamDetails, err = extractStreamInfo(newBestSource, quality)
				if err != nil {
					fmt.Printf("Error extracting stream info: %v\n", err)
					continue
				}
				bestSource = newBestSource
				playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

				// Update history after playing new anime episode
				historyEntry := database.HistoryEntry{
					AnimeID:     selectedAnime.ID,
					Title:       selectedAnime.Title,
					CurrentEp:   episodeNum,
					TotalEps:    selectedAnime.Episodes,
					WatchedEps:  []int{episodeNum},
					LastWatched: time.Now(),
					Quality:     quality,
					Mode:        mode,
				}
				if err := db.AddOrUpdateEntry(historyEntry); err != nil {
					fmt.Printf("Warning: Failed to update history: %v\n", err)
				}
			}

		case "quit":
			fallthrough
		default:
			return
		}
	}
}

func decodeAllAnimeURL(encodedURL string) string {
	// Remove the -- prefix
	encodedURL = strings.TrimPrefix(encodedURL, "--")

	// Create the decoding map
	decodeMap := map[string]string{
		"79": "A", "7a": "B", "7b": "C", "7c": "D", "7d": "E", "7e": "F", "7f": "G",
		"70": "H", "71": "I", "72": "J", "73": "K", "74": "L", "75": "M", "76": "N", "77": "O",
		"68": "P", "69": "Q", "6a": "R", "6b": "S", "6c": "T", "6d": "U", "6e": "V", "6f": "W",
		"60": "X", "61": "Y", "62": "Z", "59": "a", "5a": "b", "5b": "c", "5c": "d", "5d": "e",
		"5e": "f", "5f": "g", "50": "h", "51": "i", "52": "j", "53": "k", "54": "l", "55": "m",
		"56": "n", "57": "o", "48": "p", "49": "q", "4a": "r", "4b": "s", "4c": "t", "4d": "u",
		"4e": "v", "4f": "w", "40": "x", "41": "y", "42": "z", "08": "0", "09": "1", "0a": "2",
		"0b": "3", "0c": "4", "0d": "5", "0e": "6", "0f": "7", "00": "8", "01": "9", "15": "-",
		"16": ".", "67": "_", "46": "~", "02": ":", "17": "/", "07": "?", "1b": "#", "63": "[",
		"65": "]", "78": "@", "19": "!", "1c": "$", "1e": "&", "10": "(", "11": ")", "12": "*",
		"13": "+", "14": ",", "03": ";", "05": "=", "1d": "%",
	}

	// Decode using the mapping
	var decoded strings.Builder
	for i := 0; i < len(encodedURL); i += 2 {
		// Check if we have at least 2 characters left
		if i+2 > len(encodedURL) {
			// If not, just append the remaining characters
			decoded.WriteString(encodedURL[i:])
			break
		}
		chunk := encodedURL[i : i+2]
		if val, ok := decodeMap[chunk]; ok {
			decoded.WriteString(val)
		} else {
			decoded.WriteString(chunk)
		}
	}

	// Clean up any special characters that might cause issues
	decodedStr := decoded.String()
	decodedStr = strings.ReplaceAll(decodedStr, "\\", "")
	decodedStr = strings.ReplaceAll(decodedStr, "\u002F", "/")
	// Fix double slashes in URLs (except after protocol)
	if strings.Contains(decodedStr, "://") {
		parts := strings.SplitN(decodedStr, "://", 2)
		if len(parts) == 2 {
			// Clean up double slashes in the path part only
			parts[1] = strings.ReplaceAll(parts[1], "//", "/")
			decodedStr = parts[0] + "://" + parts[1]
		}
	}

	// Handle different providers with switch-case
	switch {
	case strings.Contains(decodedStr, "sharepoint"):
		// SharePoint provider - extract the URL after the colon
		if strings.Contains(decodedStr, ":") {
			return strings.Split(decodedStr, ":")[1]
		}
		return decodedStr
	case strings.Contains(decodedStr, "/clock"):
		// For Ak sources, replace /clock with /clock.json as per original ani-cli
		decodedStr = strings.Replace(decodedStr, "/clock", "/clock.json", 1)
		return decodedStr
	case strings.Contains(decodedStr, "repackager.wixmp.com"):
		// Wixmp provider - extract the actual URL
		extractLink := strings.Replace(decodedStr, "repackager.wixmp.com/", "", 1)
		extractLink = strings.TrimSuffix(extractLink, ".urlset")
		return extractLink
	case strings.Contains(decodedStr, "vipanicdn") || strings.Contains(decodedStr, "anifastcdn"):
		// VIPanicDN or AnifastCDN provider
		if strings.Contains(decodedStr, "original.m3u") {
			// If it's an m3u8 playlist, return as is
			return decodedStr
		}
		// Extract the base URL and relative paths
		if strings.Contains(decodedStr, ">") {
			extractLink := strings.Split(decodedStr, ">")[1]
			if strings.HasPrefix(extractLink, "/") {
				// If it's a relative path, get the base URL
				baseURL := strings.Split(decodedStr, ">")[0]
				baseURL = strings.TrimSuffix(baseURL, "/")
				return baseURL + extractLink
			}
			return extractLink
		}
		return decodedStr
	case strings.Contains(decodedStr, "youtube"):
		// YouTube provider - already decoded, return as is
		return decodedStr
	default:
		// For other providers, return the decoded URL as is
		return decodedStr
	}
}

// extractStreamInfo decodes the source URL, fetches necessary data, and returns StreamDetails
func extractStreamInfo(source *api.EpisodeSource, quality string) (StreamDetails, error) {
	details := StreamDetails{SourceType: source.Type}
	decodedURL := decodeAllAnimeURL(source.URL)

	if decodedURL == "" {
		return details, fmt.Errorf("failed to decode source URL: %s", source.URL)
	}

	details.VideoURL = decodedURL // Initial video URL, might be an intermediate one for Ak/m3u8

	switch source.Type {
	case "Yt-mp4":
		// For Yt-mp4, the decoded URL is the video URL.
		// Use allmanga.to as referrer (as per original ani-cli)
		details.Referrer = "https://allmanga.to"
		// VideoURL is already set to decodedURL

	case "Ak":
		// For Ak sources, we need to construct the full URL
		// The decoded URL should be a path like "/apivtwo/clock.json?id=..."
		var akURL string
		if strings.HasPrefix(decodedURL, "/") {
			// It's a relative path, construct full URL with correct domain
			akURL = "https://allanime.day" + decodedURL
		} else if strings.HasPrefix(decodedURL, "http") {
			// It's already a full URL
			akURL = decodedURL
		} else {
			// Assume it's a path without leading slash
			akURL = "https://allanime.day/" + decodedURL
		}

		fmt.Printf("Ak: Fetching stream data from: %s\n", akURL)

		// Create request with proper headers
		req, err := http.NewRequest("GET", akURL, nil)
		if err != nil {
			return details, fmt.Errorf("Ak: failed to create request: %v", err)
		}
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0")
		req.Header.Set("Referer", "https://allanime.to")
		req.Header.Set("Accept", "*/*")

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			return details, fmt.Errorf("Ak: failed to fetch stream data from %s: %v", akURL, err)
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return details, fmt.Errorf("Ak: failed to read response body from %s: %v", akURL, err)
		}

		fmt.Printf("Ak: Raw JSON response: %s\n", string(body))

		// Parse the JSON response
		var akData struct {
			Links []struct {
				Link      string `json:"link"`
				Dash      bool   `json:"dash"`
				Src       string `json:"src"`
				Subtitles []struct {
					Lang    string `json:"lang"`
					Default bool   `json:"default"`
					Src     string `json:"src"`
				} `json:"subtitles"`
				ResolutionStr string `json:"resolutionStr"`
				RawUrls       struct {
					Vids []struct {
						URL    string `json:"url"`
						Height int    `json:"height"`
					} `json:"vids"`
					Audios []struct {
						URL string `json:"url"`
					} `json:"audios"`
				} `json:"rawUrls"`
			} `json:"links"`
		}

		if err := json.Unmarshal(body, &akData); err != nil {
			return details, fmt.Errorf("Ak: failed to unmarshal JSON from %s: %v", akURL, err)
		}

		// Process all links and find the best one
		if len(akData.Links) > 0 {
			// Use the first available link (they're usually sorted by quality)
			bestLink := akData.Links[0]

			// For DASH streams or if we have rawUrls, extract the actual video URLs
			if bestLink.Dash && len(bestLink.RawUrls.Vids) > 0 {
				// Find the highest quality video stream
				var bestVid struct {
					URL    string `json:"url"`
					Height int    `json:"height"`
				}
				for _, vid := range bestLink.RawUrls.Vids {
					if vid.Height > bestVid.Height {
						bestVid = vid
					}
				}
				if bestVid.URL != "" {
					details.VideoURL = bestVid.URL
					fmt.Printf("Ak: Using extracted video URL: %s (height: %d)\n", bestVid.URL, bestVid.Height)
				}

				// Set audio URL if available
				if len(bestLink.RawUrls.Audios) > 0 {
					details.AudioURL = bestLink.RawUrls.Audios[0].URL
					fmt.Printf("Ak: Using audio URL: %s\n", details.AudioURL)
				}
			} else if bestLink.Link != "" {
				// Use the link directly if no rawUrls available
				details.VideoURL = bestLink.Link
				fmt.Printf("Ak: Using direct link: %s\n", bestLink.Link)
			} else if bestLink.Src != "" {
				// Fallback to src field
				details.VideoURL = bestLink.Src
				fmt.Printf("Ak: Using src URL: %s\n", bestLink.Src)
			}

			// Set subtitle URL if available
			for _, sub := range bestLink.Subtitles {
				if sub.Lang == "en" && sub.Default {
					details.SubtitleURL = sub.Src
					break
				}
			}

			fmt.Printf("Ak: Successfully extracted stream URL\n")
		} else {
			return details, fmt.Errorf("Ak: No links found in JSON response")
		}

		// Validate that we got a proper video URL
		if details.VideoURL == "" || details.VideoURL == decodedURL {
			return details, fmt.Errorf("Ak: Could not extract a valid video stream URL from JSON response at %s", akURL)
		}

		// Set referrer for Ak sources
		details.Referrer = "https://allanime.to"

	case "S-mp4", "Luf-Mp4":
		// For m3u8 sources, we need to fetch the playlist and parse it
		fmt.Printf("Fetching m3u8 playlist from: %s\n", decodedURL)

		// Add base domain if the URL is relative
		m3u8URL := decodedURL
		if !strings.HasPrefix(m3u8URL, "http") {
			m3u8URL = "https://blog.allanime.day" + m3u8URL
		}

		// Create a new request with headers to mimic a browser
		req, err := http.NewRequest("GET", m3u8URL, nil)
		if err != nil {
			return details, fmt.Errorf("failed to create request: %v", err)
		}

		// Add headers to mimic a browser
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
		req.Header.Set("Accept", "*/*")
		req.Header.Set("Referer", "https://allanime.day/")

		// Send the request
		client := &http.Client{
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// Allow redirects
				return nil
			},
		}

		resp, err := client.Do(req)
		if err != nil {
			return details, fmt.Errorf("failed to fetch m3u8 playlist from %s: %v", m3u8URL, err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return details, fmt.Errorf("received non-200 status code: %d", resp.StatusCode)
		}

		// Read the response body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return details, fmt.Errorf("failed to read response body: %v", err)
		}

		// Check if the response is HTML (which would indicate an error)
		contentType := resp.Header.Get("Content-Type")
		if strings.Contains(contentType, "text/html") {
			return details, fmt.Errorf("received HTML response instead of m3u8 playlist. The server might be blocking the request")
		}

		// Check if the response looks like a valid m3u8 playlist
		if !bytes.HasPrefix(body, []byte("#EXTM3U")) {
			return details, fmt.Errorf("invalid m3u8 playlist format")
		}

		// Parse the m3u8 content to extract the highest quality stream
		playlist := string(body)
		lines := strings.Split(playlist, "\n")

		// Look for the highest quality stream URL
		var streamURL string
		var maxBandwidth int
		for i, line := range lines {
			if strings.HasPrefix(line, "#EXT-X-STREAM-INF:") {
				// Extract bandwidth if available
				bandwidth := 0
				if strings.Contains(line, "BANDWIDTH=") {
					bwStr := strings.Split(strings.Split(line, "BANDWIDTH=")[1], ",")[0]
					bw, _ := strconv.Atoi(bwStr)
					bandwidth = bw
				}

				// The next line should be the stream URL
				if i+1 < len(lines) && !strings.HasPrefix(lines[i+1], "#") {
					streamURL = strings.TrimSpace(lines[i+1])
					if bandwidth > maxBandwidth {
						maxBandwidth = bandwidth
						details.VideoURL = streamURL
					}
				}
			}

			// Look for subtitles in the m3u8
			if strings.Contains(line, "EXT-X-MEDIA:TYPE=SUBTITLES") && strings.Contains(line, "LANGUAGE=\"en\"") {
				// Extract the URI parameter
				if strings.Contains(line, "URI=\"") {
					subURI := strings.Split(strings.Split(line, "URI=\"")[1], "\"")[0]
					// Handle relative URLs
					if !strings.HasPrefix(subURI, "http") {
						baseURL := strings.TrimSuffix(decodedURL, "/")
						subURI = baseURL + "/" + subURI
					}
					details.SubtitleURL = subURI
				}
			}

			// Look for the referrer in the m3u8 comments
			if strings.Contains(line, "#EXT-X-REFERER:") {
				details.Referrer = strings.TrimSpace(strings.TrimPrefix(line, "#EXT-X-REFERER:"))
			}
		}

		// If we didn't find a stream URL, use the first non-comment line as fallback
		if details.VideoURL == decodedURL {
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if line != "" && !strings.HasPrefix(line, "#") {
					details.VideoURL = line
					break
				}
			}
		}

		// If we still don't have a valid URL, return an error
		if details.VideoURL == "" || details.VideoURL == decodedURL {
			return details, fmt.Errorf("could not extract a valid stream URL from m3u8 playlist at %s", decodedURL)
		}

		// Log the extracted details for debugging
		fmt.Printf("Extracted m3u8 stream: %s\n", details.VideoURL)
		if details.SubtitleURL != "" {
			fmt.Printf("Extracted subtitles: %s\n", details.SubtitleURL)
		}
		if details.Referrer != "" {
			fmt.Printf("Using referrer: %s\n", details.Referrer)
		}

	default:
		fmt.Printf("Playing with generic direct URL handling for source: %s. VideoURL: %s\n", source.Type, details.VideoURL)
		// For other types, assume decodedURL is a direct video URL.
	}
	return details, nil
}

// StreamDetails holds all necessary information for playback
type StreamDetails struct {
	VideoURL    string
	AudioURL    string
	SubtitleURL string
	Referrer    string
	SourceType  string // To help decide mpv flags, e.g., "Yt-mp4", "m3u8", "direct"
}

// Config holds user configuration
type Config struct {
	MpvArgs []string `json:"mpv_args"`
}

// loadConfig loads configuration from file or creates default
func loadConfig() Config {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return Config{MpvArgs: []string{}}
	}

	configPath := filepath.Join(homeDir, ".go-ani", "config.json")
	data, err := os.ReadFile(configPath)
	if err != nil {
		// Create default config
		defaultConfig := Config{MpvArgs: []string{}}
		os.MkdirAll(filepath.Dir(configPath), 0755)
		if configData, err := json.MarshalIndent(defaultConfig, "", "  "); err == nil {
			os.WriteFile(configPath, configData, 0644)
		}
		return defaultConfig
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return Config{MpvArgs: []string{}}
	}

	return config
}

func playEpisode(details StreamDetails, title string, epNo int, noDetach bool) {
	if details.VideoURL == "" {
		fmt.Println("Error: VideoURL is empty, cannot play.")
		return
	}

	// Clear screen and show episode info
	fmt.Printf("\033[2K\r\033[1;34mPlaying episode %d...\033[0m\n", epNo)

	// Load config and set up mpv arguments
	config := loadConfig()
	args := make([]string, len(config.MpvArgs))
	copy(args, config.MpvArgs)
	args = append(args, "--force-media-title="+title+" Episode "+strconv.Itoa(epNo))

	// Add proper headers for different source types
	if details.Referrer != "" {
		args = append(args, "--referrer="+details.Referrer)
	}

	// Add user agent to prevent 403 errors
	args = append(args, "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0")

	// For Ak sources (Akamaized CDN), add additional headers
	if details.SourceType == "Ak" {
		args = append(args, "--http-header-fields=Accept: */*")
		args = append(args, "--http-header-fields=Accept-Language: en-US,en;q=0.9")
		args = append(args, "--http-header-fields=Origin: https://allanime.to")
		args = append(args, "--http-header-fields=Sec-Fetch-Dest: video")
		args = append(args, "--http-header-fields=Sec-Fetch-Mode: cors")
		args = append(args, "--http-header-fields=Sec-Fetch-Site: cross-site")
	}

	// Add separate audio file for DASH streams that have separate audio
	if details.AudioURL != "" {
		args = append(args, "--audio-file="+details.AudioURL)
	}

	// Add subtitle file if available and not a Yt-mp4 source (which handles subs via referrer)
	if details.SubtitleURL != "" && details.SourceType != "Yt-mp4" {
		args = append(args, "--sub-file="+details.SubtitleURL)
	}

	// Add video URL as the last argument
	args = append(args, details.VideoURL)

	var cmd *exec.Cmd
	if runtime.GOOS == "android" {
		// For Android, pass referrer as extra if available
		// If not set referrer in mpv.conf
		if details.Referrer != "" {
			cmd = exec.Command("am", "start", "--user", "0", "-a", "android.intent.action.VIEW", "-d", details.VideoURL, "-n", "is.xyz.mpv/.MPVActivity", "--es", "referrer", details.Referrer)
		} else {
			// cmd = exec.Command("am", "start", "--user", "0", "-a", "android.intent.action.VIEW", "-d", details.VideoURL, "-n", "is.xyz.mpv/.MPVActivity")
			// cmd = exec.Command("termux-open", details.VideoURL)
			cmd = exec.Command("termux-open-url", details.VideoURL)
		}
	} else {
		cmd = exec.Command("mpv", args...)
	}

	// Don't attach stdout/stderr to avoid blocking
	if noDetach {
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
	}

	fmt.Printf("Starting playback with mpv args: %v\n", args)
	if err := cmd.Start(); err != nil {
		fmt.Printf("Error starting mpv: %v\n", err)
		return
	}

	// Only wait if noDetach is true, otherwise let mpv run in background
	if noDetach {
		if err := cmd.Wait(); err != nil {
			fmt.Printf("Error during playback: %v\n", err)
		}
	}
}

func handleContinueWatch(db *database.DB) {
	continueOptions, err := db.GetContinueOptions()
	if err != nil {
		fmt.Printf("Error getting continue options: %v\n", err)
		return
	}

	if len(continueOptions) == 0 {
		fmt.Println("No anime in history to continue watching")
		return
	}

	// Format options for fzf
	var fzfOptions []string
	for _, entry := range continueOptions {
		nextEp := entry.CurrentEp + 1
		if nextEp > entry.TotalEps && entry.TotalEps > 0 {
			nextEp = entry.TotalEps
		}
		fzfOptions = append(fzfOptions, fmt.Sprintf("%s - Episode %d/%d (Last: %s)",
			entry.Title, nextEp, entry.TotalEps, entry.LastWatched.Format("2006-01-02")))
	}

	// Select anime to continue using fzf
	selectedIdx, err := selectWithFzf(fzfOptions, "Continue watching: ")
	if err != nil {
		fmt.Printf("Error selecting anime: %v\n", err)
		return
	}
	if selectedIdx == -1 {
		fmt.Println("No selection made")
		return
	}

	selectedEntry := &continueOptions[selectedIdx]

	if selectedEntry == nil {
		fmt.Println("Error: Could not find selected anime")
		return
	}

	// Continue with the selected anime
	continueAnime(db, *selectedEntry)
}

func continueAnime(db *database.DB, entry database.HistoryEntry) {
	client := api.NewClient()

	// Get next episode to watch
	episodeNum := entry.CurrentEp + 1
	if episodeNum > entry.TotalEps && entry.TotalEps > 0 {
		fmt.Printf("All episodes of %s have been watched!\n", entry.Title)
		return
	}

	fmt.Printf("Continuing %s from episode %d...\n", entry.Title, episodeNum)

	// Get episode sources
	sources, err := client.GetEpisodeURL(entry.AnimeID, entry.Mode, strconv.Itoa(episodeNum))
	if err != nil {
		fmt.Printf("Error getting episode sources: %v\n", err)
		return
	}

	// Find best source using same logic as main
	var bestSource *api.EpisodeSource
	for _, source := range sources {
		if source.Type == "Yt-mp4" {
			bestSource = &source
			break
		}
	}
	if bestSource == nil {
		for _, source := range sources {
			if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
				bestSource = &source
				break
			}
		}
	}
	if bestSource == nil {
		for _, source := range sources {
			if bestSource == nil || source.Priority > bestSource.Priority {
				bestSource = &source
			}
		}
	}

	if bestSource == nil {
		fmt.Println("No suitable sources found")
		return
	}

	// Extract stream info and play
	streamDetails, err := extractStreamInfo(bestSource, entry.Quality)
	if err != nil {
		fmt.Printf("Error extracting stream info: %v\n", err)
		return
	}

	playEpisode(streamDetails, entry.Title, episodeNum, false)

	// Update history
	entry.CurrentEp = episodeNum
	entry.LastWatched = time.Now()
	if !contains(entry.WatchedEps, episodeNum) {
		entry.WatchedEps = append(entry.WatchedEps, episodeNum)
	}
	if err := db.AddOrUpdateEntry(entry); err != nil {
		fmt.Printf("Warning: Failed to update history: %v\n", err)
	}

	// Create selectedAnime struct for menu loop
	selectedAnime := api.SearchResult{
		ID:       entry.AnimeID,
		Title:    entry.Title,
		Episodes: entry.TotalEps,
	}

	// Get episodes for menu loop (needed for select option)
	episodes, err := client.GetEpisodes(selectedAnime.ID)
	var episodeOptions []string
	if err != nil {
		fmt.Printf("Warning: Could not fetch episodes for menu: %v\n", err)
		// Create fallback episode options based on total episodes
		for i := 1; i <= entry.TotalEps; i++ {
			episodeOptions = append(episodeOptions, fmt.Sprintf("Episode %d", i))
		}
	} else {
		// Create episode options from fetched episodes
		for _, ep := range episodes {
			subs := ep.AvailableEpisodes["sub"]
			dubs := ep.AvailableEpisodes["dub"]
			subsCount := len(subs)
			dubsCount := len(dubs)
			episodeOptions = append(episodeOptions, fmt.Sprintf("Episode %s (Subs: %d, Dubs: %d)", ep.EpisodeNumber, subsCount, dubsCount))
		}
	}

	// Enter post-playback menu loop
	for {
		menuOptions := []string{
			"next",
			"replay",
			"previous",
			"select",
			"history",
			"change_quality",
			"search_new",
			"quit",
		}

		menuIdx, err := selectWithFzf(menuOptions, fmt.Sprintf("Playing episode %d of %s... ", episodeNum, selectedAnime.Title))
		if err != nil {
			fmt.Printf("Error using fzf for menu: %v\n", err)
			break
		}
		if menuIdx == -1 {
			break
		}
		selectedOption := menuOptions[menuIdx]

		switch selectedOption {
		case "next":
			nextEpisode := episodeNum + 1
			if nextEpisode <= selectedAnime.Episodes {
				episodeNum = nextEpisode
				sources, err := client.GetEpisodeURL(selectedAnime.ID, entry.Mode, strconv.Itoa(episodeNum))
				if err != nil {
					fmt.Printf("Error getting next episode sources: %v\n", err)
					continue
				}
				var nextBestSource *api.EpisodeSource
				for _, source := range sources {
					if source.Type == "Yt-mp4" {
						nextBestSource = &source
						break
					}
				}
				if nextBestSource == nil {
					for _, source := range sources {
						if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
							nextBestSource = &source
							break
						}
					}
				}
				if nextBestSource == nil {
					for _, source := range sources {
						if nextBestSource == nil || source.Priority > nextBestSource.Priority {
							nextBestSource = &source
						}
					}
				}
				if nextBestSource != nil {
					streamDetails, err = extractStreamInfo(nextBestSource, entry.Quality)
					if err != nil {
						fmt.Printf("Error extracting stream info for next episode: %v\n", err)
						continue
					}
					bestSource = nextBestSource
					playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)
					if err := db.MarkEpisodeWatched(selectedAnime.ID, episodeNum); err != nil {
						fmt.Printf("Warning: Failed to update history: %v\n", err)
					}
				}
			} else {
				fmt.Println("Already at the last episode")
			}

		case "replay":
			playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

		case "previous":
			prevEpisode := episodeNum - 1
			if prevEpisode >= 1 {
				episodeNum = prevEpisode
				sources, err := client.GetEpisodeURL(selectedAnime.ID, entry.Mode, strconv.Itoa(episodeNum))
				if err != nil {
					fmt.Printf("Error getting previous episode sources: %v\n", err)
					continue
				}
				var prevBestSource *api.EpisodeSource
				for _, source := range sources {
					if source.Type == "Yt-mp4" {
						prevBestSource = &source
						break
					}
				}
				if prevBestSource == nil {
					for _, source := range sources {
						if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
							prevBestSource = &source
							break
						}
					}
				}
				if prevBestSource == nil {
					for _, source := range sources {
						if prevBestSource == nil || source.Priority > prevBestSource.Priority {
							prevBestSource = &source
						}
					}
				}
				if prevBestSource != nil {
					streamDetails, err = extractStreamInfo(prevBestSource, entry.Quality)
					if err != nil {
						fmt.Printf("Error extracting stream info for previous episode: %v\n", err)
						continue
					}
					bestSource = prevBestSource
					playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)
					if err := db.MarkEpisodeWatched(selectedAnime.ID, episodeNum); err != nil {
						fmt.Printf("Warning: Failed to update history: %v\n", err)
					}
				}
			} else {
				fmt.Println("Already at the first episode")
			}

		case "select":
			// Check if episodeOptions is empty
			if len(episodeOptions) == 0 {
				fmt.Println("No episodes available for selection")
				continue
			}

			// Re-show episode selection
			newEpisodeIdx, err := selectWithFzf(episodeOptions, "Select episode")
			if err != nil {
				fmt.Printf("Error using fzf for episode selection: %v\n", err)
				continue
			}
			if newEpisodeIdx == -1 {
				continue
			}

			newSelectedEpisode := episodeOptions[newEpisodeIdx]

			// Extract episode number and mode from the selected string
			parts := strings.Split(newSelectedEpisode, " ")
			newEpisodeNum, err := strconv.Atoi(parts[1])
			if err != nil {
				fmt.Printf("Error parsing episode number: %v\n", err)
				continue
			}

			// Determine mode based on subs/dubs count
			var selectedMode string
			if strings.Contains(newSelectedEpisode, "Subs: 1, Dubs: 0") {
				selectedMode = "sub"
			} else if strings.Contains(newSelectedEpisode, "Subs: 0, Dubs: 1") {
				selectedMode = "dub"
			} else {
				selectedMode = entry.Mode // fallback to original mode
			}

			episodeNum = newEpisodeNum

			// Get sources for selected episode
			sources, err := client.GetEpisodeURL(selectedAnime.ID, selectedMode, strconv.Itoa(episodeNum))
			if err != nil {
				fmt.Printf("Error getting selected episode sources: %v\n", err)
				continue
			}

			// Find best source for selected episode using same logic
			var selectedBestSource *api.EpisodeSource
			for _, source := range sources {
				if source.Type == "Yt-mp4" {
					selectedBestSource = &source
					break
				}
			}
			if selectedBestSource == nil {
				for _, source := range sources {
					if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
						selectedBestSource = &source
						break
					}
				}
			}
			if selectedBestSource == nil {
				for _, source := range sources {
					if selectedBestSource == nil || source.Priority > selectedBestSource.Priority {
						selectedBestSource = &source
					}
				}
			}

			if selectedBestSource != nil {
				streamDetails, err = extractStreamInfo(selectedBestSource, entry.Quality)
				if err != nil {
					fmt.Printf("Error extracting stream info for selected episode: %v\n", err)
					continue
				}
				bestSource = selectedBestSource
				playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

				// Update history after playing selected episode
				if err := db.MarkEpisodeWatched(selectedAnime.ID, episodeNum); err != nil {
					fmt.Printf("Warning: Failed to update history: %v\n", err)
				}
			}

		case "history":
			handleHistoryMenu(db)

		case "change_quality":
			qualityOptions := []string{"1080p", "720p", "480p", "360p", "best", "worst"}
			qualityIdx, err := selectWithFzf(qualityOptions, "Select quality")
			if err != nil {
				fmt.Printf("Error using fzf for quality selection: %v\n", err)
				continue
			}
			if qualityIdx != -1 {
				newQuality := qualityOptions[qualityIdx]
				entry.Quality = newQuality
				streamDetails, err = extractStreamInfo(bestSource, entry.Quality)
				if err != nil {
					fmt.Printf("Error extracting stream info with new quality: %v\n", err)
					continue
				}
				playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)
			}

		case "search_new":
			// Search for new anime
			fmt.Print("\033[1;36mEnter search term: \033[0m")
			reader := bufio.NewReader(os.Stdin)
			newQuery, err := reader.ReadString('\n')
			if err != nil {
				fmt.Printf("Error reading input: %v\n", err)
				continue
			}
			newQuery = strings.TrimSpace(newQuery)

			if newQuery == "" {
				fmt.Println("Search term cannot be empty")
				continue
			}

			// Search for new anime
			newResults, err := client.SearchAnime(newQuery, entry.Mode)
			if err != nil {
				fmt.Printf("Error searching for anime: %v\n", err)
				continue
			}

			if len(newResults) == 0 {
				fmt.Println("No results found")
				continue
			}

			// Format results for fzf
			var newFzfOptions []string
			for _, result := range newResults {
				newFzfOptions = append(newFzfOptions, fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes))
			}

			// Use fzf to select new anime
			newAnimeIdx, err := selectWithFzf(newFzfOptions, "Select anime")
			if err != nil {
				fmt.Printf("Error using fzf: %v\n", err)
				continue
			}
			if newAnimeIdx == -1 {
				continue
			}
			newSelected := newFzfOptions[newAnimeIdx]

			// Find the selected anime
			for _, result := range newResults {
				formatted := fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes)
				if formatted == newSelected {
					selectedAnime = result
					break
				}
			}

			// Get episodes for new anime
			newEpisodes, err := client.GetEpisodes(selectedAnime.ID)
			if err != nil {
				fmt.Printf("Error getting episodes: %v\n", err)
				continue
			}

			// Update episode options
			var newEpisodeOptions []string
			for _, ep := range newEpisodes {
				subs := ep.AvailableEpisodes["sub"]
				dubs := ep.AvailableEpisodes["dub"]
				subsCount := len(subs)
				dubsCount := len(dubs)
				newEpisodeOptions = append(newEpisodeOptions, fmt.Sprintf("Episode %s (Subs: %d, Dubs: %d)", ep.EpisodeNumber, subsCount, dubsCount))
			}
			episodeOptions = newEpisodeOptions

			// Select episode from new anime
			newEpisodeIdx, err := selectWithFzf(episodeOptions, "Select episode")
			if err != nil {
				fmt.Printf("Error using fzf: %v\n", err)
				continue
			}
			if newEpisodeIdx == -1 {
				continue
			}
			newSelectedEpisode := episodeOptions[newEpisodeIdx]

			// Extract episode number and mode
			parts := strings.Split(newSelectedEpisode, " ")
			episodeNum, err = strconv.Atoi(parts[1])
			if err != nil {
				fmt.Printf("Error parsing episode number: %v\n", err)
				continue
			}

			// Determine mode based on subs/dubs count
			var selectedMode string
			if strings.Contains(newSelectedEpisode, "Subs: 1, Dubs: 0") {
				selectedMode = "sub"
			} else if strings.Contains(newSelectedEpisode, "Subs: 0, Dubs: 1") {
				selectedMode = "dub"
			} else {
				selectedMode = entry.Mode // fallback to original mode
			}

			// Get sources for new episode
			sources, err := client.GetEpisodeURL(selectedAnime.ID, selectedMode, strconv.Itoa(episodeNum))
			if err != nil {
				fmt.Printf("Error getting episode sources: %v\n", err)
				continue
			}

			// Find best source using same logic
			var newBestSource *api.EpisodeSource
			for _, source := range sources {
				if source.Type == "Yt-mp4" {
					newBestSource = &source
					break
				}
			}
			if newBestSource == nil {
				for _, source := range sources {
					if source.Type == "S-mp4" || source.Type == "Luf-Mp4" {
						newBestSource = &source
						break
					}
				}
			}
			if newBestSource == nil {
				for _, source := range sources {
					if newBestSource == nil || source.Priority > newBestSource.Priority {
						newBestSource = &source
					}
				}
			}

			if newBestSource != nil {
				streamDetails, err = extractStreamInfo(newBestSource, entry.Quality)
				if err != nil {
					fmt.Printf("Error extracting stream info: %v\n", err)
					continue
				}
				bestSource = newBestSource
				playEpisode(streamDetails, selectedAnime.Title, episodeNum, false)

				// Update history after playing new anime episode
				historyEntry := database.HistoryEntry{
					AnimeID:     selectedAnime.ID,
					Title:       selectedAnime.Title,
					CurrentEp:   episodeNum,
					TotalEps:    selectedAnime.Episodes,
					WatchedEps:  []int{episodeNum},
					LastWatched: time.Now(),
					Quality:     entry.Quality,
					Mode:        entry.Mode,
				}
				if err := db.AddOrUpdateEntry(historyEntry); err != nil {
					fmt.Printf("Warning: Failed to update history: %v\n", err)
				}
			}

		case "quit":
			fallthrough
		default:
			return
		}
	}
}

func handleHistoryMenu(db *database.DB) {
	continueOptions, err := db.GetContinueOptions()
	if err != nil {
		fmt.Printf("Error getting history: %v\n", err)
		return
	}

	if len(continueOptions) == 0 {
		fmt.Println("No anime in history")
		return
	}

	// Format options for fzf
	var fzfOptions []string
	for _, entry := range continueOptions {
		fzfOptions = append(fzfOptions, fmt.Sprintf("%s - %d/%d episodes (Last: %s)",
			entry.Title, entry.CurrentEp, entry.TotalEps, entry.LastWatched.Format("2006-01-02")))
	}

	// Select anime from history using fzf
	selectedIdx, err := selectWithFzf(fzfOptions, "Select from history: ")
	if err != nil {
		fmt.Printf("Error selecting from history: %v\n", err)
		return
	}
	if selectedIdx == -1 {
		return
	}

	selectedEntry := &continueOptions[selectedIdx]

	if selectedEntry != nil {
		continueAnime(db, *selectedEntry)
	}
}

func isFzfAvailable() bool {
	_, err := exec.LookPath("fzf")
	return err == nil
}

func selectWithFzf(options []string, prompt string) (int, error) {
	if isFzfAvailable() {
		cmd := exec.Command("fzf", "--reverse", "--cycle", "--prompt="+prompt)
		cmd.Stdin = strings.NewReader(strings.Join(options, "\n"))
		var out bytes.Buffer
		cmd.Stdout = &out
		err := cmd.Run()
		if err != nil {
			return -1, err
		}
		selected := strings.TrimSpace(out.String())
		playerConfig := player.Config{
			NoDetach:  noDetach,
			SkipIntro: skipIntro,
			SkipTitle: skipTitle,
			Quality:   quality,
		}
	}
}
		if selected == "" {
			return -1, nil
		}
		for i, option := range options {
			if option == selected {
				return i, nil
			}
		}
		return -1, fmt.Errorf("selected option not found")
	}

	// Use go-fzf as fallback
	f, err := fzf.New(
		fzf.WithInputPosition("bottom"),
		fzf.WithSelectedPrefix("●"),
		fzf.WithUnselectedPrefix("◯"),
		fzf.WithStyles(
			fzf.WithStylePrompt(fzf.Style{
				ForegroundColor: "#FFFFFF",
				BackgroundColor: "#1E1E1E",
			}),
			fzf.WithStyleInputPlaceholder(fzf.Style{
				ForegroundColor: "#AAAAAA",
			}),
			fzf.WithStyleInputText(fzf.Style{
				ForegroundColor: "#00FF00",
				BackgroundColor: "#1E1E1E",
			}),
			fzf.WithStyleCursorLine(fzf.Style{
				ForegroundColor: "#00FF00",
				BackgroundColor: "#1E1E1E",
			}),
			fzf.WithStyleCursor(fzf.Style{
				ForegroundColor: "#00ADD8",
			}),
			fzf.WithStyleSelectedPrefix(fzf.Style{
				ForegroundColor: "#00ADD8",
			}),
			fzf.WithStyleUnselectedPrefix(fzf.Style{
				ForegroundColor: "#FFFFFF",
			}),
			fzf.WithStyleMatches(fzf.Style{
				ForegroundColor: "#00ADD8",
			}),
		),
	)
	if err != nil {
		return -1, err
	}

	idxs, err := f.Find(options, func(i int) string { return options[i] })
	if err != nil {
		return -1, err
	}
	if len(idxs) > 0 {
		return idxs[0], nil
	}
	return -1, nil
}

func handleDeleteHistory(db *database.DB) {
	continueOptions, err := db.GetContinueOptions()
	if err != nil {
		fmt.Printf("Error getting history: %v\n", err)
		return
	}

	if len(continueOptions) == 0 {
		fmt.Println("No anime in history to delete")
		return
	}

	// Format options for fzf
	var fzfOptions []string
	for _, entry := range continueOptions {
		fzfOptions = append(fzfOptions, fmt.Sprintf("%s - %d/%d episodes (Last: %s)",
			entry.Title, entry.CurrentEp, entry.TotalEps, entry.LastWatched.Format("2006-01-02")))
	}

	// Select anime to delete using fzf
	selectedIdx, err := selectWithFzf(fzfOptions, "Select anime to delete: ")
	if err != nil {
		fmt.Printf("Error selecting anime to delete: %v\n", err)
		return
	}
	if selectedIdx == -1 {
		fmt.Println("No selection made")
		return
	}

	selectedEntry := &continueOptions[selectedIdx]

	// Confirm deletion
	fmt.Printf("Are you sure you want to delete '%s' from history? (y/N): ", selectedEntry.Title)
	var confirm string
	fmt.Scanln(&confirm)
	if strings.ToLower(confirm) != "y" && strings.ToLower(confirm) != "yes" {
		fmt.Println("Deletion cancelled")
		return
	}

	// Delete the entry
	if err := db.DeleteEntry(selectedEntry.AnimeID); err != nil {
		fmt.Printf("Error deleting anime: %v\n", err)
		return
	}

	fmt.Printf("Successfully deleted '%s' from history\n", selectedEntry.Title)
}

func handleWipeHistory(db *database.DB) {
	// Confirm wipe
	fmt.Print("Are you sure you want to delete ALL history? This cannot be undone! (y/N): ")
	var confirm string
	fmt.Scanln(&confirm)
	if strings.ToLower(confirm) != "y" && strings.ToLower(confirm) != "yes" {
		fmt.Println("Wipe cancelled")
		return
	}

	// Create backup before wiping
	if err := db.CreateBackup(); err != nil {
		fmt.Printf("Warning: Failed to create backup: %v\n", err)
	}

	// Clear all history
	if err := db.ClearHistory(); err != nil {
		fmt.Printf("Error clearing history: %v\n", err)
		return
	}

	fmt.Println("All history has been deleted")
}

func downloadEpisode(details StreamDetails, title string, epNo int, downloadDir string) {
	if details.VideoURL == "" {
		fmt.Println("Error: VideoURL is empty, cannot download.")
		return
	}

	filename := fmt.Sprintf("%s Episode %d", title, epNo)
	// Sanitize filename
	filename = strings.ReplaceAll(filename, "/", "-")
	filename = strings.ReplaceAll(filename, "\\", "-")
	filename = strings.ReplaceAll(filename, ":", "-")
	filename = strings.ReplaceAll(filename, "*", "-")
	filename = strings.ReplaceAll(filename, "?", "-")
	filename = strings.ReplaceAll(filename, "<", "-")
	filename = strings.ReplaceAll(filename, ">", "-")
	filename = strings.ReplaceAll(filename, "|", "-")

	fmt.Printf("\033[1;34mDownloading episode %d...\033[0m\n", epNo)

	// Download subtitle if available
	if details.SubtitleURL != "" {
		fmt.Printf("Downloading subtitles...\n")
		subCmd := exec.Command("curl", "-s", details.SubtitleURL, "-o", filepath.Join(downloadDir, filename+".vtt"))
		if err := subCmd.Run(); err != nil {
			fmt.Printf("Warning: Failed to download subtitles: %v\n", err)
		}
	}

	// Check if URL is m3u8 stream
	if strings.Contains(details.VideoURL, "m3u8") {
		// Try yt-dlp first
		if _, err := exec.LookPath("yt-dlp"); err == nil {
			args := []string{
				details.VideoURL,
				"--no-skip-unavailable-fragments",
				"--fragment-retries", "infinite",
				"-N", "16",
				"-o", filepath.Join(downloadDir, filename+".mp4"),
			}
			if details.Referrer != "" {
				args = append([]string{"--referer", details.Referrer}, args...)
			}
			cmd := exec.Command("yt-dlp", args...)
			cmd.Stdout = os.Stdout
			cmd.Stderr = os.Stderr
			if err := cmd.Run(); err != nil {
				fmt.Printf("Error downloading with yt-dlp: %v\n", err)
				return
			}
		} else if _, err := exec.LookPath("ffmpeg"); err == nil {
			// Fallback to ffmpeg
			args := []string{
				"-extension_picky", "0",
				"-loglevel", "error",
				"-stats",
				"-i", details.VideoURL,
				"-c", "copy",
				filepath.Join(downloadDir, filename+".mp4"),
			}
			if details.Referrer != "" {
				args = append([]string{"-referer", details.Referrer}, args...)
			}
			cmd := exec.Command("ffmpeg", args...)
			cmd.Stdout = os.Stdout
			cmd.Stderr = os.Stderr
			if err := cmd.Run(); err != nil {
				fmt.Printf("Error downloading with ffmpeg: %v\n", err)
				return
			}
		} else {
			fmt.Println("Error: Neither yt-dlp nor ffmpeg found. Please install one of them for m3u8 downloads.")
			return
		}
	} else {
		// Direct download with aria2c
		if _, err := exec.LookPath("aria2c"); err == nil {
			args := []string{
				"--enable-rpc=false",
				"--check-certificate=false",
				"--continue",
				"--summary-interval=0",
				"-x", "16",
				"-s", "16",
				details.VideoURL,
				"--dir=" + downloadDir,
				"-o", filename + ".mp4",
				"--download-result=hide",
			}
			if details.Referrer != "" {
				args = append([]string{"--referer=" + details.Referrer}, args...)
			}
			cmd := exec.Command("aria2c", args...)
			cmd.Stdout = os.Stdout
			cmd.Stderr = os.Stderr
			if err := cmd.Run(); err != nil {
				fmt.Printf("Error downloading with aria2c: %v\n", err)
				return
			}
		} else {
			fmt.Println("Error: aria2c not found. Please install aria2c for direct downloads.")
			return
		}
	}

	fmt.Printf("\033[1;32mDownload completed: %s\033[0m\n", filepath.Join(downloadDir, filename+".mp4"))
}

func contains(slice []int, value int) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}

func showHelp() {
    helpText := `Usage: go-ani [options] [query]

Options:
  -download, --download           Download the video instead of playing
  -episode, --episode, -e <num>   Specify episode number or range (e.g., 1, 1-5, 1,3,5)
  -quality, --quality, -q <qual>  Video quality (1080p, 720p, 480p, 360p, best, worst)
  -mode, --mode <mode>            Translation type (sub or dub)
  -continue, --continue, -c       Continue watching from history
  -delete, --delete               Delete specific anime from history
  -wipe, --wipe                   Delete entire history database
  -skip, --skip                   Use go-aniskip to skip the intro of the episode (mpv only)
  -skip-title, --skip-title <title> Use given title as go-aniskip query
  -no-detach, --no-detach         Don't detach the player (useful for in-terminal playback)
  -version, --version, -V         Show version
  -help, --help, -h               Show this help message
`
    fmt.Print(helpText)
}

// parseEpisodeRange parses episode ranges like "1-5", "1,3,5", or "1"
func parseEpisodeRange(episodeStr string) ([]int, error) {
	var episodes []int

	// Handle comma-separated episodes
	if strings.Contains(episodeStr, ",") {
		parts := strings.Split(episodeStr, ",")
		for _, part := range parts {
			part = strings.TrimSpace(part)
			if strings.Contains(part, "-") {
				// Handle range within comma-separated list
				rangeEps, err := parseRange(part)
				if err != nil {
					return nil, err
				}
				episodes = append(episodes, rangeEps...)
			} else {
				// Single episode
				ep, err := strconv.Atoi(part)
				if err != nil {
					return nil, fmt.Errorf("invalid episode number: %s", part)
				}
				if ep <= 0 {
					return nil, fmt.Errorf("episode number must be positive: %d", ep)
				}
				episodes = append(episodes, ep)
			}
		}
	} else if strings.Contains(episodeStr, "-") {
		// Handle range
		rangeEps, err := parseRange(episodeStr)
		if err != nil {
			return nil, err
		}
		episodes = rangeEps
	} else {
		// Single episode
		ep, err := strconv.Atoi(strings.TrimSpace(episodeStr))
		if err != nil {
			return nil, fmt.Errorf("invalid episode number: %s", episodeStr)
		}
		if ep <= 0 {
			return nil, fmt.Errorf("episode number must be positive: %d", ep)
		}
		episodes = []int{ep}
	}

	// Remove duplicates and sort
	episodes = removeDuplicatesAndSort(episodes)
	return episodes, nil
}

// parseRange parses a range like "1-5" and returns []int{1,2,3,4,5}
func parseRange(rangeStr string) ([]int, error) {
	parts := strings.Split(rangeStr, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid range format: %s (expected format: start-end)", rangeStr)
	}

	start, err := strconv.Atoi(strings.TrimSpace(parts[0]))
	if err != nil {
		return nil, fmt.Errorf("invalid start episode: %s", parts[0])
	}

	end, err := strconv.Atoi(strings.TrimSpace(parts[1]))
	if err != nil {
		return nil, fmt.Errorf("invalid end episode: %s", parts[1])
	}

	if start <= 0 || end <= 0 {
		return nil, fmt.Errorf("episode numbers must be positive: %d-%d", start, end)
	}

	if start > end {
		return nil, fmt.Errorf("start episode cannot be greater than end episode: %d-%d", start, end)
	}

	var episodes []int
	for i := start; i <= end; i++ {
		episodes = append(episodes, i)
	}

	return episodes, nil
}

// removeDuplicatesAndSort removes duplicates from slice and sorts it
func removeDuplicatesAndSort(episodes []int) []int {
	keys := make(map[int]bool)
	var result []int

	for _, ep := range episodes {
		if !keys[ep] {
			keys[ep] = true
			result = append(result, ep)
		}
	}

	// Simple sort
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i] > result[j] {
				result[i], result[j] = result[j], result[i]
			}
		}

	}

	return result
}


