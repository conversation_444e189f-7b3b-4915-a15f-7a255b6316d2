package player

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"

	"github.com/stl3/go-ani/history"
)

const AllanimeRefr = "https://allanime.to"

type Player interface {
	Play(url string, title string, episode int, history *history.Manager) error
	Stop() error
	SelectQuality(url string, qualities []string) (string, error)
	GetVideoProviders(url string) ([]VideoProvider, error)
}

type Config struct {
	NoDetach  bool
	SkipIntro bool
	SkipTitle string
	MalID     string
	Quality   string
}

type MPVPlayer struct {
	config     Config
	executable string
	cmd        *exec.Cmd
}

type VLCPlayer struct {
	config     Config
	executable string
	cmd        *exec.Cmd
}

type AndroidPlayer struct {
	config Config
	player string // "mpv" or "vlc"
}

type IINAPlayer struct {
	config     Config
	executable string
	cmd        *exec.Cmd
}

type SyncplayPlayer struct {
	config     Config
	executable string
	cmd        *exec.Cmd
}

type CattPlayer struct{}

type ISHPlayer struct{}

type DownloadPlayer struct {
	downloadDir string
}

func getEnvWithDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

func getDefaultPlayer() string {
	switch runtime.GOOS {
	case "darwin":
		return "iina"
	case "windows":
		return "mpv.exe"
	default:
		return "mpv"
	}
}

func NewPlayer(playerType string, config Config) (Player, error) {
	if playerType == "" {
		playerType = getEnvWithDefault("ANI_CLI_PLAYER", getDefaultPlayer())
	}

	downloadDir := getEnvWithDefault("ANI_CLI_DOWNLOAD_DIR", ".")

	switch {
	case strings.Contains(playerType, "mpv"):
		executable := "mpv"
		if runtime.GOOS == "windows" {
			executable = "mpv.exe"
		} else if strings.Contains(playerType, "flatpak") {
			executable = "flatpak run io.mpv.Mpv"
		}
		return &MPVPlayer{config: config, executable: executable}, nil

	case strings.Contains(playerType, "vlc"):
		executable := "vlc"
		if runtime.GOOS == "windows" {
			executable = "vlc.exe"
		}
		return &VLCPlayer{config: config, executable: executable}, nil

	case strings.Contains(playerType, "iina"):
		return &IINAPlayer{config: config, executable: "/Applications/IINA.app/Contents/MacOS/iina-cli"}, nil

	case strings.Contains(playerType, "syncplay"):
		var executable string
		switch runtime.GOOS {
		case "darwin":
			executable = "/Applications/Syncplay.app/Contents/MacOS/syncplay"
		case "windows":
			executable = "syncplay.exe"
		default:
			executable = "syncplay"
		}
		return &SyncplayPlayer{config: config, executable: executable}, nil

	case strings.Contains(playerType, "android"):
		player := "mpv"
		if strings.Contains(playerType, "vlc") {
			player = "vlc"
		}
		return &AndroidPlayer{config: config, player: player}, nil

	case playerType == "download":
		return &DownloadPlayer{downloadDir: downloadDir}, nil

	case playerType == "catt":
		return &CattPlayer{}, nil

	case playerType == "iSH":
		return &ISHPlayer{}, nil

	default:
		return nil, fmt.Errorf("unsupported player type: %s", playerType)
	}
}

func (p *MPVPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	args := []string{
		"--fullscreen",
		"--no-osd",
		"--no-video-title-show",
		"--no-embedded-video",
		"--no-sub-autodetect-file",
	}

	// Handle different video types
	if strings.Contains(url, "youtube.com") || strings.Contains(url, "youtu.be") || strings.Contains(url, "/clock.json") {
		args = append(args, "--ytdl")
	} else if strings.Contains(url, ".m3u8") {
		args = append(args, "--demuxer-lavf-format=m3u8")
	}

	// Add quality selection if specified
	if p.config.Quality != "" {
		args = append(args, "--ytdl-format=bestvideo[height<=?", p.config.Quality, "]+bestaudio/best[height<=?", p.config.Quality, "]")
	}

	if p.config.SkipIntro && p.config.MalID != "" {
		if skipCmd, err := exec.LookPath("go-aniskip.exe"); err == nil {
			skipOut, err := exec.Command(skipCmd, "-q", p.config.MalID, "-e", strconv.Itoa(episode)).Output()
			if err == nil && len(skipOut) > 0 {
				args = append(args, strings.Split(string(skipOut), " ")...)
			}
		}
	}

	// Add title and episode to metadata
	args = append(args, "--title=", fmt.Sprintf("%s - Episode %d", title, episode))

	// Set user agent for embedded URLs
	args = append(args, "--user-agent=", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	p.cmd = exec.Command(p.executable, append(args, url)...)
	p.cmd.Stdout = os.Stdout
	p.cmd.Stderr = os.Stderr

	// If no-detach flag is set, run in foreground
	if p.config.NoDetach {
		err := p.cmd.Run()
		if err != nil {
			return fmt.Errorf("error running mpv: %v", err)
		}
	} else {
		err := p.cmd.Start()
		if err != nil {
			return fmt.Errorf("error starting mpv: %v", err)
		}
	}

	return nil
}

func (p *MPVPlayer) Stop() error {
	if p.cmd != nil && p.cmd.Process != nil {
		return p.cmd.Process.Kill()
	}
	return nil
}

func (p *MPVPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *MPVPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *AndroidPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	cmd := exec.Command("termux-open-url", url)
	return cmd.Start()
}

func (p *AndroidPlayer) Stop() error {
	return nil
}

func (p *AndroidPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *SyncplayPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *ISHPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *AndroidPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *SyncplayPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	args := []string{
		"--play-and-exit",
		"--meta-title=" + title,
		"--http-referrer=" + AllanimeRefr,
		url,
	}

	if p.config.SkipIntro && p.config.MalID != "" {
		if skipCmd, err := exec.LookPath("go-aniskip.exe"); err == nil {
			skipOut, err := exec.Command(skipCmd, "-q", p.config.MalID, "-e", strconv.Itoa(episode)).Output()
			if err == nil && len(skipOut) > 0 {
				args = append(args, strings.Split(string(skipOut), " ")...)
			}
		}
	}

	if p.config.NoDetach {
		p.cmd = exec.Command(p.executable, args...)
		p.cmd.Stdout = os.Stdout
		p.cmd.Stderr = os.Stderr
		return p.cmd.Run()
	}

	return nil
}

func (p *ISHPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	fmt.Printf("\033]8;;vlc://%s\a~~~~~~~~~~~~~~~~~~~~\n~ Tap to open VLC ~\n~~~~~~~~~~~~~~~~~~~~\033]8;;\a\n", url)
	return nil
}

func (p *CattPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	cmd := exec.Command("catt", "play", url)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

func (p *SyncplayPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *ISHPlayer) SelectQuality(url string, qualities []string) (string, error) {
	// ISH doesn't support quality switching during playback
	return url, nil
}

func (p *CattPlayer) Stop() error {
	cmd := exec.Command("catt", "stop")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

func (p *SyncplayPlayer) Stop() error {
	if p.cmd != nil && p.cmd.Process != nil {
		return p.cmd.Process.Kill()
	}
	return nil
}

func (p *ISHPlayer) Stop() error {
	return nil
}

func (p *CattPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *CattPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *DownloadPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	mapFunc := func(r rune) rune {
		if strings.ContainsRune("\\/:|*?\"<>", r) {
			return '_'
		}
		return r
	}

	safeTitle := strings.Map(mapFunc, title)

	filename := fmt.Sprintf("%s - Episode %d.mp4", strings.ReplaceAll(safeTitle, " ", "_"), episode)
	fullPath := filepath.Join(p.downloadDir, filename)

	if strings.Contains(url, "m3u8") {
		if _, err := exec.LookPath("yt-dlp"); err == nil {
			cmd := exec.Command("yt-dlp", url,
				"--no-skip-unavailable-fragments",
				"--fragment-retries", "infinite",
				"-N", "16",
				"--referer", AllanimeRefr,
				"-o", fullPath)
			cmd.Stdout = os.Stdout
			cmd.Stderr = os.Stderr
			return cmd.Run()
		}

		cmd := exec.Command("ffmpeg",
			"-loglevel", "error",
			"-stats",
			"-headers", fmt.Sprintf("Referer: %s\r\n", AllanimeRefr),
			"-i", url,
			"-c", "copy",
			fullPath)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		return cmd.Run()
	}

	cmd := exec.Command("aria2c",
		"--enable-rpc=false",
		"--check-certificate=false",
		"--continue",
		"--summary-interval=0",
		"-x", "16",
		"-s", "16",
		"--referer="+AllanimeRefr,
		url,
		"--dir="+p.downloadDir,
		"-o", filepath.Base(fullPath),
		"--download-result=hide")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

func (p *DownloadPlayer) Stop() error {
	return nil
}

func (p *DownloadPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *DownloadPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *IINAPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	args := []string{
		"--play-and-exit",
		"--meta-title=" + title,
		"--http-referrer=" + AllanimeRefr,
		url,
	}

	if p.config.SkipIntro && p.config.MalID != "" {
		if skipCmd, err := exec.LookPath("go-aniskip.exe"); err == nil {
			skipOut, err := exec.Command(skipCmd, "-q", p.config.MalID, "-e", strconv.Itoa(episode)).Output()
			if err == nil && len(skipOut) > 0 {
				args = append(args, strings.Split(string(skipOut), " ")...)
			}
		}
	}

	if p.config.NoDetach {
		p.cmd = exec.Command(p.executable, args...)
		p.cmd.Stdout = os.Stdout
		p.cmd.Stderr = os.Stderr
		return p.cmd.Run()
	}

	return nil
}

func (p *IINAPlayer) Stop() error {
	if p.cmd != nil && p.cmd.Process != nil {
		return p.cmd.Process.Kill()
	}
	return nil
}

func (p *IINAPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *IINAPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

func (p *VLCPlayer) Play(url string, title string, episode int, history *history.Manager) error {
	args := []string{
		"--fullscreen",
		"--no-osd",
		"--no-video-title-show",
		"--no-embedded-video",
		"--no-sub-autodetect-file",
	}

	if p.config.SkipIntro && p.config.MalID != "" {
		if skipCmd, err := exec.LookPath("go-aniskip.exe"); err == nil {
			skipOut, err := exec.Command(skipCmd, "-q", p.config.MalID, "-e", strconv.Itoa(episode)).Output()
			if err == nil && len(skipOut) > 0 {
				args = append(args, strings.Split(string(skipOut), " ")...)
			}
		}
	}

	p.cmd = exec.Command(p.executable, append(args, url)...)
	p.cmd.Stdout = os.Stdout
	p.cmd.Stderr = os.Stderr
	return p.cmd.Run()
}

func (p *VLCPlayer) Stop() error {
	if p.cmd != nil && p.cmd.Process != nil {
		return p.cmd.Process.Kill()
	}
	return nil
}

func (p *VLCPlayer) SelectQuality(url string, qualities []string) (string, error) {
	return url, nil
}

func (p *VLCPlayer) GetVideoProviders(url string) ([]VideoProvider, error) {
	return []VideoProvider{}, nil
}

type VideoProvider struct {
	Name     string
	ID       string
	Quality  string
	URL      string
	Priority int
}
