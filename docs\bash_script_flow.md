# ani-cli-mymod-current.sh Flow Analysis

## 1. Setup and Configuration
- Sets user agent string
- Configures API endpoints and references
- Initializes variables for mode (sub/dub), quality, etc.

## 2. Search Flow
### Search Anime
1. Makes GraphQL request to AllAnime API
2. Query parameters:
   ```bash
   variables={"search":{"allowAdult":false,"allowUnknown":false,"query":"$QUERY"},"limit":40,"page":1,"translationType":"$MODE","countryOrigin":"ALL"}
   ```
3. Parses response to extract:
   - Show ID
   - Name
   - Number of episodes
   - Formats as: "ID\tName (Episodes)"

### Episode List
1. Makes GraphQL request for episodes
2. Query parameters:
   ```bash
   variables={"showId":"$ID"}
   ```
3. Returns sorted list of episodes

## 3. Episode Selection
### Get Episode URL
1. Makes GraphQL request for episode sources
2. Query parameters:
   ```bash
   variables={"showId":"$ID","translationType":"$MODE","episodeString":"$EP_NO"}
   ```
3. Response contains multiple source URLs with:
   - sourceUrl (encrypted)
   - sourceName
   - priority
   - type

## 4. URL Processing
### Provider Initialization
1. Maps source names to provider IDs:
   - wixmp (m3u8 -> mp4)
   - dropbox (mp4)
   - wetransfer (mp4)
   - sharepoint (mp4)
   - gogoanime (m3u8)

### URL Decoding
1. For YouTube URLs (start with `--`):
   - Extracts base64 encoded part
   - Decodes using specific mapping:
     ```bash
     01 -> 9
     08 -> 0
     05 -> =
     0a -> 2
     0b -> 3
     0c -> 4
     07 -> ?
     00 -> 8
     5c -> d
     0f -> 7
     5e -> f
     17 -> /
     54 -> l
     09 -> 1
     48 -> p
     4f -> w
     0e -> 6
     5b -> c
     5d -> e
     0d -> 5
     53 -> k
     1e -> &
     5a -> b
     59 -> a
     4a -> r
     4c -> t
     4e -> v
     57 -> o
     51 -> i
     ```
   - Adds `/clock.json` to the end

2. For other URLs:
   - Handles wixmp URLs:
     - Extracts quality from URL
     - Creates multiple quality variants
   - Handles vipanicdn/anifastcdn:
     - Processes m3u8 playlists
     - Extracts quality information

## 5. Quality Selection
1. Sorts links by quality (highest to lowest)
2. Selects based on user preference:
   - `best`: takes highest quality
   - `worst`: takes lowest quality
   - specific quality: matches exact quality
   - defaults to best if not found

## 6. Playback
### MPV Player
1. Uses ani-skip for intro skipping
2. Handles different URL types:
   - YouTube URLs directly
   - m3u8 playlists
   - mp4 files
3. Detaches process unless `--no-detach` is set

## 7. History Management
1. Tracks watched episodes
2. Updates history file
3. Allows resuming from last watched episode

## 8. Error Handling
1. Validates responses
2. Checks URL formats
3. Handles missing episodes
4. Provides fallbacks for quality selection

## 9. Additional instructions for Go Implementation
Go Implementation Guide for ani-cli
This document provides detailed instructions for recreating the ani-cli-mymod-current.sh bash script in Go. The Go version must match the bash script's functionality exactly in terms of API requests, data processing, and playback, while improving organization and leveraging Go's strengths. It will detect if fzf is installed natively and fall back to github.com/koki-develop/go-fzf if not.
Project Setup

Initialize Go Module

Run go mod init github.com/yourusername/ani-cli-go to create a new Go module.
Install dependencies: go get github.com/koki-develop/go-fzf.


Directory Structure
ani-cli-go/
├── cmd/              # Main application entry point
│   └── main.go
├── internal/         # Internal packages
│   ├── config/       # Configuration handling
│   ├── api/         # API client and request logic
│   ├── ui/          # User interface and selection logic
│   ├── provider/    # URL processing and provider logic
│   ├── playback/    # Playback handling
│   └── history/     # History management
├── go.mod
└── go.sum



1. Configuration

Config Struct: Define a Config struct in internal/config/config.go to hold settings like API endpoints, user agent, quality, and player preferences.package config

type Config struct {
    UserAgent      string
    AllAnimeAPI    string
    AllAnimeBase   string
    AllAnimeRefr   string
    Mode           string // "sub" or "dub"
    Quality        string // "best", "worst", or specific resolution
    Player         string
    DownloadDir    string
    UseExternalMenu bool
    SkipIntro      bool
    SkipTitle      string
    NoDetach       bool
}

func NewConfig() *Config {
    return &Config{
        UserAgent:     "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        AllAnimeAPI:   "https://api.allanime.day/api",
        AllAnimeBase:  "allanime.day",
        AllAnimeRefr:  "https://allanime.to",
        Mode:          "sub",
        Quality:       "best",
        Player:        "mpv", // Default, adjust based on OS later
        DownloadDir:   ".",
        UseExternalMenu: false,
        SkipIntro:     false,
        NoDetach:      false,
    }
}


Command-Line Parsing: Use the flag package in cmd/main.go to parse options like -q, -e, --dub, etc., matching the bash script's options.
OS-Specific Defaults: Adjust the default player based on runtime.GOOS (e.g., iina for Darwin, android_mpv for Android).

2. Dependency Checks

Function: Create CheckDependencies in internal/ui/deps.go.package ui

import (
    "os/exec"
    "errors"
)

var UseGoFzf bool

func CheckDependencies() error {
    deps := []string{"curl", "sed", "grep"}
    for _, dep := range deps {
        if _, err := exec.LookPath(dep); err != nil {
            return errors.New("dependency not found: " + dep)
        }
    }
    if _, err := exec.LookPath("fzf"); err != nil {
        UseGoFzf = true
    }
    return nil
}


Fzf Detection: Set a global UseGoFzf flag if fzf is not found, triggering the use of go-fzf.

3. API Interactions

Package: Create internal/api/api.go for AllAnime API interactions.
Search Anime:
Replicate the GraphQL query exactly as in the bash script.
Use net/http to send the request with the same headers and query parameters.

package api

import (
    "net/http"
    "net/url"
    "encoding/json"
    "fmt"
    "strings"
)

type Anime struct {
    ID         string
    Name       string
    Episodes   int
}

func SearchAnime(cfg *config.Config, query string) ([]Anime, error) {
    variables := fmt.Sprintf(`{"search":{"allowAdult":false,"allowUnknown":false,"query":"%s"},"limit":40,"page":1,"translationType":"%s","countryOrigin":"ALL"}`, query, cfg.Mode)
    gql := "query($search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType) { shows(search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin) { edges { _id name availableEpisodes __typename } } }"
    params := url.Values{
        "variables": {variables},
        "query":     {gql},
    }
    req, _ := http.NewRequest("GET", cfg.AllAnimeAPI+"?"+params.Encode(), nil)
    req.Header.Set("User-Agent", cfg.UserAgent)
    req.Header.Set("Referer", cfg.AllAnimeRefr)
    resp, err := http.DefaultClient.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    var result struct {
        Data struct {
            Shows struct {
                Edges []struct {
                    ID              string `json:"_id"`
                    Name            string
                    AvailableEpisodes map[string]int
                }
            }
        }
    }
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    var animes []Anime
    for _, edge := range result.Data.Shows.Edges {
        animes = append(animes, Anime{
            ID:       edge.ID,
            Name:     edge.Name,
            Episodes: edge.AvailableEpisodes[cfg.Mode],
        })
    }
    return animes, nil
}


Episode List: Implement EpisodesList with the same GraphQL query and sorting logic.
Get Episode URL: Implement GetEpisodeURL to fetch source URLs, matching the bash script's query structure.

4. User Interface

Package: Create internal/ui/ui.go.
Selection Logic:
If UseGoFzf is false, use os/exec to run fzf.
Otherwise, use github.com/koki-develop/go-fzf.

package ui

import (
    "os/exec"
    "strings"
    gfzf "github.com/koki-develop/go-fzf"
)

func Select(prompt string, items []string, multi bool) ([]string, error) {
    if !UseGoFzf {
        args := []string{"--reverse", "--cycle", "--prompt", prompt}
        if multi {
            args = append(args, "-m")
        }
        cmd := exec.Command("fzf", args...)
        cmd.Stdin = strings.NewReader(strings.Join(items, "\n"))
        out, err := cmd.Output()
        if err != nil {
            return nil, err
        }
        return strings.Split(strings.TrimSpace(string(out)), "\n"), nil
    }
    f, err := gfzf.New(gfzf.WithPrompt(prompt), gfzf.WithMultiple(multi))
    if err != nil {
        return nil, err
    }
    idxs, err := f.Find(items, func(i int) string { return items[i] })
    if err != nil {
        return nil, err
    }
    var selected []string
    for _, i := range idxs {
        selected = append(selected, items[i])
    }
    return selected, nil
}


Query Prompt: If no query is provided, prompt the user using bufio.Reader or go-fzf.

5. URL Processing

Package: Create internal/provider/provider.go.
Provider Initialization: Replicate the bash script's provider mapping and decoding logic.package provider

var decodeMap = map[string]string{
    "01": "9", "08": "0", "05": "=", "0a": "2", "0b": "3", "0c": "4", "07": "?", "00": "8",
    "5c": "d", "0f": "7", "5e": "f", "17": "/", "54": "l", "09": "1", "48": "p", "4f": "w",
    "0e": "6", "5b": "c", "5d": "e", "0d": "5", "53": "k", "1e": "&", "5a": "b", "59": "a",
    "4a": "r", "4c": "t", "4e": "v", "57": "o", "51": "i",
}

func DecodeURL(encoded string) string {
    parts := strings.Split(encoded, "")
    var decoded strings.Builder
    for i := 0; i < len(parts)-1; i += 2 {
        pair := parts[i] + parts[i+1]
        if val, ok := decodeMap[pair]; ok {
            decoded.WriteString(val)
        }
    }
    result := decoded.String()
    if strings.Contains(result, "/clock") {
        result += ".json"
    }
    return result
}


Link Generation: Match the bash script's get_links logic for each provider (wixmp, dropbox, etc.).

6. Playback

Package: Create internal/playback/playback.go.
Play Episode: Use os/exec to run MPV with the same arguments as the bash script.package playback

import (
    "os/exec"
)

func Play(cfg *config.Config, url, title, epNo string) error {
    args := []string{"--profile=anime", "--force-media-title=" + title + "Episode " + epNo, url}
    if cfg.NoDetach {
        cmd := exec.Command(cfg.Player, args...)
        return cmd.Run()
    }
    cmd := exec.Command(cfg.Player, args...)
    cmd.Start()
    return nil
}



7. History Management

Package: Create internal/history/history.go.
Functions: Implement UpdateHistory and ReadHistory to match the bash script's file-based history.

8. Main Flow

cmd/main.go:
Parse flags.
Check dependencies.
If no query, prompt user.
Search anime, select anime, select episode, process URL, play.
Implement the replay loop with options (next, replay, etc.).



Additional Notes

Error Handling: Use Go's error type throughout for robust feedback.
Concurrency: Use goroutines for parallel link generation, matching the bash script's & usage.
Testing: Write unit tests for API, provider, and UI functions.

This structure ensures a 1:1 replication of the bash script's flow in Go, with improved modularity and the specified fzf fallback.
