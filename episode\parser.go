package episode

import (
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

// EpisodeRange represents a range of episodes
// It can be a single episode (e.g., "1") or a range (e.g., "1-5")
type EpisodeRange struct {
	Start int
	End   int
}

// ParseEpisodeRange parses an episode number or range string
// Supports formats:
// - Single episode: "1", "01"
// - Range: "1-5", "1-5,7-9"
// - Multiple episodes: "1,2,3"
func ParseEpisodeRange(input string) ([]EpisodeRange, error) {
	// First try to parse as a simple range (e.g., "1-5")
	if rangeRe := regexp.MustCompile(`^(\d+)-(\d+)$`); rangeRe.MatchString(input) {
		matches := rangeRe.FindStringSubmatch(input)
		start, err := strconv.Atoi(matches[1])
		if err != nil {
			return nil, fmt.<PERSON><PERSON><PERSON>("invalid start episode number: %v", err)
		}
		end, err := strconv.Atoi(matches[2])
		if err != nil {
			return nil, fmt.Errorf("invalid end episode number: %v", err)
		}
		if start > end {
			return nil, errors.New("start episode cannot be greater than end episode")
		}
		return []EpisodeRange{{Start: start, End: end}}, nil
	}

	// Try to parse as multiple episodes (e.g., "1,2,3")
	if strings.Contains(input, ",") {
		episodes := strings.Split(input, ",")
		var ranges []EpisodeRange
		for _, ep := range episodes {
			if strings.Contains(ep, "-") {
				subRanges, err := ParseEpisodeRange(ep)
				if err != nil {
					return nil, fmt.Errorf("invalid episode range '%s': %v", ep, err)
				}
				ranges = append(ranges, subRanges...)
			} else {
				num, err := strconv.Atoi(strings.TrimSpace(ep))
				if err != nil {
					return nil, fmt.Errorf("invalid episode number '%s': %v", ep, err)
				}
				ranges = append(ranges, EpisodeRange{Start: num, End: num})
			}
		}
		return ranges, nil
	}

	// Try to parse as a single episode
	num, err := strconv.Atoi(input)
	if err != nil {
		return nil, fmt.Errorf("invalid episode number: %v", err)
	}
	return []EpisodeRange{{Start: num, End: num}}, nil
}

// GenerateEpisodeNumbers generates all episode numbers from the ranges
func (r EpisodeRange) GenerateEpisodeNumbers() []int {
	var episodes []int
	for i := r.Start; i <= r.End; i++ {
		episodes = append(episodes, i)
	}
	return episodes
}

// GenerateAllEpisodeNumbers generates all episode numbers from a slice of ranges
func GenerateAllEpisodeNumbers(ranges []EpisodeRange) []int {
	var allEpisodes []int
	for _, r := range ranges {
		allEpisodes = append(allEpisodes, r.GenerateEpisodeNumbers()...)
	}
	// Sort and remove duplicates
	sort.Ints(allEpisodes)
	return uniqueInts(allEpisodes)
}

// uniqueInts removes duplicates from a slice of integers
func uniqueInts(slice []int) []int {
	if len(slice) == 0 {
		return slice
	}
	unique := make([]int, 1, len(slice))
	unique[0] = slice[0]
	for _, value := range slice[1:] {
		if value != unique[len(unique)-1] {
			unique = append(unique, value)
		}
	}
	return unique
}
