package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"

	"github.com/stl3/go-ani/api"
)

type SearchResult struct {
	ID       string
	Title    string
	Episodes int
}

func main() {
	// Parse command line arguments
	var err error
	var quality, query string
	var mode string = "sub" // Default to lowercase sub
	var download bool
	var episode string
	var syncPlay bool
	var selectNth int
	var continueWatch bool
	var deleteHistory bool
	var version bool
	var episodeNum int
	var cmdEpisodeNum string

	flag.StringVar(&quality, "quality", "1080p", "Video quality (e.g., 1080p, 720p, 480p)")
	flag.StringVar(&query, "query", "", "Search query for anime")
	flag.StringVar(&mode, "mode", "sub", "Translation type (sub or dub)")
	flag.BoolVar(&download, "download", false, "Download the video instead of playing")
	flag.StringVar(&episode, "episode", "", "Specify episode number or range")
	flag.BoolVar(&syncPlay, "syncplay", false, "Use Syncplay to watch with friends")
	flag.IntVar(&selectNth, "select-nth", 0, "Select nth entry")
	flag.BoolVar(&continueWatch, "continue", false, "Continue watching from history")
	flag.BoolVar(&deleteHistory, "delete", false, "Delete history")
	flag.BoolVar(&version, "version", false, "Show version")
	flag.Parse()

	// Combine remaining arguments into query
	args := flag.Args()
	if len(args) > 0 {
		query = strings.Join(args, " ")
	}

	// Validate required arguments
	if query == "" && !continueWatch {
		fmt.Println("Error: Either --query or --continue flag is required")
		flag.Usage()
		return
	}

	// Handle spaces in query
	query = strings.TrimSpace(query)
	if query == "" {
		fmt.Println("Error: Query cannot be empty")
		flag.Usage()
		return
	}

	// Parse episode number
	if episode != "" {
		episodeNum, err = strconv.Atoi(episode)
		if err != nil {
			fmt.Printf("Error: Invalid episode number: %v\n", err)
			flag.Usage()
			return
		}
		cmdEpisodeNum = strconv.Itoa(episodeNum)
	} else {
		episodeNum = 1 // Default to episode 1
		cmdEpisodeNum = "1"
	}

	// Validate translation type
	mode = strings.ToLower(mode)
	if mode != "sub" && mode != "dub" {
		fmt.Printf("Error: Invalid translation type: %s. Must be either 'sub' or 'dub'\n", mode)
		flag.Usage()
		return
	}

	// Create API client
	client := api.NewClient()

	// Search anime
	results, err := client.SearchAnime(query, mode)
	if err != nil {
		fmt.Printf("Error searching for anime: %v\n", err)
		return
	}

	// Display results
	if len(results) == 0 {
		fmt.Println("No results found")
		return
	}

	// Format results for fzf
	var fzfOptions []string
	for _, result := range results {
		fzfOptions = append(fzfOptions, fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes))
	}

	// Use system fzf command to select an anime
	cmd := exec.Command("fzf", "--reverse", "--cycle", "--prompt='Select anime'")
	cmd.Stdin = strings.NewReader(strings.Join(fzfOptions, "\n"))
	var animeOut bytes.Buffer
	cmd.Stdout = &animeOut
	err = cmd.Run()
	if err != nil {
		fmt.Printf("Error using fzf: %v\n", err)
		return
	}

	if animeOut.Len() == 0 {
		fmt.Println("No selection made")
		return
	}

	// Convert selected string to string
	selected := strings.TrimSpace(animeOut.String())
	if selected == "" {
		fmt.Println("No selection made")
		return
	}

	// Find the selected anime in our results
	selectedAnime := results[0] // Default to first result
	for _, result := range results {
		formatted := fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes)
		if formatted == selected {
			selectedAnime = result
			break
		}
	}

	fmt.Printf("Selected: %s\n", selectedAnime.Title)

	// Get episodes for the selected anime
	episodes, err := client.GetEpisodes(selectedAnime.ID)
	if err != nil {
		fmt.Printf("Error getting episodes: %v\n", err)
		return
	}

	// Create fzf options
	var episodeOptions []string
	for _, ep := range episodes {
		subs := ep.AvailableEpisodes["sub"]
		dubs := ep.AvailableEpisodes["dub"]
		subsCount := len(subs)
		dubsCount := len(dubs)

		episodeOptions = append(episodeOptions, fmt.Sprintf("Episode %s (Subs: %d, Dubs: %d)", ep.EpisodeNumber, subsCount, dubsCount))
	}

	// Display available episodes
	fmt.Println("\nAvailable episodes:")
	for _, option := range episodeOptions {
		fmt.Println(option)
	}

	// Use fzf to select an episode
	episodeCmd := exec.Command("fzf", "--reverse", "--cycle", "--prompt='Select episode'")
	episodeCmd.Stdin = strings.NewReader(strings.Join(episodeOptions, "\n"))
	var episodeOut bytes.Buffer
	episodeCmd.Stdout = &episodeOut
	err = episodeCmd.Run()
	if err != nil {
		fmt.Printf("Error using fzf: %v\n", err)
		return
	}

	selectedEpisode := strings.TrimSpace(episodeOut.String())
	if selectedEpisode == "" {
		fmt.Println("No episode selected")
		return
	}

	// Extract episode number from the selected string
	episodeNum, err = strconv.Atoi(strings.Split(selectedEpisode, " ")[1])
	cmdEpisodeNum = strconv.Itoa(episodeNum)
	if err != nil {
		fmt.Printf("Error: Invalid episode number: %v\n", err)
		return
	}

	// Get episode URLs
	sources, err := client.GetEpisodeURL(selectedAnime.ID, mode, cmdEpisodeNum)
	if err != nil {
		fmt.Printf("Error getting episode sources: %v\n", err)
		return
	}

	// Display available sources
	fmt.Println("\nAvailable sources:")
	for _, source := range sources {
		fmt.Printf("Source: %s, Priority: %.1f, Type: %s\n", source.URL, source.Priority, source.Type)
	}

	// Find the highest priority source
	var bestSource *api.EpisodeSource
	for _, source := range sources {
		if bestSource == nil || source.Priority > bestSource.Priority {
			bestSource = &source
		}
	}

	if bestSource == nil {
		fmt.Println("No sources available")
		return
	}

	// Play the best source
	fmt.Printf("Playing episode %d with source: %s\n", episodeNum, bestSource.Type)
	fmt.Printf("URL: %s\n", bestSource.URL)

	// Play using mpv with proper arguments
	playEpisode(bestSource.URL, selectedAnime.Title, episodeNum, false)
}

// 	return
// }

// // // 	// Display results
// // // 	if len(results) == 0 {
// // // 		fmt.Println("No results found")
// // // 		return
// // // 	}

// // // 	// Format results for fzf
// // // 	var fzfOptions []string
// // // 	for _, result := range results {
// // // 		fzfOptions = append(fzfOptions, fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes))
// // // 	}

// // // 	// Use system fzf command to select an anime
// // // 	cmd := exec.Command("fzf", "--reverse", "--cycle", "--prompt='Select anime'")
// // // 	cmd.Stdin = strings.NewReader(strings.Join(fzfOptions, "\n"))
// // // 	var animeOut bytes.Buffer
// // // 	cmd.Stdout = &animeOut
// // // 	err = cmd.Run()
// // // 	if err != nil {
// // // 		fmt.Printf("Error using fzf: %v\n", err)
// // // 		return
// // // 	}

// // // 	if animeOut.Len() == 0 {
// // // 		fmt.Println("No selection made")
// // // 		return
// // // 	}

// // // 	// Convert selected string to string
// // // 	selected := strings.TrimSpace(animeOut.String())
// // // 	if selected == "" {
// // // 		fmt.Println("No selection made")
// // // 		return
// // // 	}

// // // 	// Find the selected anime in our results
// // // 	selectedAnime := results[0] // Default to first result
// // // 	for _, result := range results {
// // // 		formatted := fmt.Sprintf("%s (%d episodes)", result.Title, result.Episodes)
// // // 		if formatted == selected {
// // // 			selectedAnime = result
// // // 			break
// // // 		}
// // // 	}

// // // 	fmt.Printf("Selected: %s\n", selectedAnime.Title)

// // // 	// Get episodes for the selected anime
// // // 	episodes, err := client.GetEpisodes(selectedAnime.ID)
// // // 	if err != nil {
// // // 		fmt.Printf("Error getting episodes: %v\n", err)
// // // 		return
// // // 	}

// // // 	// Create fzf options
// // // 	var episodeOptions []string
// // // 	for _, ep := range episodes {
// // // 		subs := ep.AvailableEpisodes["sub"]
// // // 		dubs := ep.AvailableEpisodes["dub"]
// // // 		subsCount := len(subs)
// // // 		dubsCount := len(dubs)

// // // 		episodeOptions = append(episodeOptions, fmt.Sprintf("Episode %s (Subs: %d, Dubs: %d)", ep.EpisodeNumber, subsCount, dubsCount))
// // // 	}

// // // 	// Display available episodes
// // // 	fmt.Println("\nAvailable episodes:")
// // // 	for _, option := range episodeOptions {
// // // 		fmt.Println(option)
// // // 	}

// // // 	// Use fzf to select an episode
// // // 	episodeCmd := exec.Command("fzf", "--reverse", "--cycle", "--prompt='Select episode'")
// // // 	episodeCmd.Stdin = strings.NewReader(strings.Join(episodeOptions, "\n"))
// // // 	var episodeOut bytes.Buffer
// // // 	episodeCmd.Stdout = &episodeOut
// // // 	err = episodeCmd.Run()
// // // 	if err != nil {
// // // 		fmt.Printf("Error using fzf: %v\n", err)
// // // 		return
// // // 	}

// // // 	selectedEpisode := strings.TrimSpace(episodeOut.String())
// // // 	if selectedEpisode == "" {
// // // 		fmt.Println("No episode selected")
// // // 		return
// // // 	}

// // // 	// Extract episode number from the selected string
// // // 	episodeNum, err = strconv.Atoi(strings.Split(selectedEpisode, " ")[1])
// // // 	cmdEpisodeNum = strconv.Itoa(episodeNum)
// // // 	if err != nil {
// // // 		fmt.Printf("Error: Invalid episode number: %v\n", err)
// // // 		return
// // // 	}

// // // 	// Get episode URLs
// // // 	sources, err := client.GetEpisodeURL(selectedAnime.ID, mode, cmdEpisodeNum)
// // // 	if err != nil {
// // // 		fmt.Printf("Error getting episode sources: %v\n", err)
// // // 		return
// // // 	}

// // // 	// Display available sources
// // // 	fmt.Println("\nAvailable sources:")
// // // 	for _, source := range sources {
// // // 		fmt.Printf("Source: %s, Priority: %.1f, Type: %s\n", source.URL, source.Priority, source.Type)
// // // 	}

// // // 	// Find the highest priority source
// // // 	var bestSource *api.EpisodeSource
// // // 	for _, source := range sources {
// // // 		if bestSource == nil || source.Priority > bestSource.Priority {
// // // 			bestSource = &source
// // // 		}
// // // 	}

// // // 	if bestSource == nil {
// // // 		fmt.Println("No sources available")
// // // 		return
// // // 	}

// // // 	// Play the best source
// // // 	fmt.Printf("Playing episode %d with source: %s\n", episodeNum, bestSource.Type)
// // // 	fmt.Printf("URL: %s\n", bestSource.URL)

// // // 	// Check if URL is a direct video URL or needs to be decoded
// // // 	// Play using mpv with proper arguments
// // // 	playEpisode(bestSource.URL, selectedAnime.Title, episodeNum, false)
// // // }

// // // return
// // // }

func decodeAllAnimeURL(encodedURL string) string {
	// Remove the -- prefix
	encodedURL = strings.TrimPrefix(encodedURL, "--")

	// Create the decoding map
	decodeMap := map[string]string{
		"79": "A", "7a": "B", "7b": "C", "7c": "D", "7d": "E", "7e": "F", "7f": "G",
		"70": "H", "71": "I", "72": "J", "73": "K", "74": "L", "75": "M", "76": "N", "77": "O",
		"68": "P", "69": "Q", "6a": "R", "6b": "S", "6c": "T", "6d": "U", "6e": "V", "6f": "W",
		"60": "X", "61": "Y", "62": "Z", "59": "a", "5a": "b", "5b": "c", "5c": "d", "5d": "e",
		"5e": "f", "5f": "g", "50": "h", "51": "i", "52": "j", "53": "k", "54": "l", "55": "m",
		"56": "n", "57": "o", "48": "p", "49": "q", "4a": "r", "4b": "s", "4c": "t", "4d": "u",
		"4e": "v", "4f": "w", "40": "x", "41": "y", "42": "z", "08": "0", "09": "1", "0a": "2",
		"0b": "3", "0c": "4", "0d": "5", "0e": "6", "0f": "7", "00": "8", "01": "9", "15": "-",
		"16": ".", "67": "_", "46": "~", "02": ":", "17": "/", "07": "?", "1b": "#", "63": "[",
		"65": "]", "78": "@", "19": "!", "1c": "$", "1e": "&", "10": "(", "11": ")", "12": "*",
		"13": "+", "14": ",", "03": ";", "05": "=", "1d": "%",
	}

	// Decode using the mapping
	var decoded strings.Builder
	for i := 0; i < len(encodedURL); i += 2 {
		chunk := encodedURL[i : i+2]
		if val, ok := decodeMap[chunk]; ok {
			decoded.WriteString(val)
		} else {
			decoded.WriteString(chunk)
		}
	}

	// Clean up any special characters that might cause issues
	decodedStr := decoded.String()
	decodedStr = strings.ReplaceAll(decodedStr, "\\", "")
	decodedStr = strings.ReplaceAll(decodedStr, "\u002F", "/")

	// Handle different providers with switch-case
	switch {
	case strings.Contains(decodedStr, "sharepoint"):
		// SharePoint provider - extract the URL after the colon
		if strings.Contains(decodedStr, ":") {
			return strings.Split(decodedStr, ":")[1]
		}
		return decodedStr
	case strings.Contains(decodedStr, "Ak"):
		if !strings.HasPrefix(decodedStr, "http") {
			decodedStr = "https://blog.allanime.day/" + decodedStr
		}
		// For Ak sources, we need to get the subtitle URL by replacing /clock with /clock.json
		if strings.Contains(decodedStr, "/clock") {
			return strings.Replace(decodedStr, "/clock", "/clock.json", 1)
		}
		return decodedStr
	case strings.Contains(decodedStr, "repackager.wixmp.com"):
		// Wixmp provider - extract the actual URL
		extractLink := strings.Replace(decodedStr, "repackager.wixmp.com/", "", 1)
		extractLink = strings.TrimSuffix(extractLink, ".urlset")
		return extractLink
	case strings.Contains(decodedStr, "vipanicdn") || strings.Contains(decodedStr, "anifastcdn"):
		// VIPanicDN or AnifastCDN provider
		if strings.Contains(decodedStr, "original.m3u") {
			// If it's an m3u8 playlist, return as is
			return decodedStr
		}
		// Extract the base URL and relative paths
		if strings.Contains(decodedStr, ">") {
			extractLink := strings.Split(decodedStr, ">")[1]
			if strings.HasPrefix(extractLink, "/") {
				// If it's a relative path, get the base URL
				baseURL := strings.Split(decodedStr, ">")[0]
				baseURL = strings.TrimSuffix(baseURL, "/")
				return baseURL + extractLink
			}
			return extractLink
		}
		return decodedStr
	case strings.Contains(decodedStr, "youtube"):
		// YouTube provider - already decoded, return as is
		return decodedStr
	default:
		// Default case - handle clock.json URLs
		if strings.Contains(decodedStr, "/clock.json") {
			// Remove any duplicate clock.json references
			decodedStr = strings.ReplaceAll(decodedStr, "/clock.json", "")
			// Add base URL if needed
			if !strings.HasPrefix(decodedStr, "http") {
				return "https://blog.allanime.day/apivtwo/clock.json?id=" + decodedStr
			}
			return decodedStr
		}
		// For other providers, return the decoded URL as is
		return decodedStr
	}
}

func playEpisode(url string, title string, epNo int, noDetach bool) {
	// First decode the URL to get the actual video URL
	videoUrl := decodeAllAnimeURL(url)
	if videoUrl == "" {
		fmt.Println("Failed to decode URL")
		return
	}

	// Get the audio URL from the video URL's JSON response
	audioUrl := ""

	// Parse the JSON response to get the audio URL
	resp, err := http.Get(videoUrl)
	if err == nil {
		body, err := io.ReadAll(resp.Body)
		if err == nil {
			var data struct {
				Links []struct {
					RawUrls struct {
						Audios []struct {
							Url       string `json:"url"`
							Bandwidth int    `json:"bandwidth"`
						} `json:"audios"`
					} `json:"rawUrls"`
				} `json:"links"`
			}

			if err := json.Unmarshal(body, &data); err == nil {
				// Use the highest quality audio track
				var bestAudioUrl string
				var bestAudioBandwidth int
				if len(data.Links) > 0 && len(data.Links[0].RawUrls.Audios) > 0 {
					for _, audio := range data.Links[0].RawUrls.Audios {
						if audio.Bandwidth > bestAudioBandwidth {
							bestAudioBandwidth = audio.Bandwidth
							bestAudioUrl = audio.Url
						}
					}
					audioUrl = bestAudioUrl
				}
			}
		}
	}

	// Set up mpv arguments
	args := []string{
		"-config-dir=F:\\PortableApps\\mpv",
		"--profile=anime",
		"--force-media-title=" + title + " Episode " + strconv.Itoa(epNo),
	}

	// Add subtitle URL if available
	// For Ak sources, we need to get the subtitle URL by replacing /clock with /clock.json
	subtitleUrl := decodeAllAnimeURL(strings.Replace(videoUrl, "/clock", "/clock.json", 1))
	if subtitleUrl != "" {
		args = append(args, "--sub-file="+subtitleUrl)
	}

	// Add audio file if we have one
	if audioUrl != "" {
		args = append(args, "--audio-file="+audioUrl)
	}

	// Add video URL as the last argument
	args = append(args, videoUrl)

	// Create mpv command with arguments
	cmd := exec.Command("mpv", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// Start and wait for mpv
	fmt.Println("Starting playback...")
	if err := cmd.Start(); err != nil {
		fmt.Printf("Error starting mpv: %v\n", err)
		return
	}

	// Wait for mpv to complete
	if err := cmd.Wait(); err != nil {
		fmt.Printf("Error during playback: %v\n", err)
	}
}
