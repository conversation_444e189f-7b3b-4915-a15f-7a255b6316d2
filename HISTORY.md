# History System

## Overview

The Go-Ani history system uses SQLite for reliable data storage with automatic backup functionality to prevent data loss from corruption or power outages.

## Features

- **SQLite Database**: Reliable, ACID-compliant storage
- **Automatic Backups**: Created before every write operation
- **Corruption Recovery**: Automatic backup restoration on database corruption
- **Continue Watching**: Resume from where you left off
- **Episode Tracking**: Track watched episodes per anime
- **Quality & Mode Persistence**: Remember preferred settings

## Database Schema

```sql
CREATE TABLE history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anime_id TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    current_ep INTEGER DEFAULT 1,
    total_eps INTEGER DEFAULT 0,
    watched_eps TEXT DEFAULT '[]',  -- JSON array of watched episode numbers
    last_watched DATETIME DEFAULT CURRENT_TIMESTAMP,
    quality TEXT DEFAULT '1080p',
    mode TEXT DEFAULT 'sub',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Usage

### Continue Watching
```bash
go-ani --continue
```
Shows a list of anime you can continue watching, sorted by most recently watched.

### Manual History Management
```bash
# List all history entries
go run cmd/history/main.go --list

# Create manual backup
go run cmd/history/main.go --backup

# Restore from backup
go run cmd/history/main.go --restore

# Clear all history
go run cmd/history/main.go --clear
```

## File Locations

- **Database**: `~/.go-ani/history.db`
- **Backup**: `~/.go-ani/history.db.backup`
- **WAL File**: `~/.go-ani/history.db-wal` (SQLite Write-Ahead Log)
- **SHM File**: `~/.go-ani/history.db-shm` (SQLite Shared Memory)

## Backup Strategy

1. **Automatic Backups**: Created before every write operation
2. **WAL Mode**: SQLite Write-Ahead Logging for better concurrency and crash recovery
3. **Corruption Detection**: Automatic backup restoration if database becomes corrupted
4. **Manual Backups**: Available via CLI utility

## Advantages over ani-cli

| Feature | ani-cli | go-ani |
|---------|---------|---------|
| Storage | Plain text file | SQLite database |
| Backup | None | Automatic + manual |
| Corruption Recovery | None | Automatic restoration |
| Concurrent Access | Unsafe | Safe with WAL mode |
| Data Integrity | None | ACID compliance |
| Episode Tracking | Basic | Detailed with JSON arrays |
| Metadata | Limited | Rich (quality, mode, timestamps) |
| Performance | O(n) file parsing | O(1) indexed queries |

## Error Handling

- **Database Corruption**: Automatically restores from backup
- **Missing Backup**: Creates new empty database
- **Write Failures**: Preserves existing data
- **Power Outages**: WAL mode ensures data consistency

## Migration from ani-cli

The system can be extended to import ani-cli history:

```go
// Example migration function (not implemented)
func MigrateFromAniCli(histFile string, db *database.DB) error {
    // Parse ani-cli history file
    // Convert to HistoryEntry structs
    // Import into SQLite database
}
```