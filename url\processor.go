package url

import (
	"fmt"
	"strings"
	"strconv"
	"net/http"
	"io"
	"encoding/json"
	"sort"

	"github.com/stl3/go-ani/provider"
)

// URLProcessor handles URL processing and quality selection
type URLProcessor struct {
	quality string
}

// NewProcessor creates a new URL processor
func NewProcessor(quality string) *URLProcessor {
	return &URLProcessor{
		quality: quality,
	}
}

// ProcessURL processes a URL and returns the best quality version
func (p *URLProcessor) ProcessURL(rawURL string) (string, error) {
	// First, check if it's a YouTube URL (starts with --)
	if strings.HasPrefix(rawURL, "--") {
		return p.processYouTubeURL(rawURL)
	}

	// Create provider manager
	manager := provider.NewProviderManager(p.quality)

	// Get all providers for this URL
	providers, err := p.getProviders(rawURL)
	if err != nil {
		return "", err
	}

	// Add providers to manager
	for _, prov := range providers {
		manager.AddProvider(prov)
	}

	// Select best quality provider
	selected := manager.SelectQuality()
	if selected == nil {
		return "", fmt.Errorf("no suitable providers found")
	}

	// Process the selected provider's URL
	url, err := provider.ProcessURL(selected.URL)
	if err != nil {
		return "", err
	}

	return url, nil
}

// processYouTubeURL handles YouTube URLs with special base64 decoding
func (p *URLProcessor) processYouTubeURL(url string) (string, error) {
	// Extract base64 encoded part
	base64Part := strings.TrimPrefix(url, "--")

	// Decode using specific mapping
	mapping := map[string]string{
		"01": "9",
		"08": "0",
		"05": "=",
		"0a": "2",
		"0b": "3",
		"0c": "4",
		"07": "?",
		"00": "8",
		"5c": "d",
		"0f": "7",
		"5e": "f",
		"17": "/",
		"54": "l",
		"09": "1",
		"48": "p",
		"4f": "w",
		"0e": "6",
		"5b": "c",
		"5d": "e",
		"0d": "5",
		"53": "k",
		"1e": "&",
		"5a": "b",
		"59": "a",
		"4a": "r",
		"4c": "t",
		"4e": "v",
		"57": "o",
		"51": "i",
	}

	decoded := ""
	for i := 0; i < len(base64Part); i += 2 {
		key := base64Part[i : i+2]
		if val, ok := mapping[key]; ok {
			decoded += val
		} else {
			decoded += string(base64Part[i])
		}
	}

	// Add /clock.json to the end
	return decoded + "/clock.json", nil
}

// getProviders retrieves all available providers for a URL
func (p *URLProcessor) getProviders(rawURL string) ([]provider.Provider, error) {
	// First try to extract links using the extractor
	extractor := NewExtractor()
	
	// Get quality and priority from the URL (if available)
	quality := p.quality
	var priority float64 = 0
	
	// Try to parse priority from URL if it exists
	if strings.Contains(rawURL, "priority=") {
		parts := strings.Split(rawURL, "priority=")
		if len(parts) > 1 {
			priorityStr := strings.Split(parts[1], "&")[0]
			priority, _ = strconv.ParseFloat(priorityStr, 64)
		}
	}
	
	// Handle clock.json URLs
	if strings.HasSuffix(rawURL, "/clock.json") {
		// Make request to get actual video URL
		req, err := http.NewRequest("GET", rawURL, nil)
		if err != nil {
			return nil, fmt.Errorf("error creating request: %v", err)
		}
		req.Header.Set("User-Agent", "Mozilla/5.0")
		req.Header.Set("Accept", "*/*")
		
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("error fetching clock.json: %v", err)
		}
		defer resp.Body.Close()
		
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("error reading response body: %v", err)
		}
		
		// Extract URL from response
		if strings.Contains(string(body), "url=") {
			urlParts := strings.Split(string(body), "url=")
			if len(urlParts) > 1 {
				return []provider.Provider{{URL: urlParts[1]}}, nil
			}
		}
		return nil, fmt.Errorf("no URL found in clock.json response")
	}
	
	// Handle embed URLs
	if strings.Contains(rawURL, "vidstreaming.io") || strings.Contains(rawURL, "streamwish.to") {
		// Make request to get embed page
		req, err := http.NewRequest("GET", rawURL, nil)
		if err != nil {
			return nil, fmt.Errorf("error creating request: %v", err)
		}
		req.Header.Set("User-Agent", "Mozilla/5.0")
		req.Header.Set("Accept", "text/html")
		
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("error fetching embed page: %v", err)
		}
		defer resp.Body.Close()
		
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("error reading response body: %v", err)
		}
		
		// Extract video URL from embed page
		if strings.Contains(string(body), "sources:") {
			// Extract JSON from sources array
			start := strings.Index(string(body), "sources:")
			if start != -1 {
				end := strings.Index(string(body)[start:], "]")
				if end != -1 {
					jsonStr := string(body)[start+9 : start+end+1]
					
					// Parse JSON
					var sources []struct {
						File string `json:"file"`
						Type string `json:"type"`
						Label string `json:"label"`
					}
					if err := json.Unmarshal([]byte(jsonStr), &sources); err == nil {
						// Find best quality source
						bestQuality := 0
						bestURL := ""
						for _, src := range sources {
							if src.File != "" {
								qualityNum := 1080 // Default to 1080p
								if src.Label != "" {
									if num, err := strconv.Atoi(strings.TrimSuffix(src.Label, "p")); err == nil {
										qualityNum = num
									}
								}
								if qualityNum > bestQuality {
									bestQuality = qualityNum
									bestURL = src.File
								}
							}
						}
						if bestURL != "" {
							return []provider.Provider{{URL: bestURL}}, nil
						}
					}
				}
			}
		}
		return nil, fmt.Errorf("no video URL found in embed page")
	}
	
	// Extract links from providers
	providers, err := extractor.ExtractLinks(rawURL, quality, priority)
	if err != nil {
		return nil, err
	}

	if len(providers) == 0 {
		return nil, fmt.Errorf("no providers found")
	}

	// Sort providers by priority (higher numbers first)
	sort.Slice(providers, func(i, j int) bool {
		return providers[i].Priority > providers[j].Priority
	})

	// Sort providers by quality (higher numbers first)
	for i := 0; i < len(providers)-1; i++ {
		for j := i + 1; j < len(providers); j++ {
			if providers[i].Quality == "" || providers[j].Quality == "" {
				continue
			}
			
			// Extract quality numbers (e.g., "1080p" -> 1080)
			iQual, _ := strconv.Atoi(strings.TrimSuffix(providers[i].Quality, "p"))
			jQual, _ := strconv.Atoi(strings.TrimSuffix(providers[j].Quality, "p"))
			
			if iQual < jQual {
				providers[i], providers[j] = providers[j], providers[i]
			}
		}
	}

	// If specific quality requested, try to find it
	if quality != "" {
		for _, prov := range providers {
			if strings.Contains(prov.Quality, quality) {
				return []provider.Provider{{URL: prov.URL}}, nil
			}
		}
	}

	// If no specific quality found, return the highest priority provider
	return []provider.Provider{{URL: providers[0].URL}}, nil
}
