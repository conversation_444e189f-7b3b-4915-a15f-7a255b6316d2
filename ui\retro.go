package ui

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

var (
	// Retro green color scheme
	retroGreen = lipgloss.Color("#00FF00")
	retroDark  = lipgloss.Color("#001100")

	// Styles
	titleStyle = lipgloss.NewStyle().
			Foreground(retroGreen).
			Background(retroDark).
			Bold(true).
			Padding(1, 2).
			Border(lipgloss.DoubleBorder()).
			BorderForeground(retroGreen)

	promptStyle = lipgloss.NewStyle().
			Foreground(retroGreen).
			Bold(true)

	textStyle = lipgloss.NewStyle().
			Foreground(retroGreen)

	errorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#FF0000")).
			Bold(true)
)

type Theme struct {
	Name       string         `json:"name"`
	Primary    lipgloss.Color `json:"primary"`
	Background lipgloss.Color `json:"background"`
	Prompt     string         `json:"prompt"`
	Header     []string       `json:"header"`
}

type ThemeConfig struct {
	CurrentTheme int `json:"current_theme"`
}

type Model struct {
	textInput    textinput.Model
	prompt       string
	output       []string
	cursor       int
	width        int
	height       int
	currentTheme int
	themes       []Theme
}

func NewModel() Model {
	ti := textinput.New()
	ti.Placeholder = "Enter command..."
	ti.Focus()
	ti.CharLimit = 156
	ti.Width = 50

	themes := loadThemes()

	m := Model{
		textInput:    ti,
		themes:       themes,
		currentTheme: loadThemeConfig(),
	}
	// Validate theme index
	if m.currentTheme >= len(themes) {
		m.currentTheme = 0
	}
	m.applyTheme()
	return m
}

func (m Model) Init() tea.Cmd {
	return textinput.Blink
}

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.Type {
		case tea.KeyCtrlC, tea.KeyEsc:
			return m, tea.Quit
		case tea.KeyEnter:
			input := strings.TrimSpace(m.textInput.Value())
			if input != "" {
				m.output = append(m.output, m.prompt+input)
				if m.shouldLaunchCLI(input) {
					m.launchCLI(input)
					return m, tea.Quit
				} else {
					m.output = append(m.output, m.processCommand(input)...)
				}
				m.textInput.SetValue("")
			}
		}
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
	}

	m.textInput, cmd = m.textInput.Update(msg)
	return m, cmd
}

func (m *Model) applyTheme() {
	theme := m.themes[m.currentTheme]
	m.prompt = theme.Prompt
	m.output = make([]string, len(theme.Header))
	copy(m.output, theme.Header)

	// Update styles
	titleStyle = titleStyle.Foreground(theme.Primary).Background(theme.Background).BorderForeground(theme.Primary)
	promptStyle = promptStyle.Foreground(theme.Primary)
	textStyle = textStyle.Foreground(theme.Primary)
}

func (m *Model) processCommand(input string) []string {
	parts := strings.Fields(strings.ToLower(input))
	command := parts[0]

	switch command {
	case "help", "kawaii-help":
		return []string{
			"AVAILABLE COMMANDS:",
			"  search <anime>  - Search and watch anime",
			"  continue        - Continue watching from history",
			"  history         - Browse watch history",
			"  download <anime> - Download anime episodes",
			"  status          - System status",
			"  theme           - Theme switcher",
			"  clear           - Clear terminal",
			"  exit            - Exit system",
			"",
		}
	case "download":
		if len(parts) < 2 {
			return []string{"Usage: download <anime_name>", ""}
		}
		query := strings.Join(parts[1:], " ")
		return m.handleDownload(query)
	case "search":
		if len(parts) < 2 {
			return []string{"Usage: search <anime_name>", ""}
		}
		query := strings.Join(parts[1:], " ")
		return m.handleSearch(query)
	case "continue":
		return m.handleContinue()
	case "history":
		return m.handleHistory()
	case "status":
		return []string{
			"SYSTEM STATUS:",
			fmt.Sprintf("  TIME: %s", time.Now().Format("15:04:05")),
			"  CONNECTION: STABLE",
			"  STREAMING: READY",
			"  DATABASE: ONLINE",
			"",
		}
	case "theme":
		if len(parts) < 2 {
			return m.showThemeSelector()
		}
		themeName := parts[1]
		// Try by name first
		for i, theme := range m.themes {
			if theme.Name == themeName {
				m.currentTheme = i
				saveThemeConfig(i)
				clearScreen()
				m.applyTheme()
				return []string{fmt.Sprintf("Theme changed to: %s", themeName), ""}
			}
		}
		// Try by number
		if themeNum := parseThemeNumber(themeName); themeNum > 0 && themeNum <= len(m.themes) {
			m.currentTheme = themeNum - 1
			saveThemeConfig(themeNum - 1)
			clearScreen()
			m.applyTheme()
			return []string{fmt.Sprintf("Theme changed to: %s", m.themes[themeNum-1].Name), ""}
		}
		return []string{fmt.Sprintf("Unknown theme: %s", themeName), "Type 'theme' to see available options", ""}
	case "clear":
		m.applyTheme()
		return []string{}
	case "exit":
		return []string{"SYSTEM SHUTDOWN INITIATED..."}
	default:
		return []string{
			fmt.Sprintf("UNKNOWN COMMAND: %s", input),
			"Type 'help' for available commands",
			"",
		}
	}
}

func (m *Model) showThemeSelector() []string {
	var result []string
	result = append(result, "THEME SELECTOR:")
	result = append(result, "")
	for i, theme := range m.themes {
		if i == m.currentTheme {
			result = append(result, fmt.Sprintf("  [%d] %s ← CURRENT", i+1, theme.Name))
		} else {
			result = append(result, fmt.Sprintf("  [%d] %s", i+1, theme.Name))
		}
	}
	result = append(result, "")
	result = append(result, "Usage: theme <name> or theme <number>")
	result = append(result, "")
	return result
}

func (m *Model) handleSearch(query string) []string {
	return []string{
		fmt.Sprintf("Launching search for '%s'...", query),
		"Press any key to continue after selection...",
		"",
	}
}

func (m *Model) handleContinue() []string {
	return []string{
		"Launching continue watching...",
		"Press any key to continue after selection...",
		"",
	}
}

func (m *Model) handleHistory() []string {
	return []string{
		"Launching history menu...",
		"Press any key to continue after selection...",
		"",
	}
}

func (m Model) View() string {
	var b strings.Builder

	// Title
	b.WriteString(titleStyle.Render("GO-ANI TERMINAL v1.0"))
	b.WriteString("\n\n")

	// Output history
	for _, line := range m.output {
		if strings.HasPrefix(line, "UNKNOWN COMMAND") || strings.HasPrefix(line, "ERROR") {
			b.WriteString(errorStyle.Render(line))
		} else {
			b.WriteString(textStyle.Render(line))
		}
		b.WriteString("\n")
	}

	// Input prompt
	b.WriteString(promptStyle.Render(m.prompt))
	b.WriteString(m.textInput.View())

	return b.String()
}

func loadThemeConfig() int {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return 0
	}
	configPath := filepath.Join(homeDir, ".go-ani", "retro-theme.json")
	data, err := os.ReadFile(configPath)
	if err != nil {
		return 0
	}
	var config ThemeConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return 0
	}
	return config.CurrentTheme
}

func saveThemeConfig(themeIndex int) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return
	}
	configDir := filepath.Join(homeDir, ".go-ani")
	os.MkdirAll(configDir, 0755)
	configPath := filepath.Join(configDir, "retro-theme.json")
	config := ThemeConfig{CurrentTheme: themeIndex}
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return
	}
	os.WriteFile(configPath, data, 0644)
}

func loadThemes() []Theme {
	// Try to load from user config first
	homeDir, err := os.UserHomeDir()
	if err == nil {
		userThemesPath := filepath.Join(homeDir, ".go-ani", "themes.json")
		if themes := loadThemesFromFile(userThemesPath); themes != nil {
			return themes
		}
	}

	// Try to load from current directory
	if themes := loadThemesFromFile("ui/themes.json"); themes != nil {
		return themes
	}

	// Fallback to default theme if file loading fails
	return []Theme{{
		Name:       "cyberpunk",
		Primary:    lipgloss.Color("#00FFFF"),
		Background: lipgloss.Color("#001122"),
		Prompt:     "🌌 NeoAnimeOS > ",
		Header:     []string{"GO-ANI TERMINAL", "Type 'help' for commands", ""},
	}}
}

func loadThemesFromFile(path string) []Theme {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil
	}
	var themes []Theme
	if err := json.Unmarshal(data, &themes); err != nil {
		return nil
	}
	return themes
}

func (m *Model) handleDownload(query string) []string {
	return []string{
		fmt.Sprintf("Launching download for '%s'...", query),
		"Press any key to continue after selection...",
		"",
	}
}

func parseThemeNumber(s string) int {
	switch s {
	case "1":
		return 1
	case "2":
		return 2
	case "3":
		return 3
	case "4":
		return 4
	case "5":
		return 5
	case "6":
		return 6
	case "7":
		return 7
	case "8":
		return 8
	default:
		return 0
	}
}

func (m *Model) shouldLaunchCLI(input string) bool {
	parts := strings.Fields(strings.ToLower(input))
	if len(parts) == 0 {
		return false
	}
	command := parts[0]
	return command == "search" || command == "continue" || command == "history" || command == "download"
}

func (m *Model) launchCLI(input string) {
	parts := strings.Fields(input)
	command := strings.ToLower(parts[0])

	var cmd *exec.Cmd
	switch command {
	case "search":
		if len(parts) > 1 {
			query := strings.Join(parts[1:], " ")
			cmd = exec.Command("go", "run", "main.go", query)
		}
	case "continue":
		cmd = exec.Command("go", "run", "main.go", "-continue")
	case "history":
		cmd = exec.Command("go", "run", "main.go", "-continue")
	case "download":
		if len(parts) > 1 {
			query := strings.Join(parts[1:], " ")
			cmd = exec.Command("go", "run", "main.go", "-download", query)
		}
	}

	if cmd != nil {
		cmd.Stdin = os.Stdin
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		cmd.Run()
	}
}

func clearScreen() {
	fmt.Print("\033[2J\033[H")
}

func RunRetroUI() error {
	p := tea.NewProgram(NewModel(), tea.WithAltScreen())
	_, err := p.Run()
	return err
}
