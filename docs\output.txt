+ version_number=4.10.1
+ agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ allanime_refr=https://allmanga.to
+ allanime_base=allanime.day
+ allanime_api=https://api.allanime.day
+ mode=sub
+ download_dir=.
+ log_episode=1
+ quality=best
+ case "$(uname -a | cut -d " " -f 1,3-)" in
++ uname -a
++ cut -d ' ' -f 1,3-
+ player_function=mpv.exe
+ no_detach=0
+ exit_after_play=0
+ use_external_menu=0
+ external_menu_normal_window=0
+ skip_intro=0
+ skip_title=
+ '[' -t 0 ']'
+ hist_dir=/c/Users/<USER>/.local/state/ani-cli
+ '[' '!' -d /c/Users/<USER>/.local/state/ani-cli ']'
+ histfile=/c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' '!' -f /c/Users/<USER>/.local/state/ani-cli/ani-hsts ']'
+ search=scrape
+ '[' 3 -gt 0 ']'
+ case "$1" in
+ '[' 3 -lt 2 ']'
+ quality=1080p
+ shift
+ shift
+ '[' 1 -gt 0 ']'
+ case "$1" in
++ printf %s ' tokyo magnitude'
++ sed 's|^ ||;s| |+|g'
+ query=tokyo+magnitude
+ shift
+ '[' 0 -gt 0 ']'
+ '[' 0 = 0 ']'
+ multi_selection_flag=-m
+ '[' 0 = 1 ']'
+ '[' 0 = 1 ']'
+ printf '\33[2K\r\033[1;34mChecking dependencies...\033[0m\n'
[2K
[1;34mChecking dependencies...[0m
+ dep_ch curl sed grep
+ for dep in "$@"
+ command -v curl
+ for dep in "$@"
+ command -v sed
+ for dep in "$@"
+ command -v grep
+ '[' 0 = 1 ']'
+ dep_ch fzf
+ for dep in "$@"
+ command -v fzf
+ case "$player_function" in
+ dep_ch mpv.exe
+ for dep in "$@"
+ command -v mpv.exe
+ case "$search" in
+ '[' 0 = 0 ']'
+ '[' -z tokyo+magnitude ']'
+ '[' scrape = nextep ']'
++ printf %s tokyo+magnitude
++ sed 's| |+|g'
+ query=tokyo+magnitude
++ search_anime tokyo+magnitude
++ search_gql='query( $search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType ) { shows( search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin ) { edges { _id name availableEpisodes __typename } }}'
++ curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"search":{"allowAdult":false,"allowUnknown":false,"query":"tokyo+magnitude"},"limit":40,"page":1,"translationType":"sub","countryOrigin":"ALL"}' --data-urlencode 'query=query( $search: SearchInput $limit: Int $page: Int $translationType: VaildTranslationTypeEnumType $countryOrigin: VaildCountryOriginEnumType ) { shows( search: $search limit: $limit page: $page translationType: $translationType countryOrigin: $countryOrigin ) { edges { _id name availableEpisodes __typename } }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed 's|Show|\
| g'
++ sed -nE 's|.*_id":"([^"]*)","name":"(.+)",.*sub":([1-9][^,]*).*|\1	\2 (\3 episodes)|p'
++ sed 's/\\"//g'
+ anime_list='bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
+ '[' -z 'bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)' ']'
+ '[' '' -eq '' ']'
+ '[' -z '' ']'
++ printf %s 'bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
++ nl -w 2
++ sed 's/^[[:space:]]//'
++ nth 'Select anime: '
+++ cat -
++ stdin='1	bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
++ '[' -z '1	bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)' ']'
+++ printf '%s\n' '1	bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=1
++ '[' 1 -eq 1 ']'
++ printf %s '1	bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
++ cut -f2,3
++ return 0
+ result='bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
+ '[' -z 'bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)' ']'
++ printf %s 'bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
++ cut -f2
+ title='Tokyo Magnitude 8.0 (11 episodes)'
++ printf %s 'Tokyo Magnitude 8.0 (11 episodes)'
++ cut '-d(' -f1
++ tr -d '[:punct:]'
+ allanime_title='Tokyo Magnitude 80 '
++ printf %s 'bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)'
++ cut -f1
+ id=bWwhdbtF8NAaqwNwx
++ episodes_list bWwhdbtF8NAaqwNwx
++ episodes_list_gql='query ($showId: String!) { show( _id: $showId ) { _id availableEpisodesDetail }}'
++ curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"bWwhdbtF8NAaqwNwx"}' --data-urlencode 'query=query ($showId: String!) { show( _id: $showId ) { _id availableEpisodesDetail }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ sed -nE 's|.*sub":\[([0-9.",]*)\].*|\1|p'
++ sed 's|,|\
|g; s|"||g'
++ sort -n -k 1
+ ep_list='1
2
3
4
5
6
7
8
9
10
11'
+ '[' -z '' ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
++ nth 'Select episode: ' -m
+++ cat -
++ stdin='1
2
3
4
5
6
7
8
9
10
11'
++ '[' -z '1
2
3
4
5
6
7
8
9
10
11' ']'
+++ printf '%s\n' '1
2
3
4
5
6
7
8
9
10
11'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=11
++ '[' 11 -eq 1 ']'
++ prompt='Select episode: '
++ multi_flag=
++ '[' 2 -ne 1 ']'
++ shift
++ multi_flag=-m
+++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher -m 'Select episode: '
+++ '[' 0 = 0 ']'
+++ '[' -z -m ']'
+++ '[' 0 = 0 ']'
+++ fzf -m --reverse --cycle --prompt 'Select episode: '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=1
+++ printf %s 1
+++ head -n1
++ line_start=1
+++ printf %s 1
+++ tail -n1
++ line_end=1
++ '[' -n 1 ']'
++ '[' 1 = 1 ']'
++ printf %s '1
2
3
4
5
6
7
8
9
10
11'
++ grep -E '^1($|[[:space:]])'
++ cut -f2,3
+ ep_no=1
+ '[' -z 1 ']'
+ '[' 0 = 1 ']'
+ tput cuu1
[A+ tput el
[K+ tput sc
7+ play
++ printf %s 1
++ grep -Eo '^(-1|[0-9]+(\.[0-9]+)?)'
+ start=1
++ printf %s 1
++ grep -Eo '(-1|[0-9]+(\.[0-9]+)?)$'
+ end=1
+ '[' 1 = -1 ']'
+ '[' -z 1 ']'
+ '[' 1 = 1 ']'
+ unset start end
+ '[' '' = -1 ']'
++ printf '%s\n' 1
++ wc -l
++ tr -d '[:space:]'
+ line_count=1
+ '[' 1 '!=' 1 ']'
+ '[' -n '' ']'
+ play_episode
+ '[' 1 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ command -v logger
+ '[' 0 = 1 ']'
+ '[' -z '' ']'
+ get_episode_url
+ episode_embed_gql='query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}'
++ curl -e https://allmanga.to -s -G https://api.allanime.day/api --data-urlencode 'variables={"showId":"bWwhdbtF8NAaqwNwx","translationType":"sub","episodeString":"1"}' --data-urlencode 'query=query ($showId: String!, $translationType: VaildTranslationTypeEnumType!, $episodeString: String!) { episode( showId: $showId translationType: $translationType episodeString: $episodeString ) { episodeString sourceUrls }}' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
++ tr '{}' '\n'
++ sed 's|\\u002F|\/|g;s|\\||g'
++ sed -nE 's|.*sourceUrl":"--([^"]*)".*sourceName":"([^"]*)".*|\2 :\1|p'
+ resp='Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590e174e515c5d574b175a6f4f505c5a4c7e00767959494f764f40174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0f0e000e5e0e5a0e0b0b0c0a010e0f0e000e5e0e5a0e0b0a010e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0f0b0e0b0b0b0c0b5d0b5d0b0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Uv-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0e5d0f0a0f0a0f0e0f0d0b5b0a010a010f0b0f0d0e0b0f0c0f0d0e0d0e590e010f0b0e0a0a000e0d0e010e5a0a010e0b0e5a0e0c0e0b0e0a0a5a0f0d0e5c0b0b0e0f0b090f5d0b0a0e090f0d0b5e0e5d0e0c0a000e5d0f0a0e5a0e590a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0b0f0d0e0b0f0c0f080e5e0e0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010f0a0e010e5c0f5e0e010a5a0e5a0e0f0e090e000e5e0f0a0f0b0e0a0e0b0a5a0b5d0b0e0a5a0b0a0b0d0b0e0b010e0b0f0e0b5a0b5e0b0d0b0f0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ mktemp -d
+ cache_dir=/tmp/tmp.72OheSkoWs
+ providers='1 2 3 4'
+ for provider in $providers
+ for provider in $providers
+ generate_link 1
+ case $1 in
+ provider_init wixmp '/Default :/p'
+ provider_name=wixmp
+ for provider in $providers
+ generate_link 2
+ case $1 in
+ provider_init youtube '/Yt-mp4 :/p'
+ provider_name=youtube
+ for provider in $providers
+ generate_link 3
+ case $1 in
+ provider_init sharepoint '/S-mp4 :/p'
+ provider_name=sharepoint
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590e174e515c5d574b175a6f4f505c5a4c7e00767959494f764f40174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0f0e000e5e0e5a0e0b0b0c0a010e0f0e000e5e0e5a0e0b0a010e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0f0b0e0b0b0b0c0b5d0b5d0b0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Uv-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0e5d0f0a0f0a0f0e0f0d0b5b0a010a010f0b0f0d0e0b0f0c0f0d0e0d0e590e010f0b0e0a0a000e0d0e010e5a0a010e0b0e5a0e0c0e0b0e0a0a5a0f0d0e5c0b0b0e0f0b090f5d0b0a0e090f0d0b5e0e5d0e0c0a000e5d0f0a0e5a0e590a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0b0f0d0e0b0f0c0f080e5e0e0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010f0a0e010e5c0f5e0e010a5a0e5a0e0f0e090e000e5e0f0a0f0b0e0a0e0b0a5a0b5d0b0e0a5a0b0a0b0d0b0e0b010e0b0f0e0b5a0b5e0b0d0b0f0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
+ wait
+ generate_link 4
+ case $1 in
+ provider_init hianime '/Luf-Mp4 :/p'
+ provider_name=hianime
++ sed -n '/Default :/p'
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590e174e515c5d574b175a6f4f505c5a4c7e00767959494f764f40174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0f0e000e5e0e5a0e0b0b0c0a010e0f0e000e5e0e5a0e0b0a010e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0f0b0e0b0b0b0c0b5d0b5d0b0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Uv-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0e5d0f0a0f0a0f0e0f0d0b5b0a010a010f0b0f0d0e0b0f0c0f0d0e0d0e590e010f0b0e0a0a000e0d0e010e5a0a010e0b0e5a0e0c0e0b0e0a0a5a0f0d0e5c0b0b0e0f0b090f5d0b0a0e090f0d0b5e0e5d0e0c0a000e5d0f0a0e5a0e590a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0b0f0d0e0b0f0c0f080e5e0e0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010f0a0e010e5c0f5e0e010a5a0e5a0e0f0e090e000e5e0f0a0f0b0e0a0e0b0a5a0b5d0b0e0a5a0b0a0b0d0b0e0b010e0b0f0e0b5a0b5e0b0d0b0f0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ head -1
++ sed -n '/Yt-mp4 :/p'
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590e174e515c5d574b175a6f4f505c5a4c7e00767959494f764f40174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0f0e000e5e0e5a0e0b0b0c0a010e0f0e000e5e0e5a0e0b0a010e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0f0b0e0b0b0b0c0b5d0b5d0b0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Uv-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0e5d0f0a0f0a0f0e0f0d0b5b0a010a010f0b0f0d0e0b0f0c0f0d0e0d0e590e010f0b0e0a0a000e0d0e010e5a0a010e0b0e5a0e0c0e0b0e0a0a5a0f0d0e5c0b0b0e0f0b090f5d0b0a0e090f0d0b5e0e5d0e0c0a000e5d0f0a0e5a0e590a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0b0f0d0e0b0f0c0f080e5e0e0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010f0a0e010e5c0f5e0e010a5a0e5a0e0f0e090e000e5e0f0a0f0b0e0a0e0b0a5a0b5d0b0e0a5a0b0a0b0d0b0e0b010e0b0f0e0b5a0b5e0b0d0b0f0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ sed -n '/S-mp4 :/p'
++ head -1
++ printf %s 'Yt-mp4 :504c4c484b0217174c5757544b165e594b4c0c4b485d5d5c164a4b4e481717555d5c51590e174e515c5d574b175a6f4f505c5a4c7e00767959494f764f40174b4d5a1709
S-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0a010f0d0e5e0f0a0e0b0f0d0a010e0f0e000e5e0e5a0e0b0b0c0a010e0f0e000e5e0e5a0e0b0a010e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a000e5a0f0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0d0e5d0e0f0f0c0e0b0f0e0e010e5e0e000f0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Ak :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0b0f0b0e0b0b0b0c0b5d0b5d0b0e0b0a0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0c0c0e0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Uv-mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0e5d0f0a0f0a0f0e0f0d0b5b0a010a010f0b0f0d0e0b0f0c0f0d0e0d0e590e010f0b0e0a0a000e0d0e010e5a0a010e0b0e5a0e0c0e0b0e0a0a5a0f0d0e5c0b0b0e0f0b090f5d0b0a0e090f0d0b5e0e5d0e0c0a000e5d0f0a0e5a0e590a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0d0b0f0d0e0b0f0c0f080e5e0e0a0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0a590a0c0f0a0f0c0e0f0e000f0d0e590e0f0f0a0e5e0e010e000d0a0f5e0f0e0e0b0a0c0b5b0a0c0f0d0f0b0e0c0a0c0a590a0c0e5c0e0b0f5e0a0c0b5b0a0c0e0b0f0e0a5a0e0c0d090f090e5d0e0a0e0c0f0a0c080b5d0c000c0f0e0f0f0f0f090c000f090f5d0d010b0f0d010f0d0f0b0e0c0a0c0f5a
Luf-Mp4 :175948514e4c4f57175b54575b5307515c050f5c0a0c0f0b0f0c0e590a0c0b5b0a0c0f0d0f0b0e0c0a5a0f590a5a0f090e0f0f0a0e0d0e5d0a010f0a0e010e5c0f5e0e010a5a0e5a0e0f0e090e000e5e0f0a0f0b0e0a0e0b0a5a0b5d0b0e0a5a0b0a0b0d0b0e0b010e0b0f0e0b5a0b5e0b0d0b0f0b5e0a0c0a590a0c0f0d0f0a0f0c0e0b0e0f0e5a0e0b0f0c0c5e0e0a0a0c0b5b0a0c0e000f0d0e0b0f0c0f080e0b0f0c0a0c0a590a0c0e0a0e0f0f0a0e0b0a0c0b5b0a0c0b0c0b0e0b0c0b0b0a5a0b0e0b090a5a0b0c0b0b0d0a0b0f0b5e0b5b0b0e0b0f0b5b0b0e0b0e0a000b0e0b0e0b0e0d5b0a0c0f5a'
++ head -1
++ sed -n '/Luf-Mp4 :/p'
++ head -1
++ cut -d: -f2
++ cut -d: -f2
++ cut -d: -f2
++ cut -d: -f2
++ sed 's/../&\
/g'
++ sed 's/../&\
++ sed 's/../&\
/g'
/g'
++ sed 's/../&\
/g'
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
++ sed 's/^79$/A/g;s/^7a$/B/g;s/^7b$/C/g;s/^7c$/D/g;s/^7d$/E/g;s/^7e$/F/g;s/^7f$/G/g;s/^70$/H/g;s/^71$/I/g;s/^72$/J/g;s/^73$/K/g;s/^74$/L/g;s/^75$/M/g;s/^76$/N/g;s/^77$/O/g;s/^68$/P/g;s/^69$/Q/g;s/^6a$/R/g;s/^6b$/S/g;s/^6c$/T/g;s/^6d$/U/g;s/^6e$/V/g;s/^6f$/W/g;s/^60$/X/g;s/^61$/Y/g;s/^62$/Z/g;s/^59$/a/g;s/^5a$/b/g;s/^5b$/c/g;s/^5c$/d/g;s/^5d$/e/g;s/^5e$/f/g;s/^5f$/g/g;s/^50$/h/g;s/^51$/i/g;s/^52$/j/g;s/^53$/k/g;s/^54$/l/g;s/^55$/m/g;s/^56$/n/g;s/^57$/o/g;s/^48$/p/g;s/^49$/q/g;s/^4a$/r/g;s/^4b$/s/g;s/^4c$/t/g;s/^4d$/u/g;s/^4e$/v/g;s/^4f$/w/g;s/^40$/x/g;s/^41$/y/g;s/^42$/z/g;s/^08$/0/g;s/^09$/1/g;s/^0a$/2/g;s/^0b$/3/g;s/^0c$/4/g;s/^0d$/5/g;s/^0e$/6/g;s/^0f$/7/g;s/^00$/8/g;s/^01$/9/g;s/^15$/-/g;s/^16$/./g;s/^67$/_/g;s/^46$/~/g;s/^02$/:/g;s/^17$/\//g;s/^07$/?/g;s/^1b$/#/g;s/^63$/\[/g;s/^65$/\]/g;s/^78$/@/g;s/^19$/!/g;s/^1c$/$/g;s/^1e$/&/g;s/^10$/\(/g;s/^11$/\)/g;s/^12$/*/g;s/^13$/+/g;s/^14$/,/g;s/^03$/;/g;s/^05$/=/g;s/^1d$/%/g'
++ tr -d '\n'
++ tr -d '\n'
++ tr -d '\n'
++ tr -d '\n'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
++ sed 's/\/clock/\/clock\.json/'
+ provider_id=https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
+ '[' -n https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1 ']'
+ get_links https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
++ curl -e https://allmanga.to -s https://allanime.dayhttps://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1 -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c2429756f7263752967686f6b63342967686f6b63296451716e626472403e484767777148717e593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6451716e626472403e484767777148717e593759757364247b'
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c2429756f7263752967686f6b63342967686f6b63296451716e626472403e484767777148717e593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6451716e626472403e484767777148717e593759757364247b' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c2429756f7263752967686f6b63342967686f6b63296451716e626472403e484767777148717e593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6451716e626472403e484767777148717e593759757364247b'
+ provider_id=
+ '[' -n '' ']'
++ curl -e https://allmanga.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c2429756f7263752967686f6b63342967686f6b63296451716e626472403e484767777148717e593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6451716e626472403e484767777148717e593759757364247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ provider_id='/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e2972696d7f692b6b6761686f727362632b3e362b3235363963763b3f35373f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c247b'
+ '[' -n '/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e2972696d7f692b6b6761686f727362632b3e362b3235363963763b3f35373f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c247b' ']'
+ get_links '/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e2972696d7f692b6b6761686f727362632b3e362b3235363963763b3f35373f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c247b'
++ curl -e https://allmanga.to -s 'https://allanime.day/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e2972696d7f692b6b6761686f727362632b3e362b3235363963763b3f35373f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c247b' -A 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
+ response=
++ printf %s ''
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link=
+ case "$episode_link" in
+ '[' -n '' ']'
+ printf %s https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
+ grep -q tools.fast4speed.rsvp
+ printf '%s\n' 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ printf '\033[1;32m%s\033[0m Links Fetched\n' youtube
[1;32myoutube[0m Links Fetched
+ response='{"links":[{"link":"https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA","mp4":true,"resolutionStr":"Mp4","src":"https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA","fromCache":"2025-07-25T19:01:22.885Z"}]}'
++ printf %s '{"links":[{"link":"https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA","mp4":true,"resolutionStr":"Mp4","src":"https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA","fromCache":"2025-07-25T19:01:22.885Z"}]}'
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link='Mp4 >https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA'
+ case "$episode_link" in
+ '[' -n 'Mp4 >https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA' ']'
+ printf '%s\n' 'Mp4 >https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA'
+ printf %s '/apivtwo/clock.json?id=7d2473746a243c2429756f7263752967686f6b63342967686f6b63296451716e626472403e484767777148717e593759757364286b7632242a2475727463676b63744f62243c24556e67746376696f6872242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c242a2472746768756a67726f6968527f7663243c24757364242a246d637f243c2463762b6451716e626472403e484767777148717e593759757364247b'
+ grep -q tools.fast4speed.rsvp
+ printf '\033[1;32m%s\033[0m Links Fetched\n' sharepoint
[1;32msharepoint[0m Links Fetched
+ response=error
++ printf %s error
++ sed 's|},{|\
|g'
++ sed -nE 's|.*link":"([^"]*)".*"resolutionStr":"([^"]*)".*|\2 >\1|p;s|.*hls","url":"([^"]*)".*"hardsub_lang":"en-US".*|\1|p'
+ episode_link=
+ case "$episode_link" in
+ '[' -n '' ']'
+ printf %s '/apivtwo/clock.json?id=7d2473746a243c247573642b7a2b716772656e2972696d7f692b6b6761686f727362632b3e362b3235363963763b3f35373f242a2475727463676b63744f62243c2468756374706374242a2462677263243c24343634332b36312b343352373f3c36373c3636283636365c247b'
+ grep -q tools.fast4speed.rsvp
+ printf '\033[1;32m%s\033[0m Links Fetched\n' hianime
[1;32mhianime[0m Links Fetched
++ cat /tmp/tmp.72OheSkoWs/1 /tmp/tmp.72OheSkoWs/2 /tmp/tmp.72OheSkoWs/3 /tmp/tmp.72OheSkoWs/4
++ sort -g -r -s
+ links='Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
Mp4 >https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA'
+ rm -r /tmp/tmp.72OheSkoWs
+ select_quality 1080p
+ printf %s mpv.exe
+ cut -f1 '-d '
+ grep -qE '(android|iSH|vlc)'
+ printf %s mpv.exe
+ cut -f1 '-d '
+ grep -qE '(android|iSH)'
+ case "$1" in
++ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
Mp4 >https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA'
++ grep -m 1 1080p
+ result=
+ '[' -z '' ']'
+ printf 'Specified quality not found, defaulting to best\n'
Specified quality not found, defaulting to best
++ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
Mp4 >https://myanime.sharepoint.com/sites/anime2/_layouts/15/download.aspx?share=EXhEmlPjuRNJn-cKfQOpz9sB9mg4BAhNDa4XgbY8TsrETA'
++ head -n1
+ result='Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ grep -q 'cc>'
+ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ grep -q 'cc>'
+ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ grep -q tools.fast4speed.rsvp
+ refr_flag=--referrer=https://allmanga.to
+ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ grep -qE '(cc>|tools.fast4speed.rsvp)'
+ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
+ grep -q 'cc>'
+ unset subs_flag
++ printf %s 'Yt >https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1'
++ cut '-d>' -f2
+ episode=https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
+ printf %s '1
2
3
4
5
6
7
8
9
10
11'
+ grep -q '^1$'
+ '[' -z https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1 ']'
+ case "$player_function" in
+ '[' 0 = 0 ']'
+ replay=https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1
+ unset episode
+ update_history
+ grep -q -- bWwhdbtF8NAaqwNwx /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ nohup mpv.exe '--force-media-title=Tokyo Magnitude 80 Episode 1' https://tools.fast4speed.rsvp//media6/videos/bWwhdbtF8NAaqwNwx/sub/1 --referrer=https://allmanga.to
+ sed -E 's|^[^	]+	bWwhdbtF8NAaqwNwx	[^	]+$|1	bWwhdbtF8NAaqwNwx	Tokyo Magnitude 8.0 (11 episodes)|' /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ mv /c/Users/<USER>/.local/state/ani-cli/ani-hsts.new /c/Users/<USER>/.local/state/ani-cli/ani-hsts
+ '[' 0 = 1 ']'
+ '[' mpv.exe '!=' debug ']'
+ '[' mpv.exe '!=' download ']'
+ tput rc
8+ tput ed
[J+ '[' mpv.exe = download ']'
+ '[' mpv.exe = debug ']'
++ printf 'next\nreplay\nprevious\nselect\nchange_quality\nquit'
++ nth 'Playing episode 1 of Tokyo Magnitude 8.0 (11 episodes)... '
+++ cat -
++ stdin='next
replay
previous
select
change_quality
quit'
++ '[' -z 'next
replay
previous
select
change_quality
quit' ']'
+++ printf '%s\n' 'next
replay
previous
select
change_quality
quit'
+++ wc -l
+++ tr -d '[:space:]'
++ line_count=6
++ '[' 6 -eq 1 ']'
++ prompt='Playing episode 1 of Tokyo Magnitude 8.0 (11 episodes)... '
++ multi_flag=
++ '[' 1 -ne 1 ']'
+++ printf %s 'next
replay
previous
select
change_quality
quit'
+++ cut -f1,3
+++ tr '\t' ' '
+++ launcher '' 'Playing episode 1 of Tokyo Magnitude 8.0 (11 episodes)... '
+++ '[' 0 = 0 ']'
+++ '[' -z '' ']'
+++ set -- +m 'Playing episode 1 of Tokyo Magnitude 8.0 (11 episodes)... '
+++ '[' 0 = 0 ']'
+++ fzf +m --reverse --cycle --prompt 'Playing episode 1 of Tokyo Magnitude 8.0 (11 episodes)... '
+++ cut -d ' ' -f 1
+++ '[' 0 = 1 ']'
++ line=quit
+++ printf %s quit
+++ head -n1
++ line_start=quit
+++ printf %s quit
+++ tail -n1
++ line_end=quit
++ '[' -n quit ']'
++ '[' quit = quit ']'
++ printf %s 'next
replay
previous
select
change_quality
quit'
++ grep -E '^quit($|[[:space:]])'
++ cut -f2,3
+ cmd=quit
+ case "$cmd" in
+ exit 0
